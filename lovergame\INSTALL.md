# Go环境安装指南

## Windows系统安装Go

### 方法一：官方下载（推荐）

1. **下载Go安装包**
   - 访问 https://golang.org/dl/
   - 下载适合Windows的安装包（如：go1.21.5.windows-amd64.msi）

2. **安装Go**
   - 双击下载的.msi文件
   - 按照安装向导完成安装（默认安装到 C:\Program Files\Go）

3. **验证安装**
   ```cmd
   go version
   ```

### 方法二：使用包管理器

**使用Chocolatey：**
```cmd
# 如果没有Chocolatey，先安装：
# 在管理员权限的PowerShell中运行：
# Set-ExecutionPolicy Bypass -Scope Process -Force; [System.Net.ServicePointManager]::SecurityProtocol = [System.Net.ServicePointManager]::SecurityProtocol -bor 3072; iex ((New-Object System.Net.WebClient).DownloadString('https://community.chocolatey.org/install.ps1'))

choco install golang
```

**使用Scoop：**
```cmd
# 如果没有Scoop，先安装：
# 在PowerShell中运行：
# Set-ExecutionPolicy RemoteSigned -Scope CurrentUser
# irm get.scoop.sh | iex

scoop install go
```

### 方法三：便携版本

1. 下载Go的zip压缩包版本
2. 解压到任意目录（如：D:\go）
3. 添加环境变量：
   - GOROOT: D:\go
   - PATH: 添加 D:\go\bin

## 环境变量配置（如果自动配置失败）

1. 右键"此电脑" → "属性" → "高级系统设置" → "环境变量"
2. 在"系统变量"中添加或修改：
   - `GOROOT`: Go安装目录（如：C:\Program Files\Go）
   - `GOPATH`: Go工作空间（如：C:\Users\<USER>\go）
   - `PATH`: 添加 %GOROOT%\bin 和 %GOPATH%\bin

3. 重新打开命令提示符，测试：
   ```cmd
   go version
   ```

## 运行项目

安装Go后，在项目目录中运行：

```cmd
# 进入项目目录
cd D:\source\windows\lovergame

# 下载依赖
go mod tidy

# 运行项目
go run main.go
```

## 常见问题

### 1. 'go' 不是内部或外部命令
- 检查Go是否正确安装
- 检查环境变量PATH是否包含Go的bin目录
- 重启命令提示符或重启电脑

### 2. 网络问题（国内用户）
如果下载依赖时遇到网络问题，可以设置代理：

```cmd
# 设置Go模块代理（推荐使用国内镜像）
go env -w GOPROXY=https://goproxy.cn,direct
go env -w GOSUMDB=off

# 或者使用七牛云镜像
go env -w GOPROXY=https://goproxy.io,direct
```

### 3. 模块下载失败
```cmd
# 清理模块缓存
go clean -modcache

# 重新下载
go mod tidy
```

## 验证安装

安装完成后，运行以下命令验证：

```cmd
go version
go env GOROOT
go env GOPATH
```

应该看到类似输出：
```
go version go1.21.5 windows/amd64
C:\Program Files\Go
C:\Users\<USER>\go
```