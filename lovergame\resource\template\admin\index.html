<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{.title}}</title>
    <link rel="icon" href="data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 viewBox=%220 0 100 100%22><text y=%22.9em%22 font-size=%2290%22>⚙️</text></svg>">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css">
    <style>
        body {
            background-color: #f8f9fa;
        }
        .sidebar {
            min-height: 100vh;
            background: linear-gradient(180deg, #667eea 0%, #764ba2 100%);
        }
        .sidebar .nav-link {
            color: rgba(255,255,255,0.8);
            padding: 12px 20px;
            margin: 5px 10px;
            border-radius: 8px;
            transition: all 0.3s;
        }
        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            color: white;
            background-color: rgba(255,255,255,0.2);
        }
        .main-content {
            padding: 20px;
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .table th {
            border-top: none;
            background-color: #f8f9fa;
            font-weight: 600;
        }
        .btn-circle {
            width: 35px;
            height: 35px;
            border-radius: 50%;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            padding: 0;
        }
        .media-preview {
            max-width: 60px;
            max-height: 60px;
            border-radius: 5px;
        }
        .type-badge {
            font-size: 0.75rem;
        }
        .level-badge {
            font-size: 0.75rem;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- 侧边栏 -->
            <nav class="col-md-3 col-lg-2 d-md-block sidebar collapse">
                <div class="position-sticky pt-3">
                    <div class="text-center py-3">
                        <h4 class="text-white">🎮 游戏管理</h4>
                    </div>
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link active" href="#" onclick="showSection('punishment-reward')">
                                <i class="bi bi-gift"></i> 奖惩内容管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" onclick="showSection('slot-symbols')">
                                <i class="bi bi-emoji-smile"></i> 老虎机符号管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" onclick="showSection('slot-combinations')">
                                <i class="bi bi-puzzle"></i> 老虎机组合管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" onclick="showSection('truth-dare-questions')">
                                <i class="bi bi-chat-quote"></i> 真心话大冒险管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" onclick="showSection('dice-challenges')">
                                <i class="bi bi-dice-6"></i> 浪漫骰子管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" onclick="showSection('game-history')">
                                <i class="bi bi-clock-history"></i> 游戏记录
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/">
                                <i class="bi bi-house"></i> 返回游戏
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/admin/logout">
                                <i class="bi bi-box-arrow-right"></i> 退出登录
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- 主内容区 -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 main-content">
                <!-- 奖惩内容管理 -->
                <div id="punishment-reward-section" class="content-section">
                    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                        <h2>🎁 奖惩内容管理</h2>
                        <button class="btn btn-primary" onclick="showAddModal()">
                            <i class="bi bi-plus-circle"></i> 添加内容
                        </button>
                    </div>

                    <!-- 筛选器 -->
                    <div class="card mb-3">
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-3">
                                    <select class="form-select" id="filterType" onchange="loadPunishmentRewards()">
                                        <option value="0">全部类型</option>
                                        <option value="1">奖励</option>
                                        <option value="2">惩罚</option>
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <button class="btn btn-outline-primary" onclick="loadPunishmentRewards()">
                                        <i class="bi bi-search"></i> 筛选
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 数据表格 -->
                    <div class="card">
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>ID</th>
                                            <th>标题</th>
                                            <th>内容</th>
                                            <th>类型</th>
                                            <th>级别</th>
                                            <th>媒体</th>
                                            <th>状态</th>
                                            <th>创建时间</th>
                                            <th>操作</th>
                                        </tr>
                                    </thead>
                                    <tbody id="punishment-reward-tbody">
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 游戏记录 -->
                <div id="game-history-section" class="content-section" style="display: none;">
                    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                        <h2>📊 游戏记录</h2>
                    </div>

                    <div class="card">
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>游戏时间</th>
                                            <th>玩家1</th>
                                            <th>玩家2</th>
                                            <th>被选中玩家</th>
                                            <th>内容标题</th>
                                            <th>游戏会话</th>
                                        </tr>
                                    </thead>
                                    <tbody id="game-history-tbody">
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 老虎机符号管理 -->
                <div id="slot-symbols-section" class="content-section" style="display: none;">
                    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                        <h2>🎰 老虎机符号管理</h2>
                        <button class="btn btn-primary" onclick="showAddSymbolModal()">
                            <i class="bi bi-plus-circle"></i> 添加符号
                        </button>
                    </div>

                    <div class="card">
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>ID</th>
                                            <th>符号</th>
                                            <th>名称</th>
                                            <th>显示名称</th>
                                            <th>颜色</th>
                                            <th>稀有度</th>
                                            <th>状态</th>
                                            <th>操作</th>
                                        </tr>
                                    </thead>
                                    <tbody id="slot-symbols-tbody">
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 老虎机组合管理 -->
                <div id="slot-combinations-section" class="content-section" style="display: none;">
                    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                        <h2>🎲 老虎机组合管理</h2>
                        <button class="btn btn-primary" onclick="showAddCombinationModal()">
                            <i class="bi bi-plus-circle"></i> 添加组合
                        </button>
                    </div>

                    <div class="card">
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>ID</th>
                                            <th>组合名称</th>
                                            <th>符号组合</th>
                                            <th>活动标题</th>
                                            <th>活动内容</th>
                                            <th>类型</th>
                                            <th>强度</th>
                                            <th>状态</th>
                                            <th>操作</th>
                                        </tr>
                                    </thead>
                                    <tbody id="slot-combinations-tbody">
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 真心话大冒险问题管理 -->
                <div id="truth-dare-questions-section" class="content-section" style="display: none;">
                    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                        <h2>💭 真心话大冒险问题管理</h2>
                        <button class="btn btn-primary" onclick="showAddTruthDareQuestionModal()">
                            <i class="bi bi-plus-circle"></i> 添加问题
                        </button>
                    </div>
                    <div class="row">
                        <div class="col-12">
                            <div class="table-responsive">
                                <table class="table table-striped table-hover">
                                    <thead class="table-dark">
                                        <tr>
                                            <th>ID</th>
                                            <th>类型</th>
                                            <th>标题</th>
                                            <th>内容</th>
                                            <th>难度</th>
                                            <th>分类</th>
                                            <th>状态</th>
                                            <th>操作</th>
                                        </tr>
                                    </thead>
                                    <tbody id="truth-dare-questions-tbody">
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 浪漫骰子挑战管理 -->
                <div id="dice-challenges-section" class="content-section" style="display: none;">
                    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                        <h2>🎲 浪漫骰子挑战管理</h2>
                        <button class="btn btn-primary" onclick="showAddDiceChallengeModal()">
                            <i class="bi bi-plus-circle"></i> 添加挑战
                        </button>
                    </div>
                    <div class="row">
                        <div class="col-12">
                            <div class="table-responsive">
                                <table class="table table-striped table-hover">
                                    <thead class="table-dark">
                                        <tr>
                                            <th>ID</th>
                                            <th>名称</th>
                                            <th>骰子组合</th>
                                            <th>活动标题</th>
                                            <th>难度</th>
                                            <th>时长(分钟)</th>
                                            <th>需要道具</th>
                                            <th>状态</th>
                                            <th>操作</th>
                                        </tr>
                                    </thead>
                                    <tbody id="dice-challenges-tbody">
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- 添加/编辑模态框 -->
    <div class="modal fade" id="addEditModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="modalTitle">添加奖惩内容</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="addEditForm">
                        <input type="hidden" id="itemId">
                        <div class="row">
                            <div class="col-md-6">
                                <label class="form-label">标题 *</label>
                                <input type="text" class="form-control" id="title" required>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">类型 *</label>
                                <select class="form-select" id="type" required>
                                    <option value="1">奖励</option>
                                    <option value="2">惩罚</option>
                                </select>
                            </div>
                        </div>
                        <div class="mt-3">
                            <label class="form-label">内容描述 *</label>
                            <textarea class="form-control" id="content" rows="3" required></textarea>
                        </div>
                        <div class="row mt-3">
                            <div class="col-md-6">
                                <label class="form-label">级别</label>
                                <select class="form-select" id="level">
                                    <option value="1">轻微</option>
                                    <option value="2">一般</option>
                                    <option value="3">严重</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">媒体类型</label>
                                <select class="form-select" id="mediaType" onchange="toggleMediaUpload()">
                                    <option value="0">无媒体</option>
                                    <option value="1">图片</option>
                                    <option value="2">音频</option>
                                    <option value="3">视频</option>
                                </select>
                            </div>
                        </div>
                        <div class="mt-3" id="mediaUploadDiv" style="display: none;">
                            <label class="form-label">上传媒体文件</label>
                            <input type="file" class="form-control" id="mediaFile" onchange="uploadMedia()">
                            <input type="hidden" id="mediaUrl">
                            <div id="mediaPreview" class="mt-2"></div>
                        </div>
                        <div class="mt-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="isActive" checked>
                                <label class="form-check-label">启用</label>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="saveItem()">保存</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let currentPage = 1;
        let currentEditId = null;

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadPunishmentRewards();
        });

        function showSection(section) {
            // 隐藏所有内容区域
            document.querySelectorAll('.content-section').forEach(el => {
                el.style.display = 'none';
            });
            
            // 移除所有导航激活状态
            document.querySelectorAll('.nav-link').forEach(el => {
                el.classList.remove('active');
            });
            
            // 显示对应内容区域
            if (section === 'punishment-reward') {
                document.getElementById('punishment-reward-section').style.display = 'block';
                loadPunishmentRewards();
            } else if (section === 'game-history') {
                document.getElementById('game-history-section').style.display = 'block';
                loadGameHistory();
            } else if (section === 'slot-symbols') {
                document.getElementById('slot-symbols-section').style.display = 'block';
                loadSlotSymbols();
            } else if (section === 'slot-combinations') {
                document.getElementById('slot-combinations-section').style.display = 'block';
                loadSlotCombinations();
            } else if (section === 'truth-dare-questions') {
                document.getElementById('truth-dare-questions-section').style.display = 'block';
                loadTruthDareQuestions();
            } else if (section === 'dice-challenges') {
                document.getElementById('dice-challenges-section').style.display = 'block';
                loadDiceChallenges();
            }
            
            // 激活对应导航
            event.target.classList.add('active');
        }

        function loadPunishmentRewards() {
            const type = document.getElementById('filterType').value;
            
            fetch(`/admin/punishment-reward?type=${type}&page=${currentPage}&limit=50`)
                .then(response => response.json())
                .then(data => {
                    if (data.code === 0) {
                        renderPunishmentRewards(data.data);
                    } else {
                        alert('加载失败：' + data.msg);
                    }
                });
        }

        function renderPunishmentRewards(items) {
            const tbody = document.getElementById('punishment-reward-tbody');
            tbody.innerHTML = '';
            
            items.forEach(item => {
                const typeText = item.type === 1 ? '<span class="badge bg-success type-badge">奖励</span>' : '<span class="badge bg-warning type-badge">惩罚</span>';
                const levelText = ['', '轻微', '一般', '严重'][item.level];
                const statusText = item.isActive ? '<span class="badge bg-success">启用</span>' : '<span class="badge bg-secondary">禁用</span>';
                const mediaPreview = item.mediaUrl ? `<img src="${item.mediaUrl}" class="media-preview" alt="预览">` : '-';
                
                const row = `
                    <tr>
                        <td>${item.id}</td>
                        <td>${item.title}</td>
                        <td>${item.content.length > 30 ? item.content.substring(0, 30) + '...' : item.content}</td>
                        <td>${typeText}</td>
                        <td><span class="badge bg-info level-badge">${levelText}</span></td>
                        <td>${mediaPreview}</td>
                        <td>${statusText}</td>
                        <td>${new Date(item.createdAt).toLocaleString()}</td>
                        <td>
                            <button class="btn btn-outline-primary btn-circle btn-sm me-1" onclick="editItem(${item.id})" title="编辑">
                                <i class="bi bi-pencil"></i>
                            </button>
                            <button class="btn btn-outline-danger btn-circle btn-sm" onclick="deleteItem(${item.id})" title="删除">
                                <i class="bi bi-trash"></i>
                            </button>
                        </td>
                    </tr>
                `;
                tbody.innerHTML += row;
            });
        }

        function showAddModal() {
            currentEditId = null;
            document.getElementById('modalTitle').textContent = '添加奖惩内容';
            document.getElementById('addEditForm').reset();
            document.getElementById('itemId').value = '';
            document.getElementById('isActive').checked = true;
            toggleMediaUpload();
            new bootstrap.Modal(document.getElementById('addEditModal')).show();
        }

        function editItem(id) {
            currentEditId = id;
            document.getElementById('modalTitle').textContent = '编辑奖惩内容';

            // 加载项目数据
            fetch(`/admin/punishment-reward/edit/${id}`, {
                headers: {
                    'Accept': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
                .then(response => response.json())
                .then(data => {
                    if (data.code === 0) {
                        const item = data.data;
                        document.getElementById('itemId').value = item.id;
                        document.getElementById('title').value = item.title;
                        document.getElementById('content').value = item.content;
                        document.getElementById('type').value = item.type;
                        document.getElementById('level').value = item.level;
                        document.getElementById('mediaType').value = item.mediaType;
                        document.getElementById('mediaUrl').value = item.mediaUrl || '';
                        document.getElementById('isActive').checked = item.isActive === 1;
                        
                        toggleMediaUpload();
                        if (item.mediaUrl) {
                            showMediaPreview(item.mediaUrl, item.mediaType);
                        }
                        
                        new bootstrap.Modal(document.getElementById('addEditModal')).show();
                    } else {
                        alert('加载失败：' + data.msg);
                    }
                });
        }

        function deleteItem(id) {
            if (confirm('确定要删除这个内容吗？')) {
                fetch(`/admin/punishment-reward/delete/${id}`, {
                    method: 'DELETE'
                })
                .then(response => response.json())
                .then(data => {
                    if (data.code === 0) {
                        alert('删除成功');
                        loadPunishmentRewards();
                    } else {
                        alert('删除失败：' + data.msg);
                    }
                });
            }
        }

        function toggleMediaUpload() {
            const mediaType = document.getElementById('mediaType').value;
            const uploadDiv = document.getElementById('mediaUploadDiv');
            uploadDiv.style.display = mediaType > 0 ? 'block' : 'none';
        }

        function uploadMedia() {
            const fileInput = document.getElementById('mediaFile');
            if (!fileInput.files[0]) return;
            
            const formData = new FormData();
            formData.append('file', fileInput.files[0]);
            
            fetch('/admin/upload', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.code === 0) {
                    document.getElementById('mediaUrl').value = data.data.url;
                    showMediaPreview(data.data.url, document.getElementById('mediaType').value);
                } else {
                    alert('上传失败：' + data.msg);
                }
            });
        }

        function showMediaPreview(url, mediaType) {
            const preview = document.getElementById('mediaPreview');
            let content = '';
            
            switch (parseInt(mediaType)) {
                case 1:
                    content = `<img src="${url}" class="img-thumbnail" style="max-width: 200px;">`;
                    break;
                case 2:
                    content = `<audio controls><source src="${url}" type="audio/mpeg"></audio>`;
                    break;
                case 3:
                    content = `<video controls style="max-width: 200px;"><source src="${url}" type="video/mp4"></video>`;
                    break;
            }
            
            preview.innerHTML = content;
        }

        function saveItem() {
            const form = document.getElementById('addEditForm');
            if (!form.checkValidity()) {
                form.reportValidity();
                return;
            }
            
            const data = {
                title: document.getElementById('title').value,
                content: document.getElementById('content').value,
                type: parseInt(document.getElementById('type').value),
                level: parseInt(document.getElementById('level').value),
                mediaType: parseInt(document.getElementById('mediaType').value),
                mediaUrl: document.getElementById('mediaUrl').value,
                isActive: document.getElementById('isActive').checked ? 1 : 0
            };
            
            const url = currentEditId ? `/admin/punishment-reward/edit/${currentEditId}` : '/admin/punishment-reward/add';
            const method = 'POST';
            
            fetch(url, {
                method: method,
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(data)
            })
            .then(response => response.json())
            .then(data => {
                if (data.code === 0) {
                    alert(currentEditId ? '更新成功' : '添加成功');
                    bootstrap.Modal.getInstance(document.getElementById('addEditModal')).hide();
                    loadPunishmentRewards();
                } else {
                    alert('保存失败：' + data.msg);
                }
            });
        }

        function loadGameHistory() {
            fetch('/game/history?page=1&limit=50')
                .then(response => response.json())
                .then(data => {
                    if (data.code === 0) {
                        renderGameHistory(data.data);
                    } else {
                        alert('加载失败：' + data.msg);
                    }
                });
        }

        function renderGameHistory(records) {
            const tbody = document.getElementById('game-history-tbody');
            tbody.innerHTML = '';
            
            records.forEach(record => {
                const selectedPlayerName = record.selectedPlayer === 1 ? record.player1Name : record.player2Name;
                
                const row = `
                    <tr>
                        <td>${new Date(record.createdAt).toLocaleString()}</td>
                        <td>${record.player1Name}</td>
                        <td>${record.player2Name}</td>
                        <td><strong>${selectedPlayerName}</strong></td>
                        <td>-</td>
                        <td><small class="text-muted">${record.gameSession}</small></td>
                    </tr>
                `;
                tbody.innerHTML += row;
            });
        }

        // 老虎机符号管理函数
        function loadSlotSymbols() {
            fetch('/admin/slot-symbols')
                .then(response => response.json())
                .then(data => {
                    if (data.code === 0) {
                        renderSlotSymbols(data.data);
                    } else {
                        alert('加载失败：' + data.msg);
                    }
                });
        }

        function renderSlotSymbols(symbols) {
            const tbody = document.getElementById('slot-symbols-tbody');
            tbody.innerHTML = '';

            symbols.forEach(symbol => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${symbol.id}</td>
                    <td style="font-size: 24px;">${symbol.emoji}</td>
                    <td>${symbol.name}</td>
                    <td>${symbol.displayName}</td>
                    <td><span class="badge" style="background-color: ${symbol.color}; color: white;">${symbol.color}</span></td>
                    <td><span class="badge bg-info">${['', '常见', '普通', '稀有', '史诗', '传说'][symbol.rarity]}</span></td>
                    <td><span class="badge ${symbol.isActive ? 'bg-success' : 'bg-secondary'}">${symbol.isActive ? '启用' : '禁用'}</span></td>
                    <td>
                        <button class="btn btn-sm btn-outline-primary" onclick="editSymbol(${symbol.id})">编辑</button>
                        <button class="btn btn-sm btn-outline-danger" onclick="deleteSymbol(${symbol.id})">删除</button>
                    </td>
                `;
                tbody.appendChild(row);
            });
        }

        function showAddSymbolModal() {
            showSymbolModal(0, '添加符号');
        }

        function editSymbol(id) {
            showSymbolModal(id, '编辑符号');
        }

        function showSymbolModal(id, title) {
            const url = id > 0 ? `/admin/slot-symbols/edit/${id}` : '/admin/slot-symbols/add';

            fetch(url)
            .then(response => response.json())
            .then(data => {
                if (data.code === 0) {
                    const symbol = data.data;
                    const modalHtml = `
                        <div class="modal fade" id="symbolModal" tabindex="-1">
                            <div class="modal-dialog">
                                <div class="modal-content">
                                    <div class="modal-header">
                                        <h5 class="modal-title">${title}</h5>
                                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                    </div>
                                    <div class="modal-body">
                                        <form id="symbolForm">
                                            <div class="mb-3">
                                                <label for="symbolName" class="form-label">符号名称</label>
                                                <input type="text" class="form-control" id="symbolName" value="${symbol.name || ''}" required>
                                            </div>
                                            <div class="mb-3">
                                                <label for="symbolDisplayName" class="form-label">显示名称</label>
                                                <input type="text" class="form-control" id="symbolDisplayName" value="${symbol.displayName || ''}" required>
                                            </div>
                                            <div class="mb-3">
                                                <label for="symbolEmoji" class="form-label">表情符号</label>
                                                <input type="text" class="form-control" id="symbolEmoji" value="${symbol.emoji || ''}" required>
                                            </div>
                                            <div class="mb-3">
                                                <label for="symbolColor" class="form-label">颜色</label>
                                                <input type="color" class="form-control" id="symbolColor" value="${symbol.color || '#FF6B6B'}">
                                            </div>
                                            <div class="mb-3">
                                                <label for="symbolRarity" class="form-label">稀有度</label>
                                                <select class="form-select" id="symbolRarity">
                                                    <option value="1" ${symbol.rarity === 1 ? 'selected' : ''}>常见</option>
                                                    <option value="2" ${symbol.rarity === 2 ? 'selected' : ''}>普通</option>
                                                    <option value="3" ${symbol.rarity === 3 ? 'selected' : ''}>稀有</option>
                                                    <option value="4" ${symbol.rarity === 4 ? 'selected' : ''}>史诗</option>
                                                    <option value="5" ${symbol.rarity === 5 ? 'selected' : ''}>传说</option>
                                                </select>
                                            </div>
                                            <div class="mb-3">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" id="symbolIsActive" ${symbol.isActive ? 'checked' : ''}>
                                                    <label class="form-check-label" for="symbolIsActive">启用</label>
                                                </div>
                                            </div>
                                        </form>
                                    </div>
                                    <div class="modal-footer">
                                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                                        <button type="button" class="btn btn-primary" onclick="saveSymbol(${id})">保存</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    `;

                    // 移除旧的模态框
                    const oldModal = document.getElementById('symbolModal');
                    if (oldModal) {
                        oldModal.remove();
                    }

                    // 添加新的模态框
                    document.body.insertAdjacentHTML('beforeend', modalHtml);

                    // 显示模态框
                    const modal = new bootstrap.Modal(document.getElementById('symbolModal'));
                    modal.show();
                } else {
                    alert('加载失败：' + data.msg);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('请求失败：' + error.message);
            });
        }

        function saveSymbol(id) {
            const formData = {
                name: document.getElementById('symbolName').value,
                displayName: document.getElementById('symbolDisplayName').value,
                emoji: document.getElementById('symbolEmoji').value,
                color: document.getElementById('symbolColor').value,
                rarity: parseInt(document.getElementById('symbolRarity').value),
                isActive: document.getElementById('symbolIsActive').checked ? 1 : 0
            };

            const url = id > 0 ? `/admin/slot-symbols/edit/${id}` : '/admin/slot-symbols/add';

            fetch(url, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(formData)
            })
            .then(response => response.json())
            .then(data => {
                if (data.code === 0) {
                    alert('保存成功');
                    // 关闭模态框
                    const modal = bootstrap.Modal.getInstance(document.getElementById('symbolModal'));
                    modal.hide();
                    // 重新加载数据
                    loadSlotSymbols();
                } else {
                    alert('保存失败：' + data.msg);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('保存失败：' + error.message);
            });
        }

        function deleteSymbol(id) {
            if (confirm('确定要删除这个符号吗？')) {
                fetch(`/admin/slot-symbols/delete/${id}`, {
                    method: 'DELETE'
                })
                .then(response => response.json())
                .then(data => {
                    if (data.code === 0) {
                        alert('删除成功');
                        loadSlotSymbols();
                    } else {
                        alert('删除失败：' + data.msg);
                    }
                });
            }
        }

        // 老虎机组合管理函数
        function loadSlotCombinations() {
            fetch('/admin/slot-combinations')
                .then(response => response.json())
                .then(data => {
                    if (data.code === 0) {
                        renderSlotCombinations(data.data);
                    } else {
                        alert('加载失败：' + data.msg);
                    }
                });
        }

        function renderSlotCombinations(combinations) {
            const tbody = document.getElementById('slot-combinations-tbody');
            tbody.innerHTML = '';

            combinations.forEach(combination => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${combination.id}</td>
                    <td>${combination.name}</td>
                    <td>
                        <span class="badge bg-light text-dark">符号1: ${combination.symbol1Id}</span>
                        <span class="badge bg-light text-dark">符号2: ${combination.symbol2Id}</span>
                        <span class="badge bg-light text-dark">符号3: ${combination.symbol3Id}</span>
                    </td>
                    <td>${combination.activityTitle}</td>
                    <td style="max-width: 200px; overflow: hidden; text-overflow: ellipsis;">${combination.activityContent}</td>
                    <td><span class="badge ${combination.activityType === 1 ? 'bg-success' : 'bg-warning'}">${combination.activityType === 1 ? '浪漫活动' : '挑战活动'}</span></td>
                    <td><span class="badge bg-info">${['', '轻松', '中等', '激烈'][combination.intensityLevel]}</span></td>
                    <td><span class="badge ${combination.isActive ? 'bg-success' : 'bg-secondary'}">${combination.isActive ? '启用' : '禁用'}</span></td>
                    <td>
                        <button class="btn btn-sm btn-outline-primary" onclick="editCombination(${combination.id})">编辑</button>
                        <button class="btn btn-sm btn-outline-danger" onclick="deleteCombination(${combination.id})">删除</button>
                    </td>
                `;
                tbody.appendChild(row);
            });
        }

        function showAddCombinationModal() {
            showCombinationModal(0, '添加组合');
        }

        function editCombination(id) {
            showCombinationModal(id, '编辑组合');
        }

        function showCombinationModal(id, title) {
            const url = id > 0 ? `/admin/slot-combinations/edit/${id}` : '/admin/slot-combinations/add';

            fetch(url)
            .then(response => response.json())
            .then(data => {
                if (data.code === 0) {
                    const combination = data.data.combination || data.data;
                    const symbols = data.data.symbols || [];

                    const symbolOptions = symbols.map(s =>
                        `<option value="${s.id}">${s.emoji} ${s.displayName}</option>`
                    ).join('');

                    const modalHtml = `
                        <div class="modal fade" id="combinationModal" tabindex="-1">
                            <div class="modal-dialog modal-lg">
                                <div class="modal-content">
                                    <div class="modal-header">
                                        <h5 class="modal-title">${title}</h5>
                                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                    </div>
                                    <div class="modal-body">
                                        <form id="combinationForm">
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <div class="mb-3">
                                                        <label for="combinationName" class="form-label">组合名称</label>
                                                        <input type="text" class="form-control" id="combinationName" value="${combination.name || ''}" required>
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="mb-3">
                                                        <label for="activityTitle" class="form-label">活动标题</label>
                                                        <input type="text" class="form-control" id="activityTitle" value="${combination.activityTitle || ''}" required>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="mb-3">
                                                <label for="activityContent" class="form-label">活动内容</label>
                                                <textarea class="form-control" id="activityContent" rows="3" required>${combination.activityContent || ''}</textarea>
                                            </div>
                                            <div class="row">
                                                <div class="col-md-4">
                                                    <div class="mb-3">
                                                        <label for="symbol1Id" class="form-label">符号1</label>
                                                        <select class="form-select" id="symbol1Id" required>
                                                            ${symbolOptions}
                                                        </select>
                                                    </div>
                                                </div>
                                                <div class="col-md-4">
                                                    <div class="mb-3">
                                                        <label for="symbol2Id" class="form-label">符号2</label>
                                                        <select class="form-select" id="symbol2Id" required>
                                                            ${symbolOptions}
                                                        </select>
                                                    </div>
                                                </div>
                                                <div class="col-md-4">
                                                    <div class="mb-3">
                                                        <label for="symbol3Id" class="form-label">符号3</label>
                                                        <select class="form-select" id="symbol3Id" required>
                                                            ${symbolOptions}
                                                        </select>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="row">
                                                <div class="col-md-4">
                                                    <div class="mb-3">
                                                        <label for="activityType" class="form-label">活动类型</label>
                                                        <select class="form-select" id="activityType">
                                                            <option value="1" ${combination.activityType === 1 ? 'selected' : ''}>浪漫活动</option>
                                                            <option value="2" ${combination.activityType === 2 ? 'selected' : ''}>挑战活动</option>
                                                        </select>
                                                    </div>
                                                </div>
                                                <div class="col-md-4">
                                                    <div class="mb-3">
                                                        <label for="intensityLevel" class="form-label">强度等级</label>
                                                        <select class="form-select" id="intensityLevel">
                                                            <option value="1" ${combination.intensityLevel === 1 ? 'selected' : ''}>轻松</option>
                                                            <option value="2" ${combination.intensityLevel === 2 ? 'selected' : ''}>中等</option>
                                                            <option value="3" ${combination.intensityLevel === 3 ? 'selected' : ''}>激烈</option>
                                                        </select>
                                                    </div>
                                                </div>
                                                <div class="col-md-4">
                                                    <div class="mb-3">
                                                        <div class="form-check mt-4">
                                                            <input class="form-check-input" type="checkbox" id="combinationIsActive" ${combination.isActive ? 'checked' : ''}>
                                                            <label class="form-check-label" for="combinationIsActive">启用</label>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </form>
                                    </div>
                                    <div class="modal-footer">
                                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                                        <button type="button" class="btn btn-primary" onclick="saveCombination(${id})">保存</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    `;

                    // 移除旧的模态框
                    const oldModal = document.getElementById('combinationModal');
                    if (oldModal) {
                        oldModal.remove();
                    }

                    // 添加新的模态框
                    document.body.insertAdjacentHTML('beforeend', modalHtml);

                    // 设置选中的符号
                    if (combination.symbol1Id) document.getElementById('symbol1Id').value = combination.symbol1Id;
                    if (combination.symbol2Id) document.getElementById('symbol2Id').value = combination.symbol2Id;
                    if (combination.symbol3Id) document.getElementById('symbol3Id').value = combination.symbol3Id;

                    // 显示模态框
                    const modal = new bootstrap.Modal(document.getElementById('combinationModal'));
                    modal.show();
                } else {
                    alert('加载失败：' + data.msg);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('请求失败：' + error.message);
            });
        }

        function saveCombination(id) {
            const formData = {
                name: document.getElementById('combinationName').value,
                symbol1Id: parseInt(document.getElementById('symbol1Id').value),
                symbol2Id: parseInt(document.getElementById('symbol2Id').value),
                symbol3Id: parseInt(document.getElementById('symbol3Id').value),
                activityTitle: document.getElementById('activityTitle').value,
                activityContent: document.getElementById('activityContent').value,
                activityType: parseInt(document.getElementById('activityType').value),
                intensityLevel: parseInt(document.getElementById('intensityLevel').value),
                mediaType: 0,
                mediaUrl: '',
                isActive: document.getElementById('combinationIsActive').checked ? 1 : 0
            };

            const url = id > 0 ? `/admin/slot-combinations/edit/${id}` : '/admin/slot-combinations/add';

            fetch(url, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(formData)
            })
            .then(response => response.json())
            .then(data => {
                if (data.code === 0) {
                    alert('保存成功');
                    // 关闭模态框
                    const modal = bootstrap.Modal.getInstance(document.getElementById('combinationModal'));
                    modal.hide();
                    // 重新加载数据
                    loadSlotCombinations();
                } else {
                    alert('保存失败：' + data.msg);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('保存失败：' + error.message);
            });
        }

        // 真心话大冒险问题管理
        function loadTruthDareQuestions() {
            fetch('/admin/truth-dare-questions')
            .then(response => response.json())
            .then(data => {
                if (data.code === 0) {
                    const tbody = document.getElementById('truth-dare-questions-tbody');
                    tbody.innerHTML = '';

                    data.data.list.forEach(question => {
                        const typeText = question.type === 1 ? '真心话' : '大冒险';
                        const difficultyText = ['', '简单', '中等', '困难'][question.difficulty];
                        const statusText = question.isActive ? '启用' : '禁用';
                        const statusClass = question.isActive ? 'text-success' : 'text-danger';

                        const row = `
                            <tr>
                                <td>${question.id}</td>
                                <td><span class="badge ${question.type === 1 ? 'bg-info' : 'bg-warning'}">${typeText}</span></td>
                                <td>${question.title}</td>
                                <td>${question.content.length > 50 ? question.content.substring(0, 50) + '...' : question.content}</td>
                                <td><span class="badge bg-secondary">${difficultyText}</span></td>
                                <td><span class="badge bg-light text-dark">${question.category}</span></td>
                                <td><span class="${statusClass}">${statusText}</span></td>
                                <td>
                                    <button class="btn btn-sm btn-outline-primary me-1" onclick="editTruthDareQuestion(${question.id})">
                                        <i class="bi bi-pencil"></i>
                                    </button>
                                    <button class="btn btn-sm btn-outline-danger" onclick="deleteTruthDareQuestion(${question.id})">
                                        <i class="bi bi-trash"></i>
                                    </button>
                                </td>
                            </tr>
                        `;
                        tbody.innerHTML += row;
                    });
                } else {
                    alert('加载失败：' + data.msg);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('请求失败：' + error.message);
            });
        }

        function loadDiceChallenges() {
            fetch('/admin/dice-challenges')
            .then(response => response.json())
            .then(data => {
                if (data.code === 0) {
                    const tbody = document.getElementById('dice-challenges-tbody');
                    tbody.innerHTML = '';

                    data.data.list.forEach(challenge => {
                        const difficultyText = ['', '简单', '中等', '困难'][challenge.difficulty];
                        const statusText = challenge.isActive ? '启用' : '禁用';
                        const statusClass = challenge.isActive ? 'text-success' : 'text-danger';
                        const propsText = challenge.requiresProps ? '是' : '否';

                        const row = `
                            <tr>
                                <td>${challenge.id}</td>
                                <td>${challenge.name}</td>
                                <td><code>${challenge.diceCombination}</code></td>
                                <td>${challenge.activityTitle}</td>
                                <td><span class="badge bg-secondary">${difficultyText}</span></td>
                                <td>${challenge.durationMinutes}</td>
                                <td><span class="badge ${challenge.requiresProps ? 'bg-warning' : 'bg-success'}">${propsText}</span></td>
                                <td><span class="${statusClass}">${statusText}</span></td>
                                <td>
                                    <button class="btn btn-sm btn-outline-primary me-1" onclick="editDiceChallenge(${challenge.id})">
                                        <i class="bi bi-pencil"></i>
                                    </button>
                                    <button class="btn btn-sm btn-outline-danger" onclick="deleteDiceChallenge(${challenge.id})">
                                        <i class="bi bi-trash"></i>
                                    </button>
                                </td>
                            </tr>
                        `;
                        tbody.innerHTML += row;
                    });
                } else {
                    alert('加载失败：' + data.msg);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('请求失败：' + error.message);
            });
        }

        function deleteCombination(id) {
            if (confirm('确定要删除这个组合吗？')) {
                fetch(`/admin/slot-combinations/delete/${id}`, {
                    method: 'DELETE'
                })
                .then(response => response.json())
                .then(data => {
                    if (data.code === 0) {
                        alert('删除成功');
                        loadSlotCombinations();
                    } else {
                        alert('删除失败：' + data.msg);
                    }
                });
            }
        }
    </script>
</body>
</html>