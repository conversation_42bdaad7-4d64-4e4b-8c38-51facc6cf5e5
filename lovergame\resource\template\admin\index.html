<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{.title}}</title>
    <link rel="icon" href="data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 viewBox=%220 0 100 100%22><text y=%22.9em%22 font-size=%2290%22>⚙️</text></svg>">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css">
    <style>
        body {
            background-color: #f8f9fa;
        }
        .sidebar {
            min-height: 100vh;
            background: linear-gradient(180deg, #667eea 0%, #764ba2 100%);
        }
        .sidebar .nav-link {
            color: rgba(255,255,255,0.8);
            padding: 12px 20px;
            margin: 5px 10px;
            border-radius: 8px;
            transition: all 0.3s;
        }
        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            color: white;
            background-color: rgba(255,255,255,0.2);
        }
        .main-content {
            padding: 20px;
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .table th {
            border-top: none;
            background-color: #f8f9fa;
            font-weight: 600;
        }
        .btn-circle {
            width: 35px;
            height: 35px;
            border-radius: 50%;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            padding: 0;
        }
        .media-preview {
            max-width: 60px;
            max-height: 60px;
            border-radius: 5px;
        }
        .type-badge {
            font-size: 0.75rem;
        }
        .level-badge {
            font-size: 0.75rem;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- 侧边栏 -->
            <nav class="col-md-3 col-lg-2 d-md-block sidebar collapse">
                <div class="position-sticky pt-3">
                    <div class="text-center py-3">
                        <h4 class="text-white">🎮 游戏管理</h4>
                    </div>
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link active" href="#" onclick="showSection('punishment-reward')">
                                <i class="bi bi-gift"></i> 奖惩内容管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" onclick="showSection('slot-symbols')">
                                <i class="bi bi-emoji-smile"></i> 老虎机符号管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" onclick="showSection('slot-combinations')">
                                <i class="bi bi-puzzle"></i> 老虎机组合管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" onclick="showSection('game-history')">
                                <i class="bi bi-clock-history"></i> 游戏记录
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/">
                                <i class="bi bi-house"></i> 返回游戏
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/admin/logout">
                                <i class="bi bi-box-arrow-right"></i> 退出登录
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- 主内容区 -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 main-content">
                <!-- 奖惩内容管理 -->
                <div id="punishment-reward-section" class="content-section">
                    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                        <h2>🎁 奖惩内容管理</h2>
                        <button class="btn btn-primary" onclick="showAddModal()">
                            <i class="bi bi-plus-circle"></i> 添加内容
                        </button>
                    </div>

                    <!-- 筛选器 -->
                    <div class="card mb-3">
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-3">
                                    <select class="form-select" id="filterType" onchange="loadPunishmentRewards()">
                                        <option value="0">全部类型</option>
                                        <option value="1">奖励</option>
                                        <option value="2">惩罚</option>
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <button class="btn btn-outline-primary" onclick="loadPunishmentRewards()">
                                        <i class="bi bi-search"></i> 筛选
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 数据表格 -->
                    <div class="card">
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>ID</th>
                                            <th>标题</th>
                                            <th>内容</th>
                                            <th>类型</th>
                                            <th>级别</th>
                                            <th>媒体</th>
                                            <th>状态</th>
                                            <th>创建时间</th>
                                            <th>操作</th>
                                        </tr>
                                    </thead>
                                    <tbody id="punishment-reward-tbody">
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 游戏记录 -->
                <div id="game-history-section" class="content-section" style="display: none;">
                    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                        <h2>📊 游戏记录</h2>
                    </div>

                    <div class="card">
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>游戏时间</th>
                                            <th>玩家1</th>
                                            <th>玩家2</th>
                                            <th>被选中玩家</th>
                                            <th>内容标题</th>
                                            <th>游戏会话</th>
                                        </tr>
                                    </thead>
                                    <tbody id="game-history-tbody">
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- 添加/编辑模态框 -->
    <div class="modal fade" id="addEditModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="modalTitle">添加奖惩内容</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="addEditForm">
                        <input type="hidden" id="itemId">
                        <div class="row">
                            <div class="col-md-6">
                                <label class="form-label">标题 *</label>
                                <input type="text" class="form-control" id="title" required>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">类型 *</label>
                                <select class="form-select" id="type" required>
                                    <option value="1">奖励</option>
                                    <option value="2">惩罚</option>
                                </select>
                            </div>
                        </div>
                        <div class="mt-3">
                            <label class="form-label">内容描述 *</label>
                            <textarea class="form-control" id="content" rows="3" required></textarea>
                        </div>
                        <div class="row mt-3">
                            <div class="col-md-6">
                                <label class="form-label">级别</label>
                                <select class="form-select" id="level">
                                    <option value="1">轻微</option>
                                    <option value="2">一般</option>
                                    <option value="3">严重</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">媒体类型</label>
                                <select class="form-select" id="mediaType" onchange="toggleMediaUpload()">
                                    <option value="0">无媒体</option>
                                    <option value="1">图片</option>
                                    <option value="2">音频</option>
                                    <option value="3">视频</option>
                                </select>
                            </div>
                        </div>
                        <div class="mt-3" id="mediaUploadDiv" style="display: none;">
                            <label class="form-label">上传媒体文件</label>
                            <input type="file" class="form-control" id="mediaFile" onchange="uploadMedia()">
                            <input type="hidden" id="mediaUrl">
                            <div id="mediaPreview" class="mt-2"></div>
                        </div>
                        <div class="mt-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="isActive" checked>
                                <label class="form-check-label">启用</label>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="saveItem()">保存</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let currentPage = 1;
        let currentEditId = null;

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadPunishmentRewards();
        });

        function showSection(section) {
            // 隐藏所有内容区域
            document.querySelectorAll('.content-section').forEach(el => {
                el.style.display = 'none';
            });
            
            // 移除所有导航激活状态
            document.querySelectorAll('.nav-link').forEach(el => {
                el.classList.remove('active');
            });
            
            // 显示对应内容区域
            if (section === 'punishment-reward') {
                document.getElementById('punishment-reward-section').style.display = 'block';
                loadPunishmentRewards();
            } else if (section === 'game-history') {
                document.getElementById('game-history-section').style.display = 'block';
                loadGameHistory();
            }
            
            // 激活对应导航
            event.target.classList.add('active');
        }

        function loadPunishmentRewards() {
            const type = document.getElementById('filterType').value;
            
            fetch(`/admin/punishment-reward?type=${type}&page=${currentPage}&limit=50`)
                .then(response => response.json())
                .then(data => {
                    if (data.code === 0) {
                        renderPunishmentRewards(data.data);
                    } else {
                        alert('加载失败：' + data.msg);
                    }
                });
        }

        function renderPunishmentRewards(items) {
            const tbody = document.getElementById('punishment-reward-tbody');
            tbody.innerHTML = '';
            
            items.forEach(item => {
                const typeText = item.type === 1 ? '<span class="badge bg-success type-badge">奖励</span>' : '<span class="badge bg-warning type-badge">惩罚</span>';
                const levelText = ['', '轻微', '一般', '严重'][item.level];
                const statusText = item.isActive ? '<span class="badge bg-success">启用</span>' : '<span class="badge bg-secondary">禁用</span>';
                const mediaPreview = item.mediaUrl ? `<img src="${item.mediaUrl}" class="media-preview" alt="预览">` : '-';
                
                const row = `
                    <tr>
                        <td>${item.id}</td>
                        <td>${item.title}</td>
                        <td>${item.content.length > 30 ? item.content.substring(0, 30) + '...' : item.content}</td>
                        <td>${typeText}</td>
                        <td><span class="badge bg-info level-badge">${levelText}</span></td>
                        <td>${mediaPreview}</td>
                        <td>${statusText}</td>
                        <td>${new Date(item.createdAt).toLocaleString()}</td>
                        <td>
                            <button class="btn btn-outline-primary btn-circle btn-sm me-1" onclick="editItem(${item.id})" title="编辑">
                                <i class="bi bi-pencil"></i>
                            </button>
                            <button class="btn btn-outline-danger btn-circle btn-sm" onclick="deleteItem(${item.id})" title="删除">
                                <i class="bi bi-trash"></i>
                            </button>
                        </td>
                    </tr>
                `;
                tbody.innerHTML += row;
            });
        }

        function showAddModal() {
            currentEditId = null;
            document.getElementById('modalTitle').textContent = '添加奖惩内容';
            document.getElementById('addEditForm').reset();
            document.getElementById('itemId').value = '';
            document.getElementById('isActive').checked = true;
            toggleMediaUpload();
            new bootstrap.Modal(document.getElementById('addEditModal')).show();
        }

        function editItem(id) {
            currentEditId = id;
            document.getElementById('modalTitle').textContent = '编辑奖惩内容';

            // 加载项目数据
            fetch(`/admin/punishment-reward/edit/${id}`, {
                headers: {
                    'Accept': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
                .then(response => response.json())
                .then(data => {
                    if (data.code === 0) {
                        const item = data.data;
                        document.getElementById('itemId').value = item.id;
                        document.getElementById('title').value = item.title;
                        document.getElementById('content').value = item.content;
                        document.getElementById('type').value = item.type;
                        document.getElementById('level').value = item.level;
                        document.getElementById('mediaType').value = item.mediaType;
                        document.getElementById('mediaUrl').value = item.mediaUrl || '';
                        document.getElementById('isActive').checked = item.isActive === 1;
                        
                        toggleMediaUpload();
                        if (item.mediaUrl) {
                            showMediaPreview(item.mediaUrl, item.mediaType);
                        }
                        
                        new bootstrap.Modal(document.getElementById('addEditModal')).show();
                    } else {
                        alert('加载失败：' + data.msg);
                    }
                });
        }

        function deleteItem(id) {
            if (confirm('确定要删除这个内容吗？')) {
                fetch(`/admin/punishment-reward/delete/${id}`, {
                    method: 'DELETE'
                })
                .then(response => response.json())
                .then(data => {
                    if (data.code === 0) {
                        alert('删除成功');
                        loadPunishmentRewards();
                    } else {
                        alert('删除失败：' + data.msg);
                    }
                });
            }
        }

        function toggleMediaUpload() {
            const mediaType = document.getElementById('mediaType').value;
            const uploadDiv = document.getElementById('mediaUploadDiv');
            uploadDiv.style.display = mediaType > 0 ? 'block' : 'none';
        }

        function uploadMedia() {
            const fileInput = document.getElementById('mediaFile');
            if (!fileInput.files[0]) return;
            
            const formData = new FormData();
            formData.append('file', fileInput.files[0]);
            
            fetch('/admin/upload', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.code === 0) {
                    document.getElementById('mediaUrl').value = data.data.url;
                    showMediaPreview(data.data.url, document.getElementById('mediaType').value);
                } else {
                    alert('上传失败：' + data.msg);
                }
            });
        }

        function showMediaPreview(url, mediaType) {
            const preview = document.getElementById('mediaPreview');
            let content = '';
            
            switch (parseInt(mediaType)) {
                case 1:
                    content = `<img src="${url}" class="img-thumbnail" style="max-width: 200px;">`;
                    break;
                case 2:
                    content = `<audio controls><source src="${url}" type="audio/mpeg"></audio>`;
                    break;
                case 3:
                    content = `<video controls style="max-width: 200px;"><source src="${url}" type="video/mp4"></video>`;
                    break;
            }
            
            preview.innerHTML = content;
        }

        function saveItem() {
            const form = document.getElementById('addEditForm');
            if (!form.checkValidity()) {
                form.reportValidity();
                return;
            }
            
            const data = {
                title: document.getElementById('title').value,
                content: document.getElementById('content').value,
                type: parseInt(document.getElementById('type').value),
                level: parseInt(document.getElementById('level').value),
                mediaType: parseInt(document.getElementById('mediaType').value),
                mediaUrl: document.getElementById('mediaUrl').value,
                isActive: document.getElementById('isActive').checked ? 1 : 0
            };
            
            const url = currentEditId ? `/admin/punishment-reward/edit/${currentEditId}` : '/admin/punishment-reward/add';
            const method = 'POST';
            
            fetch(url, {
                method: method,
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(data)
            })
            .then(response => response.json())
            .then(data => {
                if (data.code === 0) {
                    alert(currentEditId ? '更新成功' : '添加成功');
                    bootstrap.Modal.getInstance(document.getElementById('addEditModal')).hide();
                    loadPunishmentRewards();
                } else {
                    alert('保存失败：' + data.msg);
                }
            });
        }

        function loadGameHistory() {
            fetch('/game/history?page=1&limit=50')
                .then(response => response.json())
                .then(data => {
                    if (data.code === 0) {
                        renderGameHistory(data.data);
                    } else {
                        alert('加载失败：' + data.msg);
                    }
                });
        }

        function renderGameHistory(records) {
            const tbody = document.getElementById('game-history-tbody');
            tbody.innerHTML = '';
            
            records.forEach(record => {
                const selectedPlayerName = record.selectedPlayer === 1 ? record.player1Name : record.player2Name;
                
                const row = `
                    <tr>
                        <td>${new Date(record.createdAt).toLocaleString()}</td>
                        <td>${record.player1Name}</td>
                        <td>${record.player2Name}</td>
                        <td><strong>${selectedPlayerName}</strong></td>
                        <td>-</td>
                        <td><small class="text-muted">${record.gameSession}</small></td>
                    </tr>
                `;
                tbody.innerHTML += row;
            });
        }
    </script>
</body>
</html>