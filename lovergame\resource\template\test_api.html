<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        button { padding: 10px 20px; margin: 10px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer; }
        button:hover { background: #0056b3; }
        pre { background: #f8f9fa; padding: 15px; border-radius: 5px; overflow-x: auto; }
        .error { color: red; }
        .success { color: green; }
        .test-section { margin: 20px 0; padding: 20px; border: 1px solid #ddd; border-radius: 5px; }
    </style>
</head>
<body>
    <h1>🧪 API Test Page</h1>
    
    <div class="test-section">
        <h2>Test Love Quiz</h2>
        <button type="button" onclick="testLoveQuiz()">Test Love Quiz</button>
        <div id="loveQuizResult"></div>
    </div>
    
    <div class="test-section">
        <h2>Test Photo Challenge</h2>
        <button type="button" onclick="testPhotoChallenge()">Test Photo Challenge</button>
        <div id="photoChallengeResult"></div>
    </div>
    
    <div class="test-section">
        <h2>Test Database Tables</h2>
        <button type="button" onclick="testDatabaseTables()">Check Database Tables</button>
        <div id="databaseResult"></div>
    </div>
    
    <script>
        async function testLoveQuiz() {
            console.log('🧪 Testing Love Quiz API...');
            const resultDiv = document.getElementById('loveQuizResult');
            resultDiv.innerHTML = '<p>Testing...</p>';
            
            try {
                const response = await fetch('/game/play', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        player1Name: '测试1',
                        player2Name: '测试2',
                        gameMode: 'love_quiz'
                    })
                });
                
                console.log('📡 Response status:', response.status);
                const result = await response.json();
                console.log('📋 Response data:', result);
                
                if (result.code === 0) {
                    resultDiv.innerHTML = '<div class="success"><h4>✅ Success!</h4><pre>' + JSON.stringify(result, null, 2) + '</pre></div>';
                } else {
                    resultDiv.innerHTML = '<div class="error"><h4>❌ Error!</h4><pre>' + JSON.stringify(result, null, 2) + '</pre></div>';
                }
            } catch (error) {
                console.error('💥 Error:', error);
                resultDiv.innerHTML = '<div class="error"><h4>💥 Network Error!</h4><p>' + error.message + '</p></div>';
            }
        }
        
        async function testPhotoChallenge() {
            console.log('🧪 Testing Photo Challenge API...');
            const resultDiv = document.getElementById('photoChallengeResult');
            resultDiv.innerHTML = '<p>Testing...</p>';
            
            try {
                const response = await fetch('/game/play', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        player1Name: '测试1',
                        player2Name: '测试2',
                        gameMode: 'photo_challenge'
                    })
                });
                
                console.log('📡 Response status:', response.status);
                const result = await response.json();
                console.log('📋 Response data:', result);
                
                if (result.code === 0) {
                    resultDiv.innerHTML = '<div class="success"><h4>✅ Success!</h4><pre>' + JSON.stringify(result, null, 2) + '</pre></div>';
                } else {
                    resultDiv.innerHTML = '<div class="error"><h4>❌ Error!</h4><pre>' + JSON.stringify(result, null, 2) + '</pre></div>';
                }
            } catch (error) {
                console.error('💥 Error:', error);
                resultDiv.innerHTML = '<div class="error"><h4>💥 Network Error!</h4><p>' + error.message + '</p></div>';
            }
        }
        
        async function testDatabaseTables() {
            console.log('🧪 Testing Database Tables...');
            const resultDiv = document.getElementById('databaseResult');
            resultDiv.innerHTML = '<p>Testing...</p>';
            
            try {
                // 测试爱情问答数据
                const quizResponse = await fetch('/admin/love-quiz');
                console.log('📡 Quiz response status:', quizResponse.status);
                
                // 测试主题摄影数据
                const photoResponse = await fetch('/admin/photo-themes');
                console.log('📡 Photo response status:', photoResponse.status);
                
                const quizResult = await quizResponse.json();
                const photoResult = await photoResponse.json();
                
                console.log('📋 Quiz data:', quizResult);
                console.log('📋 Photo data:', photoResult);
                
                resultDiv.innerHTML = `
                    <div class="success">
                        <h4>✅ Database Test Results</h4>
                        <h5>Love Quiz Data:</h5>
                        <pre>${JSON.stringify(quizResult, null, 2)}</pre>
                        <h5>Photo Themes Data:</h5>
                        <pre>${JSON.stringify(photoResult, null, 2)}</pre>
                    </div>
                `;
            } catch (error) {
                console.error('💥 Error:', error);
                resultDiv.innerHTML = '<div class="error"><h4>💥 Database Test Error!</h4><p>' + error.message + '</p></div>';
            }
        }
    </script>
</body>
</html>
