<!DOCTYPE html>
<html>
<head>
    <title>API Test</title>
</head>
<body>
    <h1>API Test Page</h1>
    
    <h2>Test Love Quiz</h2>
    <button onclick="testLoveQuiz()">Test Love Quiz</button>
    <div id="loveQuizResult"></div>
    
    <h2>Test Photo Challenge</h2>
    <button onclick="testPhotoChallenge()">Test Photo Challenge</button>
    <div id="photoChallengeResult"></div>
    
    <script>
        async function testLoveQuiz() {
            try {
                const response = await fetch('/game/play', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        player1Name: '测试1',
                        player2Name: '测试2',
                        gameMode: 'love_quiz'
                    })
                });
                
                const result = await response.json();
                document.getElementById('loveQuizResult').innerHTML = '<pre>' + JSON.stringify(result, null, 2) + '</pre>';
            } catch (error) {
                document.getElementById('loveQuizResult').innerHTML = 'Error: ' + error.message;
            }
        }
        
        async function testPhotoChallenge() {
            try {
                const response = await fetch('/game/play', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        player1Name: '测试1',
                        player2Name: '测试2',
                        gameMode: 'photo_challenge'
                    })
                });
                
                const result = await response.json();
                document.getElementById('photoChallengeResult').innerHTML = '<pre>' + JSON.stringify(result, null, 2) + '</pre>';
            } catch (error) {
                document.getElementById('photoChallengeResult').innerHTML = 'Error: ' + error.message;
            }
        }
    </script>
</body>
</html>
