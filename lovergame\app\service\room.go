package service

import (
	"context"
	"fmt"
	"lovergame/app/model"
	"sync"
	"time"

	"github.com/gogf/gf/v2/util/guid"
)

// GameRoom 游戏房间
type GameRoom struct {
	ID          string             `json:"id"`
	Name        string             `json:"name,omitempty"` // 房间名字，可选
	CreatedBy   string             `json:"createdBy"`
	Players     []string           `json:"players"`
	Settings    *model.GameRequest `json:"settings"`
	Status      string             `json:"status"` // waiting, playing, finished
	CurrentGame *model.GameResult  `json:"currentGame,omitempty"`
	CreatedAt   time.Time          `json:"createdAt"`
	UpdatedAt   time.Time          `json:"updatedAt"`
}

// RoomManager 房间管理器
type RoomManager struct {
	rooms map[string]*GameRoom
	mutex sync.RWMutex
}

var roomManager = &RoomManager{
	rooms: make(map[string]*GameRoom),
}

// IRoomService 房间服务接口
type IRoomService interface {
	CreateRoom(ctx context.Context, name, createdBy string, settings *model.GameRequest) (*GameRoom, error)
	JoinRoom(ctx context.Context, roomID, playerName string) (*GameRoom, error)
	GetRoom(ctx context.Context, roomID string) (*GameRoom, error)
	StartGame(ctx context.Context, roomID string) (*model.GameResult, error)
	LeaveRoom(ctx context.Context, roomID, playerName string) error
	ListRooms(ctx context.Context) ([]*GameRoom, error)
}

type roomService struct{}

func RoomService() IRoomService {
	return &roomService{}
}

// CreateRoom 创建房间
func (s *roomService) CreateRoom(ctx context.Context, name, createdBy string, settings *model.GameRequest) (*GameRoom, error) {
	roomManager.mutex.Lock()
	defer roomManager.mutex.Unlock()

	room := &GameRoom{
		ID:        guid.S()[:8], // 使用短ID方便分享
		Name:      name,         // 房间名字
		CreatedBy: createdBy,
		Players:   []string{createdBy},
		Settings:  settings,
		Status:    "waiting",
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	roomManager.rooms[room.ID] = room
	return room, nil
}

// JoinRoom 加入房间
func (s *roomService) JoinRoom(ctx context.Context, roomID, playerName string) (*GameRoom, error) {
	roomManager.mutex.Lock()
	defer roomManager.mutex.Unlock()

	room, exists := roomManager.rooms[roomID]
	if !exists {
		return nil, fmt.Errorf("房间不存在")
	}

	if room.Status != "waiting" {
		return nil, fmt.Errorf("房间已开始游戏或已结束")
	}

	// 检查玩家是否已在房间中
	for _, player := range room.Players {
		if player == playerName {
			return room, nil // 已在房间中
		}
	}

	// 检查房间是否已满
	if len(room.Players) >= 2 {
		return nil, fmt.Errorf("房间已满")
	}

	room.Players = append(room.Players, playerName)
	room.UpdatedAt = time.Now()

	return room, nil
}

// GetRoom 获取房间信息
func (s *roomService) GetRoom(ctx context.Context, roomID string) (*GameRoom, error) {
	roomManager.mutex.RLock()
	defer roomManager.mutex.RUnlock()

	room, exists := roomManager.rooms[roomID]
	if !exists {
		return nil, fmt.Errorf("房间不存在")
	}

	return room, nil
}

// StartGame 开始游戏
func (s *roomService) StartGame(ctx context.Context, roomID string) (*model.GameResult, error) {
	roomManager.mutex.Lock()
	defer roomManager.mutex.Unlock()

	room, exists := roomManager.rooms[roomID]
	if !exists {
		return nil, fmt.Errorf("房间不存在")
	}

	if len(room.Players) < 2 {
		return nil, fmt.Errorf("需要2个玩家才能开始游戏")
	}

	if room.Status != "waiting" && room.Status != "playing" {
		return nil, fmt.Errorf("房间状态不正确")
	}

	// 构建游戏请求
	gameReq := &model.GameRequest{
		Player1Name: room.Players[0],
		Player2Name: room.Players[1],
		GameMode:    room.Settings.GameMode,
		Type:        room.Settings.Type,
		Level:       room.Settings.Level,
	}

	// 开始游戏
	result, err := Game().PlayGame(ctx, gameReq)
	if err != nil {
		return nil, err
	}

	room.Status = "playing"
	room.CurrentGame = result
	room.UpdatedAt = time.Now()

	return result, nil
}

// LeaveRoom 离开房间
func (s *roomService) LeaveRoom(ctx context.Context, roomID, playerName string) error {
	roomManager.mutex.Lock()
	defer roomManager.mutex.Unlock()

	room, exists := roomManager.rooms[roomID]
	if !exists {
		return fmt.Errorf("房间不存在")
	}

	// 移除玩家
	for i, player := range room.Players {
		if player == playerName {
			room.Players = append(room.Players[:i], room.Players[i+1:]...)
			break
		}
	}

	room.UpdatedAt = time.Now()

	// 如果房间为空，删除房间
	if len(room.Players) == 0 {
		delete(roomManager.rooms, roomID)
	}

	return nil
}

// ListRooms 获取房间列表
func (s *roomService) ListRooms(ctx context.Context) ([]*GameRoom, error) {
	roomManager.mutex.RLock()
	defer roomManager.mutex.RUnlock()

	var rooms []*GameRoom
	for _, room := range roomManager.rooms {
		if room.Status == "waiting" && len(room.Players) < 2 {
			rooms = append(rooms, room)
		}
	}

	return rooms, nil
}

// CleanupExpiredRooms 清理过期房间
func (s *roomService) CleanupExpiredRooms() {
	roomManager.mutex.Lock()
	defer roomManager.mutex.Unlock()

	now := time.Now()
	for id, room := range roomManager.rooms {
		// 删除30分钟前创建且无活动的房间
		if now.Sub(room.UpdatedAt) > 30*time.Minute {
			delete(roomManager.rooms, id)
		}
	}
}

// 定期清理过期房间
func init() {
	go func() {
		ticker := time.NewTicker(10 * time.Minute)
		defer ticker.Stop()

		service := &roomService{}
		for range ticker.C {
			service.CleanupExpiredRooms()
		}
	}()
}
