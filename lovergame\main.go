package main

import (
	"context"
	"fmt"
	"lovergame/app/controller"
	"lovergame/database"
	"time"

	_ "github.com/gogf/gf/contrib/drivers/sqlite/v2"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/ghttp"
)

func main() {
	ctx := context.Background()

	fmt.Println("🚀 Starting LoverGame Server...")
	fmt.Printf("📅 Start time: %s\n", time.Now().Format("2006-01-02 15:04:05"))

	// 初始化数据库
	fmt.Println("🗄️  Initializing database...")
	if err := database.InitDB(); err != nil {
		g.Log().Fatalf(ctx, "Database initialization failed: %v", err)
	}
	fmt.Println("✅ Database initialized successfully")

	fmt.Println("🌐 Creating HTTP server...")
	s := g.Server()

	// 游戏相关路由
	fmt.Println("🎮 Setting up game routes...")
	gameCtrl := controller.NewGameController()
	s.Group("/", func(group *ghttp.RouterGroup) {
		group.GET("/", gameCtrl.Index)
		group.POST("/game/play", gameCtrl.Play)
		group.POST("/game/continue", gameCtrl.ContinueGame)
		group.POST("/game/submit-photo", gameCtrl.SubmitPhoto)
		group.GET("/game/history", gameCtrl.History)
		group.GET("/game/presets", gameCtrl.GetPresetList)
		group.POST("/game/apply-preset", gameCtrl.ApplyPreset)
		group.POST("/game/save-preset", gameCtrl.SaveAsPreset)
		group.GET("/test/slot", gameCtrl.TestSlot) // 测试老虎机数据
		group.GET("/test/slot-page", func(r *ghttp.Request) {
			r.Response.WriteTpl("test_slot.html", nil)
		}) // 测试页面

		// 房间相关路由
		group.POST("/room/create", gameCtrl.CreateRoom)
		group.POST("/room/join", gameCtrl.JoinRoom)
		group.GET("/room/{roomId}", gameCtrl.GetRoom)
		group.POST("/room/{roomId}/start", gameCtrl.StartRoomGame)
		group.GET("/rooms", gameCtrl.ListRooms)
	})

	// 后台管理路由
	fmt.Println("🔧 Setting up admin routes...")
	adminCtrl := controller.NewAdminController()
	s.Group("/admin", func(group *ghttp.RouterGroup) {
		group.ALL("/login", adminCtrl.Login)
		group.GET("/logout", adminCtrl.Logout)
		group.GET("/", adminCtrl.Index)
		group.ALL("/punishment-reward", adminCtrl.PunishmentRewardList)
		group.ALL("/punishment-reward/add", adminCtrl.PunishmentRewardAdd)
		group.ALL("/punishment-reward/edit/{id}", adminCtrl.PunishmentRewardEdit)
		group.DELETE("/punishment-reward/delete/{id}", adminCtrl.PunishmentRewardDelete)
		group.ALL("/slot-symbols", adminCtrl.SlotSymbolList)
		group.ALL("/slot-symbols/add", adminCtrl.SlotSymbolAdd)
		group.ALL("/slot-symbols/edit/{id}", adminCtrl.SlotSymbolEdit)
		group.DELETE("/slot-symbols/delete/{id}", adminCtrl.SlotSymbolDelete)
		group.ALL("/slot-combinations", adminCtrl.SlotCombinationList)
		group.ALL("/slot-combinations/add", adminCtrl.SlotCombinationAdd)
		group.ALL("/slot-combinations/edit/{id}", adminCtrl.SlotCombinationEdit)
		group.DELETE("/slot-combinations/delete/{id}", adminCtrl.SlotCombinationDelete)
		group.ALL("/truth-dare-questions", adminCtrl.TruthDareQuestionList)
		group.ALL("/truth-dare-questions/add", adminCtrl.TruthDareQuestionAdd)
		group.ALL("/truth-dare-questions/edit/{id}", adminCtrl.TruthDareQuestionEdit)
		group.DELETE("/truth-dare-questions/delete/{id}", adminCtrl.TruthDareQuestionDelete)
		group.ALL("/dice-challenges", adminCtrl.DiceChallengeList)
		group.ALL("/dice-challenges/add", adminCtrl.DiceChallengeAdd)
		group.ALL("/dice-challenges/edit/{id}", adminCtrl.DiceChallengeEdit)
		group.DELETE("/dice-challenges/delete/{id}", adminCtrl.DiceChallengeDelete)
		group.ALL("/game-settings", adminCtrl.GameSettingsList)
		group.POST("/game-settings/update", adminCtrl.GameSettingsUpdate)
		group.ALL("/love-quiz", adminCtrl.LoveQuizList)
		group.POST("/love-quiz/add", adminCtrl.LoveQuizAdd)
		group.ALL("/love-quiz/edit/{id}", adminCtrl.LoveQuizEdit)
		group.DELETE("/love-quiz/delete/{id}", adminCtrl.LoveQuizDelete)
		group.ALL("/photo-themes", adminCtrl.PhotoThemeList)
		group.POST("/photo-themes", adminCtrl.PhotoThemeAdd)
		group.ALL("/photo-themes/{id}", adminCtrl.PhotoThemeEdit)
		group.DELETE("/photo-themes/{id}", adminCtrl.PhotoThemeDelete)

		// 预设管理路由
		group.GET("/presets", gameCtrl.GetPresetsAdmin)
		group.POST("/presets", gameCtrl.CreatePresetAdmin)
		group.GET("/presets/{id}", gameCtrl.GetPresetAdmin)
		group.PUT("/presets/{id}", gameCtrl.UpdatePresetAdmin)
		group.DELETE("/presets/{id}", gameCtrl.DeletePresetAdmin)
		group.GET("/preset-feature-status", gameCtrl.GetPresetFeatureStatus)
		group.POST("/toggle-preset-feature", gameCtrl.TogglePresetFeature)

		group.POST("/upload", adminCtrl.Upload)

		// 标签和关系类型管理路由
		group.ALL("/content-tags", adminCtrl.ContentTagList)
		group.POST("/content-tags", adminCtrl.ContentTagAdd)
		group.ALL("/content-tags/{id}", adminCtrl.ContentTagEdit)
		group.DELETE("/content-tags/{id}", adminCtrl.ContentTagDelete)
		group.ALL("/relationship-types", adminCtrl.RelationshipTypeList)
		group.POST("/relationship-types", adminCtrl.RelationshipTypeAdd)
		group.ALL("/relationship-types/{id}", adminCtrl.RelationshipTypeEdit)
		group.DELETE("/relationship-types/{id}", adminCtrl.RelationshipTypeDelete)
	})

	fmt.Println("✅ All routes configured")
	fmt.Println("🚀 Starting server on http://localhost:8080")
	fmt.Println("📋 Available endpoints:")
	fmt.Println("   🎮 Game: http://localhost:8080")
	fmt.Println("   🔧 Admin: http://localhost:8080/admin")
	fmt.Println("   🧪 Test API: http://localhost:8080/test_api.html")
	fmt.Println("=" + fmt.Sprintf("%50s", "="))

	g.Log().Info(ctx, "🚀 Starting server with SQLite database...")
	s.Run()
}
