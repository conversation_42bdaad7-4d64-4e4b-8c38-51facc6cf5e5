package main

import (
	"database/sql"
	"fmt"
	"io/ioutil"
	"log"
	"os"

	_ "github.com/mattn/go-sqlite3"
)

func main() {
	// 打开数据库连接
	db, err := sql.Open("sqlite3", "./database/lovergame.db")
	if err != nil {
		log.Fatal("Failed to open database:", err)
	}
	defer db.Close()

	// 读取SQL文件
	sqlContent, err := ioutil.ReadFile("./sql/init_sqlite.sql")
	if err != nil {
		log.Fatal("Failed to read SQL file:", err)
	}

	// 执行SQL
	_, err = db.Exec(string(sqlContent))
	if err != nil {
		log.Fatal("Failed to execute SQL:", err)
	}

	fmt.Println("Database initialized successfully!")
	
	// 验证表是否创建成功
	tables := []string{"content_tags", "relationship_types"}
	for _, table := range tables {
		var count int
		err = db.QueryRow(fmt.Sprintf("SELECT COUNT(*) FROM %s", table)).Scan(&count)
		if err != nil {
			log.Printf("Failed to query table %s: %v", table, err)
		} else {
			fmt.Printf("Table %s has %d records\n", table, count)
		}
	}
	
	// 检查punishment_reward表的记录数
	var prCount int
	err = db.QueryRow("SELECT COUNT(*) FROM punishment_reward").Scan(&prCount)
	if err != nil {
		log.Printf("Failed to query punishment_reward: %v", err)
	} else {
		fmt.Printf("Table punishment_reward has %d records\n", prCount)
	}
}
