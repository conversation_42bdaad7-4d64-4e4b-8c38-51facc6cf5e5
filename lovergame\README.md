# 双人互动奖惩游戏

一个基于GoFrame框架开发的双人互动奖惩游戏，支持图片、音频、视频等多媒体内容展示。**默认使用SQLite数据库，开箱即用！**

## 功能特性

- 🎮 **双人游戏**: 支持两个玩家参与的互动游戏
- 🎯 **随机选择**: 随机选择玩家和奖惩内容
- 🎁 **奖惩系统**: 支持奖励和惩罚两种类型的内容
- 📱 **多媒体支持**: 支持图片、音频、视频展示
- ⚙️ **后台管理**: 完整的后台管理系统，可管理奖惩内容
- 📊 **游戏记录**: 记录所有游戏历史
- 🎨 **美观界面**: 响应式设计，支持移动端
- 💾 **开箱即用**: 默认使用SQLite，无需额外数据库配置

## 技术栈

- **后端**: GoFrame v2.7.1
- **前端**: Bootstrap 5 + Vanilla JavaScript
- **数据库**: SQLite (默认) / MySQL (可选)
- **模板引擎**: GoFrame内置模板引擎

## 快速开始

### 方式一：直接运行（推荐）

```bash
# 1. 克隆项目
git clone <项目地址>
cd lovergame

# 2. 下载依赖
go mod tidy

# 3. 直接运行（会自动创建SQLite数据库）
go run main.go

# 4. 访问应用
# 游戏主页: http://localhost:8080
# 后台管理: http://localhost:8080/admin
```

### 方式二：使用MySQL

如果你想使用MySQL而不是SQLite，请按以下步骤：

1. **创建MySQL数据库**：
```sql
CREATE DATABASE lovergame CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

2. **导入数据表和示例数据**：
```bash
mysql -u username -p lovergame < sql/init.sql
```

3. **修改配置文件** `config/config.yaml`：
```yaml
database:
  default:
    link: "mysql:用户名:密码@tcp(127.0.0.1:3306)/lovergame"
```

4. **运行项目**：
```bash
go run main.go
```

## 项目结构

```
lovergame/
├── app/
│   ├── controller/          # 控制器
│   ├── model/              # 数据模型
│   └── service/            # 业务逻辑服务
├── config/                 # 配置文件
├── database/               # 数据库相关
│   ├── init.go            # 数据库初始化
│   └── lovergame.db       # SQLite数据库文件（自动生成）
├── resource/
│   ├── public/            # 静态资源
│   │   ├── css/
│   │   ├── js/
│   │   └── upload/        # 上传文件目录
│   │       ├── images/    # 图片
│   │       ├── audios/    # 音频
│   │       └── videos/    # 视频
│   └── template/          # 模板文件
│       ├── admin/         # 后台管理模板
│       └── game/          # 游戏模板
├── sql/                   # SQL脚本
│   ├── init.sql          # MySQL初始化脚本
│   └── init_sqlite.sql   # SQLite初始化脚本
├── go.mod                 # Go模块文件
└── main.go               # 主程序入口
```

## 环境要求

- Go 1.21+
- SQLite (自带，无需安装)
- MySQL 5.7+ (可选)

## 使用说明

### 游戏玩法

1. 输入两个玩家的姓名
2. 选择游戏类型（奖励、惩罚或混合）
3. 选择游戏级别（轻微、一般、严重）
4. 点击"开始游戏"按钮
5. 系统会随机选择一个玩家和对应的奖惩内容
6. 如果内容包含多媒体文件，会自动展示

### 后台管理

1. 访问 `/admin` 进入后台管理
2. 可以添加、编辑、删除奖惩内容
3. 支持上传图片、音频、视频文件
4. 可以查看所有游戏记录

### 内容配置

奖惩内容支持以下配置：

- **标题**: 内容的简短标题
- **描述**: 详细的内容描述
- **类型**: 奖励或惩罚
- **级别**: 轻微、一般、严重
- **媒体**: 可选的图片、音频或视频文件
- **状态**: 启用或禁用

## API接口

### 游戏相关

- `GET /` - 游戏主页
- `POST /game/play` - 开始游戏
- `GET /game/history` - 获取游戏记录

### 后台管理

- `GET /admin` - 后台管理首页
- `GET /admin/punishment-reward` - 获取奖惩内容列表
- `POST /admin/punishment-reward/add` - 添加奖惩内容
- `POST /admin/punishment-reward/edit/{id}` - 编辑奖惩内容
- `DELETE /admin/punishment-reward/delete/{id}` - 删除奖惩内容
- `POST /admin/upload` - 文件上传

## 数据库表结构

### punishment_reward (奖惩内容表)

| 字段 | 类型 | 说明 |
|------|------|------|
| id | INTEGER/int | 主键ID |
| title | TEXT/varchar(255) | 标题 |
| content | TEXT/text | 内容描述 |
| type | INTEGER/tinyint | 类型：1=奖励，2=惩罚 |
| media_type | INTEGER/tinyint | 媒体类型：0=无，1=图片，2=音频，3=视频 |
| media_url | TEXT/varchar(500) | 媒体文件URL |
| level | INTEGER/tinyint | 级别：1=轻微，2=一般，3=严重 |
| is_active | INTEGER/tinyint | 是否启用：0=禁用，1=启用 |
| created_at | DATETIME/datetime | 创建时间 |
| updated_at | DATETIME/datetime | 更新时间 |

### game_record (游戏记录表)

| 字段 | 类型 | 说明 |
|------|------|------|
| id | INTEGER/int | 主键ID |
| player1_name | TEXT/varchar(100) | 玩家1姓名 |
| player2_name | TEXT/varchar(100) | 玩家2姓名 |
| selected_item_id | INTEGER/int | 选中的奖惩内容ID |
| selected_player | INTEGER/tinyint | 被选中的玩家：1=玩家1，2=玩家2 |
| game_session | TEXT/varchar(100) | 游戏会话ID |
| created_at | DATETIME/datetime | 创建时间 |

## 部署说明

### 开发环境
```bash
go run main.go
```

### 生产环境
```bash
# 编译
go build -o lovergame main.go

# 运行
./lovergame
```

### Docker部署（可选）
```dockerfile
FROM golang:1.21-alpine AS builder
WORKDIR /app
COPY . .
RUN go mod tidy && go build -o lovergame main.go

FROM alpine:latest
RUN apk --no-cache add ca-certificates tzdata
WORKDIR /root/
COPY --from=builder /app/lovergame .
COPY --from=builder /app/config ./config
COPY --from=builder /app/resource ./resource
COPY --from=builder /app/sql ./sql
EXPOSE 8080
CMD ["./lovergame"]
```

## 配置说明

### 数据库配置

**SQLite（默认）**：
```yaml
database:
  default:
    link: "sqlite:./database/lovergame.db"
```

**MySQL**：
```yaml
database:
  default:
    link: "mysql:用户名:密码@tcp(127.0.0.1:3306)/lovergame"
```

### 服务器配置
```yaml
server:
  address: ":8080"
  serverRoot: "resource/public"
```

## 故障排除

### 常见问题

1. **数据库初始化失败**
   - 检查database目录权限
   - 确保sql/init_sqlite.sql文件存在

2. **文件上传失败**
   - 检查resource/public/upload目录权限
   - 确保有足够的磁盘空间

3. **SQLite锁定问题**
   - 确保只有一个程序实例在运行
   - 检查数据库文件权限

## 许可证

MIT License

## 贡献

欢迎提交Issue和Pull Request来帮助改进这个项目。

## 作者

Created with GoFrame Framework