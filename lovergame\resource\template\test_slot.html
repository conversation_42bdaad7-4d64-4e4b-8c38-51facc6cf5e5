<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>老虎机测试页面</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <h1>老虎机功能测试</h1>
        
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>测试老虎机游戏</h5>
                    </div>
                    <div class="card-body">
                        <form id="slotTestForm">
                            <div class="mb-3">
                                <label for="player1" class="form-label">玩家1</label>
                                <input type="text" class="form-control" id="player1" value="张三">
                            </div>
                            <div class="mb-3">
                                <label for="player2" class="form-label">玩家2</label>
                                <input type="text" class="form-control" id="player2" value="李四">
                            </div>
                            <button type="button" class="btn btn-primary" onclick="testSlotMachine()">测试老虎机</button>
                        </form>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>测试结果</h5>
                    </div>
                    <div class="card-body">
                        <div id="testResult">
                            <p class="text-muted">点击测试按钮查看结果</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5>数据库状态</h5>
                    </div>
                    <div class="card-body">
                        <button type="button" class="btn btn-info" onclick="checkDatabase()">检查数据库</button>
                        <div id="dbStatus" class="mt-3"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function testSlotMachine() {
            const player1 = document.getElementById('player1').value;
            const player2 = document.getElementById('player2').value;
            
            const gameData = {
                player1Name: player1,
                player2Name: player2,
                gameMode: 'slot_machine'
            };
            
            console.log('Sending request:', gameData);
            
            fetch('/game/play', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(gameData)
            })
            .then(response => response.json())
            .then(data => {
                console.log('Response:', data);
                displayResult(data);
            })
            .catch(error => {
                console.error('Error:', error);
                document.getElementById('testResult').innerHTML = `
                    <div class="alert alert-danger">
                        <strong>错误:</strong> ${error.message}
                    </div>
                `;
            });
        }
        
        function displayResult(data) {
            const resultDiv = document.getElementById('testResult');
            
            if (data.code === 0) {
                const result = data.data;
                let html = `
                    <div class="alert alert-success">
                        <strong>游戏成功!</strong>
                    </div>
                    <p><strong>游戏模式:</strong> ${result.gameMode}</p>
                    <p><strong>玩家:</strong> ${result.player1Name} vs ${result.player2Name}</p>
                `;
                
                if (result.slotResult) {
                    const slot = result.slotResult;
                    html += `
                        <h6>老虎机结果:</h6>
                        <p><strong>符号组合:</strong> 
                            ${slot.symbols.map(s => s.emoji + ' ' + s.displayName).join(' | ')}
                        </p>
                        <p><strong>是否获胜:</strong> ${slot.isWin ? '是' : '否'}</p>
                        <p><strong>活动标题:</strong> ${slot.combination.activityTitle}</p>
                        <p><strong>活动内容:</strong> ${slot.combination.activityContent}</p>
                    `;
                }
                
                resultDiv.innerHTML = html;
            } else {
                resultDiv.innerHTML = `
                    <div class="alert alert-danger">
                        <strong>游戏失败:</strong> ${data.msg}
                    </div>
                `;
            }
        }
        
        function checkDatabase() {
            fetch('/test/slot')
            .then(response => response.json())
            .then(data => {
                console.log('Database status:', data);
                const statusDiv = document.getElementById('dbStatus');
                
                if (data.code === 0) {
                    statusDiv.innerHTML = `
                        <div class="alert alert-info">
                            <p><strong>符号数量:</strong> ${data.data.symbolCount}</p>
                            <p><strong>组合数量:</strong> ${data.data.comboCount}</p>
                            <p><strong>符号列表:</strong></p>
                            <ul>
                                ${data.data.symbols.map(s => `<li>${s.emoji} ${s.displayName} (稀有度: ${s.rarity})</li>`).join('')}
                            </ul>
                        </div>
                    `;
                } else {
                    statusDiv.innerHTML = `
                        <div class="alert alert-danger">
                            <strong>数据库检查失败:</strong> ${data.msg}
                        </div>
                    `;
                }
            })
            .catch(error => {
                console.error('Error:', error);
                document.getElementById('dbStatus').innerHTML = `
                    <div class="alert alert-danger">
                        <strong>请求失败:</strong> ${error.message}
                    </div>
                `;
            });
        }
        
        // 页面加载时自动检查数据库
        document.addEventListener('DOMContentLoaded', function() {
            checkDatabase();
        });
    </script>
</body>
</html>
