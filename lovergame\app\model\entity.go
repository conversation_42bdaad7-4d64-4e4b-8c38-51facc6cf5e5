package model

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// PunishmentReward 奖惩内容模型
type PunishmentReward struct {
	Id        int         `json:"id"        orm:"id,primary"`
	Title     string      `json:"title"     orm:"title"`
	Content   string      `json:"content"   orm:"content"`
	Type      int         `json:"type"      orm:"type"`       // 1=奖励，2=惩罚
	MediaType int         `json:"mediaType" orm:"media_type"` // 0=无，1=图片，2=音频，3=视频
	MediaUrl  string      `json:"mediaUrl"  orm:"media_url"`
	Level     int         `json:"level"     orm:"level"`     // 1=轻微，2=一般，3=严重
	IsActive  int         `json:"isActive"  orm:"is_active"` // 0=禁用，1=启用
	CreatedAt *gtime.Time `json:"createdAt" orm:"created_at"`
	UpdatedAt *gtime.Time `json:"updatedAt" orm:"updated_at"`
}

// GameRecord 游戏记录模型
type GameRecord struct {
	Id             int         `json:"id"             orm:"id,primary"`
	Player1Name    string      `json:"player1Name"    orm:"player1_name"`
	Player2Name    string      `json:"player2Name"    orm:"player2_name"`
	GameModeId     int         `json:"gameModeId"     orm:"game_mode_id"`
	SelectedItemId *int        `json:"selectedItemId" orm:"selected_item_id"`
	SelectedPlayer *int        `json:"selectedPlayer" orm:"selected_player"` // 1=玩家1，2=玩家2
	SlotResult     string      `json:"slotResult"     orm:"slot_result"`     // JSON格式存储老虎机结果
	GameSession    string      `json:"gameSession"    orm:"game_session"`
	CreatedAt      *gtime.Time `json:"createdAt"      orm:"created_at"`
}

// GameMode 游戏模式模型
type GameMode struct {
	Id          int         `json:"id"          orm:"id,primary"`
	Name        string      `json:"name"        orm:"name"`
	DisplayName string      `json:"displayName" orm:"display_name"`
	Description string      `json:"description" orm:"description"`
	IsActive    int         `json:"isActive"    orm:"is_active"`
	CreatedAt   *gtime.Time `json:"createdAt"   orm:"created_at"`
	UpdatedAt   *gtime.Time `json:"updatedAt"   orm:"updated_at"`
}

// SlotSymbol 老虎机符号模型
type SlotSymbol struct {
	Id          int         `json:"id"          orm:"id,primary"`
	Name        string      `json:"name"        orm:"name"`
	DisplayName string      `json:"displayName" orm:"display_name"`
	Emoji       string      `json:"emoji"       orm:"emoji"`
	Color       string      `json:"color"       orm:"color"`
	Rarity      int         `json:"rarity"      orm:"rarity"`
	IsActive    int         `json:"isActive"    orm:"is_active"`
	CreatedAt   *gtime.Time `json:"createdAt"   orm:"created_at"`
	UpdatedAt   *gtime.Time `json:"updatedAt"   orm:"updated_at"`
}

// SlotCombination 老虎机组合模型
type SlotCombination struct {
	Id              int         `json:"id"              orm:"id,primary"`
	Name            string      `json:"name"            orm:"name"`
	Symbol1Id       int         `json:"symbol1Id"       orm:"symbol1_id"`
	Symbol2Id       int         `json:"symbol2Id"       orm:"symbol2_id"`
	Symbol3Id       int         `json:"symbol3Id"       orm:"symbol3_id"`
	ActivityTitle   string      `json:"activityTitle"   orm:"activity_title"`
	ActivityContent string      `json:"activityContent" orm:"activity_content"`
	ActivityType    int         `json:"activityType"    orm:"activity_type"`
	IntensityLevel  int         `json:"intensityLevel"  orm:"intensity_level"`
	MediaType       int         `json:"mediaType"       orm:"media_type"`
	MediaUrl        string      `json:"mediaUrl"        orm:"media_url"`
	IsActive        int         `json:"isActive"        orm:"is_active"`
	CreatedAt       *gtime.Time `json:"createdAt"       orm:"created_at"`
	UpdatedAt       *gtime.Time `json:"updatedAt"       orm:"updated_at"`
}

// SlotResult 老虎机结果
type SlotResult struct {
	Symbols     [3]*SlotSymbol   `json:"symbols"`
	Combination *SlotCombination `json:"combination"`
	IsWin       bool             `json:"isWin"`
	Message     string           `json:"message"`
}

// TruthDareQuestion 真心话大冒险问题模型
type TruthDareQuestion struct {
	Id         int         `json:"id"         orm:"id,primary"`
	Type       int         `json:"type"       orm:"type"`
	Title      string      `json:"title"      orm:"title"`
	Content    string      `json:"content"    orm:"content"`
	Difficulty int         `json:"difficulty" orm:"difficulty"`
	Category   string      `json:"category"   orm:"category"`
	MediaType  int         `json:"mediaType"  orm:"media_type"`
	MediaUrl   string      `json:"mediaUrl"   orm:"media_url"`
	IsActive   int         `json:"isActive"   orm:"is_active"`
	CreatedAt  *gtime.Time `json:"createdAt"  orm:"created_at"`
	UpdatedAt  *gtime.Time `json:"updatedAt"  orm:"updated_at"`
}

// DiceChallenge 浪漫骰子挑战模型
type DiceChallenge struct {
	Id              int         `json:"id"               orm:"id,primary"`
	Name            string      `json:"name"             orm:"name"`
	Description     string      `json:"description"      orm:"description"`
	DiceCombination string      `json:"diceCombination"  orm:"dice_combination"`
	ActivityTitle   string      `json:"activityTitle"    orm:"activity_title"`
	ActivityContent string      `json:"activityContent"  orm:"activity_content"`
	Difficulty      int         `json:"difficulty"       orm:"difficulty"`
	DurationMinutes int         `json:"durationMinutes"  orm:"duration_minutes"`
	RequiresProps   int         `json:"requiresProps"    orm:"requires_props"`
	PropsNeeded     string      `json:"propsNeeded"      orm:"props_needed"`
	MediaType       int         `json:"mediaType"        orm:"media_type"`
	MediaUrl        string      `json:"mediaUrl"         orm:"media_url"`
	IsActive        int         `json:"isActive"         orm:"is_active"`
	CreatedAt       *gtime.Time `json:"createdAt"        orm:"created_at"`
	UpdatedAt       *gtime.Time `json:"updatedAt"        orm:"updated_at"`
}

// TruthDareResult 真心话大冒险结果
type TruthDareResult struct {
	Question     *TruthDareQuestion `json:"question"`
	SelectedType int                `json:"selectedType"` // 1=真心话, 2=大冒险
	Message      string             `json:"message"`
}

// DiceResult 骰子游戏结果
type DiceResult struct {
	Dice1     int            `json:"dice1"`
	Dice2     int            `json:"dice2"`
	Sum       int            `json:"sum"`
	Challenge *DiceChallenge `json:"challenge"`
	Message   string         `json:"message"`
}

// GameSetting 游戏设置模型
type GameSetting struct {
	Id           int         `json:"id"           orm:"id,primary"`
	SettingKey   string      `json:"settingKey"   orm:"setting_key"`
	SettingValue string      `json:"settingValue" orm:"setting_value"`
	SettingType  string      `json:"settingType"  orm:"setting_type"`
	Description  string      `json:"description"  orm:"description"`
	CreatedAt    *gtime.Time `json:"createdAt"    orm:"created_at"`
	UpdatedAt    *gtime.Time `json:"updatedAt"    orm:"updated_at"`
}

// LoveQuiz 爱情问答模型
type LoveQuiz struct {
	Id            int         `json:"id"            orm:"id,primary"`
	QuestionType  int         `json:"questionType"  orm:"question_type"`
	Difficulty    int         `json:"difficulty"    orm:"difficulty"`
	Category      string      `json:"category"      orm:"category"`
	Question      string      `json:"question"      orm:"question"`
	AnswerType    int         `json:"answerType"    orm:"answer_type"`
	Options       string      `json:"options"       orm:"options"`
	CorrectAnswer string      `json:"correctAnswer" orm:"correct_answer"`
	Points        int         `json:"points"        orm:"points"`
	MediaType     int         `json:"mediaType"     orm:"media_type"`
	MediaUrl      string      `json:"mediaUrl"      orm:"media_url"`
	IsActive      int         `json:"isActive"      orm:"is_active"`
	CreatedAt     *gtime.Time `json:"createdAt"     orm:"created_at"`
	UpdatedAt     *gtime.Time `json:"updatedAt"     orm:"updated_at"`
}

// LoveQuizResult 爱情问答结果
type LoveQuizResult struct {
	Question     *LoveQuiz `json:"question"`
	Player1Score int       `json:"player1Score"`
	Player2Score int       `json:"player2Score"`
	CurrentTurn  int       `json:"currentTurn"`
	TotalRounds  int       `json:"totalRounds"`
	CurrentRound int       `json:"currentRound"`
	IsGameOver   bool      `json:"isGameOver"`
	Winner       string    `json:"winner"`
	Message      string    `json:"message"`
}

// WordChain 文字接龙词汇模型
type WordChain struct {
	Id         int         `json:"id"        orm:"id,primary"`
	Word       string      `json:"word"      orm:"word"`
	Category   string      `json:"category"  orm:"category"`
	Difficulty int         `json:"difficulty" orm:"difficulty"`
	FirstChar  string      `json:"firstChar" orm:"first_char"`
	LastChar   string      `json:"lastChar"  orm:"last_char"`
	Meaning    string      `json:"meaning"   orm:"meaning"`
	IsActive   int         `json:"isActive"  orm:"is_active"`
	CreatedAt  *gtime.Time `json:"createdAt" orm:"created_at"`
	UpdatedAt  *gtime.Time `json:"updatedAt" orm:"updated_at"`
}

// WordChainGame 文字接龙游戏模型
type WordChainGame struct {
	Id               int         `json:"id"               orm:"id,primary"`
	GameSession      string      `json:"gameSession"      orm:"game_session"`
	Player1Name      string      `json:"player1Name"      orm:"player1_name"`
	Player2Name      string      `json:"player2Name"      orm:"player2_name"`
	CurrentWord      string      `json:"currentWord"      orm:"current_word"`
	WordChainHistory string      `json:"wordChainHistory" orm:"word_chain_history"`
	CurrentPlayer    int         `json:"currentPlayer"    orm:"current_player"`
	Player1Score     int         `json:"player1Score"     orm:"player1_score"`
	Player2Score     int         `json:"player2Score"     orm:"player2_score"`
	TimeLimit        int         `json:"timeLimit"        orm:"time_limit"`
	IsFinished       int         `json:"isFinished"       orm:"is_finished"`
	Winner           string      `json:"winner"           orm:"winner"`
	CreatedAt        *gtime.Time `json:"createdAt"        orm:"created_at"`
	UpdatedAt        *gtime.Time `json:"updatedAt"        orm:"updated_at"`
}

// WordChainResult 文字接龙游戏结果
type WordChainResult struct {
	Game        *WordChainGame `json:"game"`
	CurrentWord string         `json:"currentWord"`
	ValidWords  []*WordChain   `json:"validWords"`
	TimeLeft    int            `json:"timeLeft"`
	IsGameOver  bool           `json:"isGameOver"`
	Winner      string         `json:"winner"`
	Message     string         `json:"message"`
	WordHistory []string       `json:"wordHistory"`
}

// LoveLetter 情话接力模型
type LoveLetter struct {
	Id            int         `json:"id"           orm:"id,primary"`
	GameSession   string      `json:"gameSession"  orm:"game_session"`
	Player1Name   string      `json:"player1Name"  orm:"player1_name"`
	Player2Name   string      `json:"player2Name"  orm:"player2_name"`
	Title         string      `json:"title"        orm:"title"`
	Content       string      `json:"content"      orm:"content"`
	CurrentPlayer int         `json:"currentPlayer" orm:"current_player"`
	SentenceCount int         `json:"sentenceCount" orm:"sentence_count"`
	MaxSentences  int         `json:"maxSentences" orm:"max_sentences"`
	IsFinished    int         `json:"isFinished"   orm:"is_finished"`
	CreatedAt     *gtime.Time `json:"createdAt"    orm:"created_at"`
	UpdatedAt     *gtime.Time `json:"updatedAt"    orm:"updated_at"`
}

// LoveLetterSentence 情话接力句子模型
type LoveLetterSentence struct {
	Id            int         `json:"id"            orm:"id,primary"`
	LetterId      int         `json:"letterId"      orm:"letter_id"`
	PlayerId      int         `json:"playerId"      orm:"player_id"`
	Sentence      string      `json:"sentence"      orm:"sentence"`
	SentenceOrder int         `json:"sentenceOrder" orm:"sentence_order"`
	CreatedAt     *gtime.Time `json:"createdAt"     orm:"created_at"`
}

// LoveLetterResult 情话接力游戏结果
type LoveLetterResult struct {
	Letter      *LoveLetter           `json:"letter"`
	Sentences   []*LoveLetterSentence `json:"sentences"`
	IsGameOver  bool                  `json:"isGameOver"`
	CurrentTurn string                `json:"currentTurn"`
	Message     string                `json:"message"`
	FullContent string                `json:"fullContent"`
}

// RolePlayCard 角色扮演卡片模型
type RolePlayCard struct {
	Id            int         `json:"id"            orm:"id,primary"`
	CharacterName string      `json:"characterName" orm:"character_name"`
	CharacterType string      `json:"characterType" orm:"character_type"`
	Description   string      `json:"description"   orm:"description"`
	Personality   string      `json:"personality"   orm:"personality"`
	Background    string      `json:"background"    orm:"background"`
	DialogueStyle string      `json:"dialogueStyle" orm:"dialogue_style"`
	Difficulty    int         `json:"difficulty"    orm:"difficulty"`
	IsActive      int         `json:"isActive"      orm:"is_active"`
	CreatedAt     *gtime.Time `json:"createdAt"     orm:"created_at"`
	UpdatedAt     *gtime.Time `json:"updatedAt"     orm:"updated_at"`
}

// RolePlayGame 角色扮演游戏模型
type RolePlayGame struct {
	Id                 int         `json:"id"                  orm:"id,primary"`
	GameSession        string      `json:"gameSession"         orm:"game_session"`
	Player1Name        string      `json:"player1Name"         orm:"player1_name"`
	Player2Name        string      `json:"player2Name"         orm:"player2_name"`
	Player1CharacterId int         `json:"player1CharacterId"  orm:"player1_character_id"`
	Player2CharacterId int         `json:"player2CharacterId"  orm:"player2_character_id"`
	Player1Score       int         `json:"player1Score"        orm:"player1_score"`
	Player2Score       int         `json:"player2Score"        orm:"player2_score"`
	DurationMinutes    int         `json:"durationMinutes"     orm:"duration_minutes"`
	IsFinished         int         `json:"isFinished"          orm:"is_finished"`
	CreatedAt          *gtime.Time `json:"createdAt"           orm:"created_at"`
	UpdatedAt          *gtime.Time `json:"updatedAt"           orm:"updated_at"`
}

// RolePlayResult 角色扮演游戏结果
type RolePlayResult struct {
	Game             *RolePlayGame `json:"game"`
	Player1Character *RolePlayCard `json:"player1Character"`
	Player2Character *RolePlayCard `json:"player2Character"`
	IsGameOver       bool          `json:"isGameOver"`
	Message          string        `json:"message"`
	TimeLeft         int           `json:"timeLeft"`
}

// FuturePrediction 未来预测模型
type FuturePrediction struct {
	Id               int         `json:"id"             orm:"id,primary"`
	GameSession      string      `json:"gameSession"    orm:"game_session"`
	PredictorName    string      `json:"predictorName"  orm:"predictor_name"`
	TargetName       string      `json:"targetName"     orm:"target_name"`
	PredictionText   string      `json:"predictionText" orm:"prediction_text"`
	PredictionDate   *gtime.Time `json:"predictionDate" orm:"prediction_date"`
	VerificationDate *gtime.Time `json:"verificationDate" orm:"verification_date"`
	IsCorrect        *int        `json:"isCorrect"      orm:"is_correct"`
	ActualResult     string      `json:"actualResult"   orm:"actual_result"`
	Points           int         `json:"points"         orm:"points"`
	CreatedAt        *gtime.Time `json:"createdAt"      orm:"created_at"`
	UpdatedAt        *gtime.Time `json:"updatedAt"      orm:"updated_at"`
}

// FuturePredictionResult 未来预测游戏结果
type FuturePredictionResult struct {
	Predictions []*FuturePrediction `json:"predictions"`
	TotalScore  int                 `json:"totalScore"`
	Message     string              `json:"message"`
}

// TreasureHunt 寻宝游戏模型
type TreasureHunt struct {
	Id             int         `json:"id"             orm:"id,primary"`
	GameSession    string      `json:"gameSession"    orm:"game_session"`
	CreatorName    string      `json:"creatorName"    orm:"creator_name"`
	HunterName     string      `json:"hunterName"     orm:"hunter_name"`
	Title          string      `json:"title"          orm:"title"`
	Description    string      `json:"description"    orm:"description"`
	TotalClues     int         `json:"totalClues"     orm:"total_clues"`
	CurrentClue    int         `json:"currentClue"    orm:"current_clue"`
	IsCompleted    int         `json:"isCompleted"    orm:"is_completed"`
	CompletionTime *gtime.Time `json:"completionTime" orm:"completion_time"`
	CreatedAt      *gtime.Time `json:"createdAt"      orm:"created_at"`
	UpdatedAt      *gtime.Time `json:"updatedAt"      orm:"updated_at"`
}

// TreasureClue 寻宝线索模型
type TreasureClue struct {
	Id           int         `json:"id"           orm:"id,primary"`
	HuntId       int         `json:"huntId"       orm:"hunt_id"`
	ClueOrder    int         `json:"clueOrder"    orm:"clue_order"`
	ClueType     string      `json:"clueType"     orm:"clue_type"`
	ClueContent  string      `json:"clueContent"  orm:"clue_content"`
	ClueImage    string      `json:"clueImage"    orm:"clue_image"`
	LocationHint string      `json:"locationHint" orm:"location_hint"`
	IsFound      int         `json:"isFound"      orm:"is_found"`
	FoundAt      *gtime.Time `json:"foundAt"      orm:"found_at"`
	CreatedAt    *gtime.Time `json:"createdAt"    orm:"created_at"`
}

// TreasureHuntResult 寻宝游戏结果
type TreasureHuntResult struct {
	Hunt        *TreasureHunt   `json:"hunt"`
	Clues       []*TreasureClue `json:"clues"`
	CurrentClue *TreasureClue   `json:"currentClue"`
	IsCompleted bool            `json:"isCompleted"`
	Message     string          `json:"message"`
}

// PhotoChallenge 主题摄影游戏模型
type PhotoChallenge struct {
	Id            int         `json:"id"            orm:"id,primary"`
	GameSession   string      `json:"gameSession"   orm:"game_session"`
	Player1Name   string      `json:"player1Name"   orm:"player1_name"`
	Player2Name   string      `json:"player2Name"   orm:"player2_name"`
	Theme         string      `json:"theme"         orm:"theme"`
	Description   string      `json:"description"   orm:"description"`
	Player1Photo  string      `json:"player1Photo"  orm:"player1_photo"`
	Player2Photo  string      `json:"player2Photo"  orm:"player2_photo"`
	Player1Score  int         `json:"player1Score"  orm:"player1_score"`
	Player2Score  int         `json:"player2Score"  orm:"player2_score"`
	VotingEnabled int         `json:"votingEnabled" orm:"voting_enabled"`
	IsFinished    int         `json:"isFinished"    orm:"is_finished"`
	Winner        string      `json:"winner"        orm:"winner"`
	CreatedAt     *gtime.Time `json:"createdAt"     orm:"created_at"`
	UpdatedAt     *gtime.Time `json:"updatedAt"     orm:"updated_at"`
}

// PhotoTheme 摄影主题模型
type PhotoTheme struct {
	Id          int         `json:"id"          orm:"id,primary"`
	ThemeName   string      `json:"themeName"   orm:"theme_name"`
	Description string      `json:"description" orm:"description"`
	Difficulty  int         `json:"difficulty"  orm:"difficulty"`
	Category    string      `json:"category"    orm:"category"`
	Tips        string      `json:"tips"        orm:"tips"`
	IsActive    int         `json:"isActive"    orm:"is_active"`
	CreatedAt   *gtime.Time `json:"createdAt"   orm:"created_at"`
	UpdatedAt   *gtime.Time `json:"updatedAt"   orm:"updated_at"`
}

// PhotoChallengeResult 主题摄影游戏结果
type PhotoChallengeResult struct {
	Challenge   *PhotoChallenge `json:"challenge"`
	Theme       *PhotoTheme     `json:"theme"`
	IsCompleted bool            `json:"isCompleted"`
	CanVote     bool            `json:"canVote"`
	Message     string          `json:"message"`
}

// MusicChain 音乐接龙游戏模型
type MusicChain struct {
	Id            int         `json:"id"            orm:"id,primary"`
	GameSession   string      `json:"gameSession"   orm:"game_session"`
	Player1Name   string      `json:"player1Name"   orm:"player1_name"`
	Player2Name   string      `json:"player2Name"   orm:"player2_name"`
	CurrentPlayer int         `json:"currentPlayer" orm:"current_player"`
	RoundNumber   int         `json:"roundNumber"   orm:"round_number"`
	MaxRounds     int         `json:"maxRounds"     orm:"max_rounds"`
	Player1Score  int         `json:"player1Score"  orm:"player1_score"`
	Player2Score  int         `json:"player2Score"  orm:"player2_score"`
	IsFinished    int         `json:"isFinished"    orm:"is_finished"`
	Winner        string      `json:"winner"        orm:"winner"`
	CreatedAt     *gtime.Time `json:"createdAt"     orm:"created_at"`
	UpdatedAt     *gtime.Time `json:"updatedAt"     orm:"updated_at"`
}

// MusicRound 音乐接龙回合模型
type MusicRound struct {
	Id           int         `json:"id"           orm:"id,primary"`
	GameId       int         `json:"gameId"       orm:"game_id"`
	RoundNumber  int         `json:"roundNumber"  orm:"round_number"`
	SingerName   string      `json:"singerName"   orm:"singer_name"`
	GuesserName  string      `json:"guesserName"  orm:"guesser_name"`
	AudioUrl     string      `json:"audioUrl"     orm:"audio_url"`
	SongTitle    string      `json:"songTitle"    orm:"song_title"`
	Artist       string      `json:"artist"       orm:"artist"`
	GuessedTitle string      `json:"guessedTitle" orm:"guessed_title"`
	IsCorrect    *int        `json:"isCorrect"    orm:"is_correct"`
	PointsEarned int         `json:"pointsEarned" orm:"points_earned"`
	CreatedAt    *gtime.Time `json:"createdAt"    orm:"created_at"`
}

// MusicChainResult 音乐接龙游戏结果
type MusicChainResult struct {
	Game         *MusicChain   `json:"game"`
	CurrentRound *MusicRound   `json:"currentRound"`
	Rounds       []*MusicRound `json:"rounds"`
	IsCompleted  bool          `json:"isCompleted"`
	CurrentTurn  string        `json:"currentTurn"`
	Message      string        `json:"message"`
	Action       string        `json:"action"` // "sing", "guess", "finished"
}

// GameRequest 游戏请求参数
type GameRequest struct {
	Player1Name string `json:"player1Name" v:"required|length:1,100#请输入玩家1姓名|玩家1姓名长度不能超过100字符"`
	Player2Name string `json:"player2Name" v:"required|length:1,100#请输入玩家2姓名|玩家2姓名长度不能超过100字符"`
	GameMode    string `json:"gameMode" v:"in:punishment_reward,slot_machine,truth_dare,dice_challenge,love_quiz,photo_challenge#游戏模式无效"`
	Level       int    `json:"level" v:"in:0,1,2,3#级别只能是0,1,2,3"`
	Type        int    `json:"type" v:"in:0,1,2#类型只能是0,1,2"`
}

// GameResult 游戏结果
type GameResult struct {
	Player1Name            string                  `json:"player1Name"`
	Player2Name            string                  `json:"player2Name"`
	GameMode               string                  `json:"gameMode"`
	SelectedPlayer         *int                    `json:"selectedPlayer"`
	SelectedItem           *PunishmentReward       `json:"selectedItem"`
	SlotResult             *SlotResult             `json:"slotResult"`
	TruthDareResult        *TruthDareResult        `json:"truthDareResult"`
	DiceResult             *DiceResult             `json:"diceResult"`
	LoveQuizResult         *LoveQuizResult         `json:"loveQuizResult"`
	WordChainResult        *WordChainResult        `json:"wordChainResult"`
	LoveLetterResult       *LoveLetterResult       `json:"loveLetterResult"`
	RolePlayResult         *RolePlayResult         `json:"rolePlayResult"`
	FuturePredictionResult *FuturePredictionResult `json:"futurePredictionResult"`
	TreasureHuntResult     *TreasureHuntResult     `json:"treasureHuntResult"`
	PhotoChallengeResult   *PhotoChallengeResult   `json:"photoChallengeResult"`
	MusicChainResult       *MusicChainResult       `json:"musicChainResult"`
	GameSession            string                  `json:"gameSession"`
}
