package model

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// PunishmentReward 奖惩内容模型
type PunishmentReward struct {
	Id        int         `json:"id"        orm:"id,primary"`
	Title     string      `json:"title"     orm:"title"`
	Content   string      `json:"content"   orm:"content"`
	Type      int         `json:"type"      orm:"type"`       // 1=奖励，2=惩罚
	MediaType int         `json:"mediaType" orm:"media_type"` // 0=无，1=图片，2=音频，3=视频
	MediaUrl  string      `json:"mediaUrl"  orm:"media_url"`
	Level     int         `json:"level"     orm:"level"`     // 1=轻微，2=一般，3=严重
	IsActive  int         `json:"isActive"  orm:"is_active"` // 0=禁用，1=启用
	CreatedAt *gtime.Time `json:"createdAt" orm:"created_at"`
	UpdatedAt *gtime.Time `json:"updatedAt" orm:"updated_at"`
}

// GameRecord 游戏记录模型
type GameRecord struct {
	Id             int         `json:"id"             orm:"id,primary"`
	Player1Name    string      `json:"player1Name"    orm:"player1_name"`
	Player2Name    string      `json:"player2Name"    orm:"player2_name"`
	GameModeId     int         `json:"gameModeId"     orm:"game_mode_id"`
	SelectedItemId *int        `json:"selectedItemId" orm:"selected_item_id"`
	SelectedPlayer *int        `json:"selectedPlayer" orm:"selected_player"` // 1=玩家1，2=玩家2
	SlotResult     string      `json:"slotResult"     orm:"slot_result"`     // JSON格式存储老虎机结果
	GameSession    string      `json:"gameSession"    orm:"game_session"`
	CreatedAt      *gtime.Time `json:"createdAt"      orm:"created_at"`
}

// GameMode 游戏模式模型
type GameMode struct {
	Id          int         `json:"id"          orm:"id,primary"`
	Name        string      `json:"name"        orm:"name"`
	DisplayName string      `json:"displayName" orm:"display_name"`
	Description string      `json:"description" orm:"description"`
	IsActive    int         `json:"isActive"    orm:"is_active"`
	CreatedAt   *gtime.Time `json:"createdAt"   orm:"created_at"`
	UpdatedAt   *gtime.Time `json:"updatedAt"   orm:"updated_at"`
}

// SlotSymbol 老虎机符号模型
type SlotSymbol struct {
	Id          int         `json:"id"          orm:"id,primary"`
	Name        string      `json:"name"        orm:"name"`
	DisplayName string      `json:"displayName" orm:"display_name"`
	Emoji       string      `json:"emoji"       orm:"emoji"`
	Color       string      `json:"color"       orm:"color"`
	Rarity      int         `json:"rarity"      orm:"rarity"`
	IsActive    int         `json:"isActive"    orm:"is_active"`
	CreatedAt   *gtime.Time `json:"createdAt"   orm:"created_at"`
	UpdatedAt   *gtime.Time `json:"updatedAt"   orm:"updated_at"`
}

// SlotCombination 老虎机组合模型
type SlotCombination struct {
	Id              int         `json:"id"              orm:"id,primary"`
	Name            string      `json:"name"            orm:"name"`
	Symbol1Id       int         `json:"symbol1Id"       orm:"symbol1_id"`
	Symbol2Id       int         `json:"symbol2Id"       orm:"symbol2_id"`
	Symbol3Id       int         `json:"symbol3Id"       orm:"symbol3_id"`
	ActivityTitle   string      `json:"activityTitle"   orm:"activity_title"`
	ActivityContent string      `json:"activityContent" orm:"activity_content"`
	ActivityType    int         `json:"activityType"    orm:"activity_type"`
	IntensityLevel  int         `json:"intensityLevel"  orm:"intensity_level"`
	MediaType       int         `json:"mediaType"       orm:"media_type"`
	MediaUrl        string      `json:"mediaUrl"        orm:"media_url"`
	IsActive        int         `json:"isActive"        orm:"is_active"`
	CreatedAt       *gtime.Time `json:"createdAt"       orm:"created_at"`
	UpdatedAt       *gtime.Time `json:"updatedAt"       orm:"updated_at"`
}

// SlotResult 老虎机结果
type SlotResult struct {
	Symbols     [3]*SlotSymbol   `json:"symbols"`
	Combination *SlotCombination `json:"combination"`
	IsWin       bool             `json:"isWin"`
	Message     string           `json:"message"`
}

// TruthDareQuestion 真心话大冒险问题模型
type TruthDareQuestion struct {
	Id         int         `json:"id"         orm:"id,primary"`
	Type       int         `json:"type"       orm:"type"`
	Title      string      `json:"title"      orm:"title"`
	Content    string      `json:"content"    orm:"content"`
	Difficulty int         `json:"difficulty" orm:"difficulty"`
	Category   string      `json:"category"   orm:"category"`
	IsActive   int         `json:"isActive"   orm:"is_active"`
	CreatedAt  *gtime.Time `json:"createdAt"  orm:"created_at"`
	UpdatedAt  *gtime.Time `json:"updatedAt"  orm:"updated_at"`
}

// DiceChallenge 浪漫骰子挑战模型
type DiceChallenge struct {
	Id              int         `json:"id"               orm:"id,primary"`
	Name            string      `json:"name"             orm:"name"`
	Description     string      `json:"description"      orm:"description"`
	DiceCombination string      `json:"diceCombination"  orm:"dice_combination"`
	ActivityTitle   string      `json:"activityTitle"    orm:"activity_title"`
	ActivityContent string      `json:"activityContent"  orm:"activity_content"`
	Difficulty      int         `json:"difficulty"       orm:"difficulty"`
	DurationMinutes int         `json:"durationMinutes"  orm:"duration_minutes"`
	RequiresProps   int         `json:"requiresProps"    orm:"requires_props"`
	PropsNeeded     string      `json:"propsNeeded"      orm:"props_needed"`
	IsActive        int         `json:"isActive"         orm:"is_active"`
	CreatedAt       *gtime.Time `json:"createdAt"        orm:"created_at"`
	UpdatedAt       *gtime.Time `json:"updatedAt"        orm:"updated_at"`
}

// TruthDareResult 真心话大冒险结果
type TruthDareResult struct {
	Question     *TruthDareQuestion `json:"question"`
	SelectedType int                `json:"selectedType"` // 1=真心话, 2=大冒险
	Message      string             `json:"message"`
}

// DiceResult 骰子游戏结果
type DiceResult struct {
	Dice1     int            `json:"dice1"`
	Dice2     int            `json:"dice2"`
	Sum       int            `json:"sum"`
	Challenge *DiceChallenge `json:"challenge"`
	Message   string         `json:"message"`
}

// GameRequest 游戏请求参数
type GameRequest struct {
	Player1Name string `json:"player1Name" v:"required|length:1,100#请输入玩家1姓名|玩家1姓名长度不能超过100字符"`
	Player2Name string `json:"player2Name" v:"required|length:1,100#请输入玩家2姓名|玩家2姓名长度不能超过100字符"`
	GameMode    string `json:"gameMode" v:"in:punishment_reward,slot_machine,truth_dare,dice_challenge#游戏模式无效"`
	Level       int    `json:"level" v:"in:0,1,2,3#级别只能是0,1,2,3"`
	Type        int    `json:"type" v:"in:0,1,2#类型只能是0,1,2"`
}

// GameResult 游戏结果
type GameResult struct {
	Player1Name     string            `json:"player1Name"`
	Player2Name     string            `json:"player2Name"`
	GameMode        string            `json:"gameMode"`
	SelectedPlayer  *int              `json:"selectedPlayer"`
	SelectedItem    *PunishmentReward `json:"selectedItem"`
	SlotResult      *SlotResult       `json:"slotResult"`
	TruthDareResult *TruthDareResult  `json:"truthDareResult"`
	DiceResult      *DiceResult       `json:"diceResult"`
	GameSession     string            `json:"gameSession"`
}
