package database

import (
	"context"
	"os"
	"strings"

	_ "github.com/gogf/gf/contrib/drivers/sqlite/v2"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gfile"
)

// InitDB 初始化数据库
func InitDB() error {
	ctx := context.Background()
	g.Log().Infof(ctx, "🔧 InitDB called")

	// 确保数据库目录存在
	dbDir := "./database"
	g.Log().Infof(ctx, "📁 Checking database directory: %s", dbDir)
	if !gfile.Exists(dbDir) {
		g.Log().Infof(ctx, "📁 Database directory does not exist, creating...")
		if err := gfile.Mkdir(dbDir); err != nil {
			return err
		}
		g.Log().Infof(ctx, "✅ Database directory created")
	} else {
		g.Log().Infof(ctx, "✅ Database directory exists")
	}

	// 检查数据库文件是否存在，如果不存在或者表不存在则重新初始化
	dbFile := "./database/lovergame.db"
	needInit := false

	if !gfile.Exists(dbFile) {
		g.Log().Infof(ctx, "Database file not exists, creating: %s", dbFile)
		// 创建数据库文件
		file, err := os.Create(dbFile)
		if err != nil {
			return err
		}
		file.Close()
		needInit = true
	} else {
		g.Log().Infof(ctx, "Database file exists, checking tables...")
		// 检查表是否存在
		if !checkTableExists(ctx) {
			g.Log().Infof(ctx, "Tables not exist, need to initialize")
			needInit = true
		}
	}

	if needInit {
		g.Log().Infof(ctx, "Initializing database...")
		// 执行初始化SQL
		err := executeSQLFile(ctx, "./sql/init_sqlite.sql")
		if err != nil {
			g.Log().Errorf(ctx, "Database initialization failed: %v", err)
			return err
		}
		g.Log().Infof(ctx, "Database initialization completed successfully")
		return nil
	}

	g.Log().Infof(ctx, "Database already initialized")
	return nil
}

// checkTableExists 检查表是否存在
func checkTableExists(ctx context.Context) bool {
	// 检查所有必要的表是否存在
	requiredTables := []string{
		"punishment_reward",
		"game_record",
		"game_modes",
		"slot_symbols",
		"slot_combinations",
		"truth_dare_questions",
		"dice_challenges",
		"game_settings",
		"love_quiz",
		"word_chain",
		"word_chain_game",
		"love_letter",
		"love_letter_sentence",
		"role_play_card",
		"role_play_game",
		"future_prediction",
		"treasure_hunt",
		"treasure_clue",
		"photo_challenge",
		"photo_theme",
		"music_chain",
		"music_round",
		"voice_challenge",
		"voice_prompt",
		"achievement",
		"user_achievement",
		"game_stats",
		"couple_profile",
		"anniversary",
		"couple_memory",
		"custom_content",
		"share_record",
		"game_analytics",
	}

	for _, tableName := range requiredTables {
		count, err := g.DB().GetCount(ctx, "SELECT count(*) FROM sqlite_master WHERE type='table' AND name=?", tableName)
		if err != nil {
			g.Log().Errorf(ctx, "Check table %s exists failed: %v", tableName, err)
			return false
		}
		if count == 0 {
			g.Log().Infof(ctx, "Table %s does not exist", tableName)
			return false
		}
	}

	g.Log().Infof(ctx, "All required tables exist")
	return true
}

// executeSQLFile 执行SQL文件
func executeSQLFile(ctx context.Context, sqlFile string) error {
	if !gfile.Exists(sqlFile) {
		g.Log().Warningf(ctx, "SQL file not found: %s", sqlFile)
		return nil
	}

	content := gfile.GetContents(sqlFile)
	if content == "" {
		g.Log().Warningf(ctx, "SQL file is empty: %s", sqlFile)
		return nil
	}

	// 分割SQL语句并逐个执行
	statements := parseSQLStatements(content)
	for _, statement := range statements {
		statement = strings.TrimSpace(statement)
		if statement == "" {
			continue
		}

		g.Log().Debugf(ctx, "Executing SQL: %s", statement)
		_, err := g.DB().Exec(ctx, statement)
		if err != nil {
			g.Log().Errorf(ctx, "Execute SQL failed: %s, Error: %v", statement, err)
			return err
		}
	}

	g.Log().Infof(ctx, "Database initialized successfully from %s", sqlFile)
	return nil
}

// parseSQLStatements 解析SQL语句，正确处理多行语句
func parseSQLStatements(content string) []string {
	var statements []string
	var currentStatement strings.Builder

	lines := strings.Split(content, "\n")
	for _, line := range lines {
		line = strings.TrimSpace(line)

		// 跳过空行和注释行
		if line == "" || strings.HasPrefix(line, "--") {
			continue
		}

		currentStatement.WriteString(line)
		currentStatement.WriteString(" ")

		// 如果行以分号结尾，表示语句结束
		if strings.HasSuffix(line, ";") {
			stmt := strings.TrimSpace(currentStatement.String())
			if stmt != "" {
				// 移除末尾的分号
				stmt = strings.TrimSuffix(stmt, ";")
				statements = append(statements, stmt)
			}
			currentStatement.Reset()
		}
	}

	// 处理最后一个语句（如果没有以分号结尾）
	if currentStatement.Len() > 0 {
		stmt := strings.TrimSpace(currentStatement.String())
		if stmt != "" {
			statements = append(statements, stmt)
		}
	}

	return statements
}
