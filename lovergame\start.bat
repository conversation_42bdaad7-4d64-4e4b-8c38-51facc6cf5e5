@echo off
title Lover Game Launcher

echo ==========================================
echo           Double Interactive Game
echo ==========================================
echo.

REM Check if Go is installed
go version >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] Go is not installed!
    echo.
    echo Please install Go by following these steps:
    echo 1. Visit https://golang.org/dl/
    echo 2. Download the Windows installer
    echo 3. Run the installer and follow the wizard
    echo 4. Restart command prompt and run this script again
    echo.
    echo Or check INSTALL.md for detailed installation guide
    echo.
    pause
    exit /b 1
)

echo [INFO] Go environment detected:
go version
echo.

REM Check if we are in the correct directory
if not exist "main.go" (
    echo [ERROR] main.go file not found!
    echo Please make sure you are running this script in the project root directory
    echo.
    pause
    exit /b 1
)

REM Download dependencies
echo [INFO] Downloading project dependencies...
go mod tidy
if %errorlevel% neq 0 (
    echo [ERROR] Failed to download dependencies!
    echo Possible solutions:
    echo 1. Check your internet connection
    echo 2. Set Go proxy: go env -w GOPROXY=https://goproxy.cn,direct
    echo 3. Clear cache: go clean -modcache
    echo.
    pause
    exit /b 1
)

echo [INFO] Dependencies downloaded successfully
echo.

REM Build the application
echo [INFO] Building the application...
go build -o lovergame.exe .
if %errorlevel% neq 0 (
    echo [ERROR] Build failed!
    echo Please check the error messages above.
    echo.
    pause
    exit /b 1
)

echo [INFO] Build successful!
echo.

REM Start the application
echo [INFO] Starting the application...
echo [INFO] Please visit: http://localhost:8080
echo [INFO] Admin panel: http://localhost:8080/admin
echo [INFO] Test API: http://localhost:8080/test_api.html
echo.
echo Press Ctrl+C to stop the server
echo ==========================================
echo.

.\lovergame.exe

echo.
echo [INFO] Server stopped.
pause