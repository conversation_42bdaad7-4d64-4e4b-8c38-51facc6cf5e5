package controller

import (
	"context"
	"encoding/json"
	"lovergame/app/model"
	"lovergame/app/service"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/ghttp"
	"github.com/gogf/gf/v2/util/gconv"
)

// AdminController 后台管理控制器
type AdminController struct{}

func NewAdminController() *AdminController {
	return &AdminController{}
}

// Login 后台登录页面
func (c *AdminController) Login(r *ghttp.Request) {
	if r.Method == "GET" {
		r.Response.WriteTpl("admin/login.html", g.Map{
			"title": "后台管理登录",
		})
		return
	}

	// POST 请求处理登录
	password := r.Get("password").String()
	if password == "loveislove" {
		// 设置会话
		r.Session.Set("admin_logged_in", true)
		r.Response.WriteJsonExit(g.Map{
			"code": 0,
			"msg":  "登录成功",
		})
	} else {
		r.Response.WriteJsonExit(g.Map{
			"code": 400,
			"msg":  "密码错误",
		})
	}
}

// Logout 后台登出
func (c *AdminController) Logout(r *ghttp.Request) {
	r.Session.Remove("admin_logged_in")
	r.Response.RedirectTo("/admin/login")
}

// checkAuth 检查管理员权限
func (c *AdminController) checkAuth(r *ghttp.Request) bool {
	value, _ := r.Session.Get("admin_logged_in")
	return value.Bool()
}

// Index 后台首页
func (c *AdminController) Index(r *ghttp.Request) {
	if !c.checkAuth(r) {
		r.Response.RedirectTo("/admin/login")
		return
	}

	r.Response.WriteTpl("admin/index.html", g.Map{
		"title": "奖惩游戏后台管理",
	})
}

// PunishmentRewardList 奖惩内容列表
func (c *AdminController) PunishmentRewardList(r *ghttp.Request) {
	if !c.checkAuth(r) {
		r.Response.WriteJsonExit(g.Map{
			"code": 401,
			"msg":  "未授权访问",
		})
	}

	page := gconv.Int(r.Get("page", 1))
	limit := gconv.Int(r.Get("limit", 10))
	searchType := gconv.Int(r.Get("type", 0))

	list, total, err := service.PunishmentReward().GetList(r.Context(), page, limit, searchType)
	if err != nil {
		r.Response.WriteJsonExit(g.Map{
			"code": 500,
			"msg":  err.Error(),
		})
	}

	r.Response.WriteJsonExit(g.Map{
		"code":  0,
		"msg":   "success",
		"count": total,
		"data":  list,
	})
}

// PunishmentRewardAdd 添加奖惩内容
func (c *AdminController) PunishmentRewardAdd(r *ghttp.Request) {
	if !c.checkAuth(r) {
		if r.Method == "GET" {
			r.Response.RedirectTo("/admin/login")
		} else {
			r.Response.WriteJsonExit(g.Map{
				"code": 401,
				"msg":  "未授权访问",
			})
		}
		return
	}

	if r.Method == "GET" {
		r.Response.WriteTpl("admin/punishment_reward_add.html", nil)
		return
	}

	var req *model.PunishmentReward
	if err := r.Parse(&req); err != nil {
		r.Response.WriteJsonExit(g.Map{
			"code": 400,
			"msg":  err.Error(),
		})
	}

	if err := service.PunishmentReward().Create(r.Context(), req); err != nil {
		r.Response.WriteJsonExit(g.Map{
			"code": 500,
			"msg":  err.Error(),
		})
	}

	r.Response.WriteJsonExit(g.Map{
		"code": 0,
		"msg":  "添加成功",
	})
}

// PunishmentRewardEdit 编辑奖惩内容
func (c *AdminController) PunishmentRewardEdit(r *ghttp.Request) {
	if !c.checkAuth(r) {
		if r.Method == "GET" {
			r.Response.RedirectTo("/admin/login")
		} else {
			r.Response.WriteJsonExit(g.Map{
				"code": 401,
				"msg":  "未授权访问",
			})
		}
		return
	}

	id := gconv.Int(r.Get("id"))

	if r.Method == "GET" {
		item, err := service.PunishmentReward().GetById(r.Context(), id)
		if err != nil {
			// 如果是AJAX请求，返回JSON错误
			if r.Header.Get("Content-Type") == "application/json" ||
				r.Header.Get("Accept") == "application/json" ||
				r.Header.Get("X-Requested-With") == "XMLHttpRequest" {
				r.Response.WriteJsonExit(g.Map{
					"code": 404,
					"msg":  "内容不存在",
				})
			}
			r.Response.WriteStatusExit(404, "内容不存在")
		}

		// 如果是AJAX请求，返回JSON数据
		if r.Header.Get("Content-Type") == "application/json" ||
			r.Header.Get("Accept") == "application/json" ||
			r.Header.Get("X-Requested-With") == "XMLHttpRequest" {
			r.Response.WriteJsonExit(g.Map{
				"code": 0,
				"msg":  "success",
				"data": item,
			})
		}

		// 否则返回HTML模板
		r.Response.WriteTpl("admin/punishment_reward_edit.html", g.Map{
			"item": item,
		})
		return
	}

	var req *model.PunishmentReward
	if err := r.Parse(&req); err != nil {
		r.Response.WriteJsonExit(g.Map{
			"code": 400,
			"msg":  err.Error(),
		})
	}

	if err := service.PunishmentReward().Update(r.Context(), id, req); err != nil {
		r.Response.WriteJsonExit(g.Map{
			"code": 500,
			"msg":  err.Error(),
		})
	}

	r.Response.WriteJsonExit(g.Map{
		"code": 0,
		"msg":  "更新成功",
	})
}

// PunishmentRewardDelete 删除奖惩内容
func (c *AdminController) PunishmentRewardDelete(r *ghttp.Request) {
	if !c.checkAuth(r) {
		r.Response.WriteJsonExit(g.Map{
			"code": 401,
			"msg":  "未授权访问",
		})
		return
	}

	id := gconv.Int(r.Get("id"))

	if err := service.PunishmentReward().Delete(r.Context(), id); err != nil {
		r.Response.WriteJsonExit(g.Map{
			"code": 500,
			"msg":  err.Error(),
		})
	}

	r.Response.WriteJsonExit(g.Map{
		"code": 0,
		"msg":  "删除成功",
	})
}

// Upload 文件上传
func (c *AdminController) Upload(r *ghttp.Request) {
	if !c.checkAuth(r) {
		r.Response.WriteJsonExit(g.Map{
			"code": 401,
			"msg":  "未授权访问",
		})
		return
	}

	file := r.GetUploadFile("file")
	if file == nil {
		r.Response.WriteJsonExit(g.Map{
			"code": 400,
			"msg":  "请选择文件",
		})
	}

	// 根据文件类型确定上传目录
	var uploadDir string
	var urlPath string
	contentType := file.Header.Get("Content-Type")
	switch {
	case contentType[:5] == "image":
		uploadDir = "resource/public/upload/images/"
		urlPath = "upload/images/"
	case contentType[:5] == "audio":
		uploadDir = "resource/public/upload/audios/"
		urlPath = "upload/audios/"
	case contentType[:5] == "video":
		uploadDir = "resource/public/upload/videos/"
		urlPath = "upload/videos/"
	default:
		r.Response.WriteJsonExit(g.Map{
			"code": 400,
			"msg":  "不支持的文件类型",
		})
	}

	filename, err := file.Save(uploadDir, true)
	if err != nil {
		r.Response.WriteJsonExit(g.Map{
			"code": 500,
			"msg":  err.Error(),
		})
	}

	r.Response.WriteJsonExit(g.Map{
		"code": 0,
		"msg":  "上传成功",
		"data": g.Map{
			"url": urlPath + filename,
		},
	})
}

// GameController 游戏控制器
type GameController struct{}

func NewGameController() *GameController {
	return &GameController{}
}

// Index 游戏首页
func (c *GameController) Index(r *ghttp.Request) {
	r.Response.WriteTpl("game/index.html", g.Map{
		"title": "双人互动奖惩游戏",
	})
}

// Play 开始游戏
func (c *GameController) Play(r *ghttp.Request) {
	g.Log().Infof(r.Context(), "🎮 Play endpoint called")
	g.Log().Infof(r.Context(), "📡 Request method: %s", r.Method)
	g.Log().Infof(r.Context(), "📋 Request body: %s", string(r.GetBody()))

	var req *model.GameRequest
	if err := r.Parse(&req); err != nil {
		g.Log().Errorf(r.Context(), "❌ Failed to parse request: %v", err)
		r.Response.WriteJsonExit(g.Map{
			"code": 400,
			"msg":  err.Error(),
		})
		return
	}

	g.Log().Infof(r.Context(), "✅ Request parsed successfully: %+v", req)

	g.Log().Infof(r.Context(), "🎯 Calling service.Game().PlayGame...")
	result, err := service.Game().PlayGame(r.Context(), req)
	if err != nil {
		g.Log().Errorf(r.Context(), "❌ service.Game().PlayGame failed: %v", err)
		r.Response.WriteJsonExit(g.Map{
			"code": 500,
			"msg":  err.Error(),
		})
		return
	}
	g.Log().Infof(r.Context(), "✅ service.Game().PlayGame succeeded")

	r.Response.WriteJsonExit(g.Map{
		"code": 0,
		"msg":  "success",
		"data": result,
	})
}

// ContinueGame 继续游戏
func (c *GameController) ContinueGame(r *ghttp.Request) {
	g.Log().Infof(r.Context(), "🎮 ContinueGame endpoint called")

	var req struct {
		GameSession string `json:"gameSession" v:"required#请提供游戏会话ID"`
		GameMode    string `json:"gameMode" v:"required#请提供游戏模式"`
	}

	if err := r.Parse(&req); err != nil {
		g.Log().Errorf(r.Context(), "❌ Failed to parse continue game request: %v", err)
		r.Response.WriteJsonExit(g.Map{
			"code": 400,
			"msg":  err.Error(),
		})
		return
	}

	g.Log().Infof(r.Context(), "✅ Continue game request parsed: %+v", req)

	// 根据游戏模式调用相应的继续游戏方法
	switch req.GameMode {
	case "love_quiz":
		g.Log().Infof(r.Context(), "💕 Continuing love quiz game")
		result, err := service.LoveQuiz().ContinueQuizGame(r.Context(), req.GameSession)
		if err != nil {
			g.Log().Errorf(r.Context(), "❌ Failed to continue love quiz: %v", err)
			r.Response.WriteJsonExit(g.Map{
				"code": 500,
				"msg":  err.Error(),
			})
			return
		}

		g.Log().Infof(r.Context(), "✅ Love quiz continued successfully")

		// 包装成 GameResult 结构以匹配前端期望
		gameResult := &model.GameResult{
			Player1Name:    result.Player1Name,
			Player2Name:    result.Player2Name,
			GameMode:       "love_quiz",
			LoveQuizResult: result,
			GameSession:    result.GameSession,
		}

		r.Response.WriteJsonExit(g.Map{
			"code": 0,
			"msg":  "success",
			"data": gameResult,
		})

	case "punishment_reward", "slot_machine", "truth_dare", "dice_challenge":
		g.Log().Infof(r.Context(), "🎮 Continuing %s game", req.GameMode)

		// 对于这些游戏模式，继续游戏就是重新开始一轮
		// 从游戏记录中获取玩家信息
		var gameRecord struct {
			Player1Name string `json:"player1_name"`
			Player2Name string `json:"player2_name"`
		}

		err := g.DB().Model("game_record").Where("game_session", req.GameSession).Scan(&gameRecord)
		if err != nil {
			g.Log().Errorf(r.Context(), "❌ Failed to get game record: %v", err)
			r.Response.WriteJsonExit(g.Map{
				"code": 500,
				"msg":  "游戏会话不存在或已过期",
			})
			return
		}

		// 创建新的游戏请求
		gameRequest := &model.GameRequest{
			Player1Name: gameRecord.Player1Name,
			Player2Name: gameRecord.Player2Name,
			GameMode:    req.GameMode,
		}

		// 调用游戏服务重新开始
		result, err := service.Game().PlayGame(r.Context(), gameRequest)
		if err != nil {
			g.Log().Errorf(r.Context(), "❌ Failed to continue %s game: %v", req.GameMode, err)
			r.Response.WriteJsonExit(g.Map{
				"code": 500,
				"msg":  err.Error(),
			})
			return
		}

		g.Log().Infof(r.Context(), "✅ %s game continued successfully", req.GameMode)
		r.Response.WriteJsonExit(g.Map{
			"code": 0,
			"msg":  "success",
			"data": result,
		})

	case "photo_challenge":
		g.Log().Infof(r.Context(), "📸 Continuing photo challenge game")

		// 主题摄影需要检查当前状态
		challengeResult, err := service.PhotoChallenge().GetPhotoChallengeBySession(r.Context(), req.GameSession)
		if err != nil {
			g.Log().Errorf(r.Context(), "❌ Failed to get photo challenge: %v", err)
			r.Response.WriteJsonExit(g.Map{
				"code": 500,
				"msg":  "游戏会话不存在或已过期",
			})
			return
		}

		// 包装成 GameResult 结构
		gameResult := &model.GameResult{
			Player1Name:          challengeResult.Challenge.Player1Name,
			Player2Name:          challengeResult.Challenge.Player2Name,
			GameMode:             "photo_challenge",
			PhotoChallengeResult: challengeResult,
			GameSession:          challengeResult.Challenge.GameSession,
		}

		g.Log().Infof(r.Context(), "✅ Photo challenge continued successfully")
		r.Response.WriteJsonExit(g.Map{
			"code": 0,
			"msg":  "success",
			"data": gameResult,
		})

	default:
		g.Log().Errorf(r.Context(), "❌ Unsupported game mode for continue: %s", req.GameMode)
		r.Response.WriteJsonExit(g.Map{
			"code": 400,
			"msg":  "该游戏模式暂不支持继续游戏功能",
		})
	}
}

// GetPresetList 获取预设列表
func (c *GameController) GetPresetList(r *ghttp.Request) {
	category := r.Get("category", "").String()

	presets, err := service.GamePreset().GetPresetList(r.Context(), category)
	if err != nil {
		r.Response.WriteJsonExit(g.Map{
			"code": 500,
			"msg":  "获取预设列表失败: " + err.Error(),
		})
		return
	}

	r.Response.WriteJsonExit(g.Map{
		"code": 0,
		"msg":  "success",
		"data": presets,
	})
}

// ApplyPreset 应用预设
func (c *GameController) ApplyPreset(r *ghttp.Request) {
	var req struct {
		PresetId int `json:"presetId" v:"required|min:1#请选择预设|预设ID无效"`
	}

	if err := r.Parse(&req); err != nil {
		r.Response.WriteJsonExit(g.Map{
			"code": 400,
			"msg":  err.Error(),
		})
		return
	}

	settings, err := service.GamePreset().ApplyPreset(r.Context(), req.PresetId)
	if err != nil {
		r.Response.WriteJsonExit(g.Map{
			"code": 500,
			"msg":  "应用预设失败: " + err.Error(),
		})
		return
	}

	r.Response.WriteJsonExit(g.Map{
		"code": 0,
		"msg":  "预设应用成功",
		"data": settings,
	})
}

// SaveAsPreset 保存当前设置为预设
func (c *GameController) SaveAsPreset(r *ghttp.Request) {
	var req struct {
		Name        string                 `json:"name" v:"required|length:1,50#请输入预设名称|预设名称长度为1-50个字符"`
		Description string                 `json:"description" v:"required|length:1,200#请输入预设描述|预设描述长度为1-200个字符"`
		Settings    map[string]interface{} `json:"settings" v:"required#请提供游戏设置"`
		CreatedBy   string                 `json:"createdBy"`
	}

	if err := r.Parse(&req); err != nil {
		r.Response.WriteJsonExit(g.Map{
			"code": 400,
			"msg":  err.Error(),
		})
		return
	}

	err := service.GamePreset().SaveCurrentAsPreset(r.Context(), req.Name, req.Description, req.Settings, req.CreatedBy)
	if err != nil {
		r.Response.WriteJsonExit(g.Map{
			"code": 500,
			"msg":  "保存预设失败: " + err.Error(),
		})
		return
	}

	r.Response.WriteJsonExit(g.Map{
		"code": 0,
		"msg":  "预设保存成功",
	})
}

// SubmitPhoto 提交照片
func (c *GameController) SubmitPhoto(r *ghttp.Request) {
	g.Log().Infof(r.Context(), "📸 SubmitPhoto endpoint called")

	var req struct {
		GameSession string `json:"gameSession" v:"required#请提供游戏会话ID"`
		PlayerName  string `json:"playerName" v:"required#请提供玩家姓名"`
		PhotoUrl    string `json:"photoUrl" v:"required#请提供照片URL"`
		GameMode    string `json:"gameMode" v:"required#请提供游戏模式"`
	}

	if err := r.Parse(&req); err != nil {
		g.Log().Errorf(r.Context(), "❌ Failed to parse submit photo request: %v", err)
		r.Response.WriteJsonExit(g.Map{
			"code": 400,
			"msg":  err.Error(),
		})
		return
	}

	g.Log().Infof(r.Context(), "✅ Submit photo request parsed: %+v", req)

	// 根据游戏模式调用相应的提交照片方法
	switch req.GameMode {
	case "photo_challenge":
		g.Log().Infof(r.Context(), "📸 Submitting photo for photo challenge")
		result, err := service.PhotoChallenge().SubmitPhoto(r.Context(), req.GameSession, req.PlayerName, req.PhotoUrl)
		if err != nil {
			g.Log().Errorf(r.Context(), "❌ Failed to submit photo: %v", err)
			r.Response.WriteJsonExit(g.Map{
				"code": 500,
				"msg":  err.Error(),
			})
			return
		}

		g.Log().Infof(r.Context(), "✅ Photo submitted successfully")

		// 包装成 GameResult 结构以匹配前端期望
		gameResult := &model.GameResult{
			Player1Name:          result.Challenge.Player1Name,
			Player2Name:          result.Challenge.Player2Name,
			GameMode:             "photo_challenge",
			PhotoChallengeResult: result,
			GameSession:          result.Challenge.GameSession,
		}

		r.Response.WriteJsonExit(g.Map{
			"code": 0,
			"msg":  "照片提交成功",
			"data": gameResult,
		})

	default:
		g.Log().Errorf(r.Context(), "❌ Unsupported game mode for photo submit: %s", req.GameMode)
		r.Response.WriteJsonExit(g.Map{
			"code": 400,
			"msg":  "该游戏模式不支持照片提交功能",
		})
	}
}

// History 游戏历史记录
func (c *GameController) History(r *ghttp.Request) {
	page := gconv.Int(r.Get("page", 1))
	limit := gconv.Int(r.Get("limit", 10))

	list, total, err := service.Game().GetGameHistory(r.Context(), page, limit)
	if err != nil {
		r.Response.WriteJsonExit(g.Map{
			"code": 500,
			"msg":  err.Error(),
		})
	}

	r.Response.WriteJsonExit(g.Map{
		"code":  0,
		"msg":   "success",
		"count": total,
		"data":  list,
	})
}

// TestSlot 测试老虎机数据
func (c *GameController) TestSlot(r *ghttp.Request) {
	symbols, err := service.SlotMachine().GetActiveSymbols(r.Context())
	if err != nil {
		r.Response.WriteJsonExit(g.Map{
			"code": 500,
			"msg":  "获取符号失败: " + err.Error(),
		})
		return
	}

	combinations, err := service.SlotMachine().GetAllCombinations(r.Context())
	if err != nil {
		r.Response.WriteJsonExit(g.Map{
			"code": 500,
			"msg":  "获取组合失败: " + err.Error(),
		})
		return
	}

	r.Response.WriteJsonExit(g.Map{
		"code": 0,
		"msg":  "success",
		"data": g.Map{
			"symbols":      symbols,
			"combinations": combinations,
			"symbolCount":  len(symbols),
			"comboCount":   len(combinations),
		},
	})
}

// CreateRoom 创建房间
func (c *GameController) CreateRoom(r *ghttp.Request) {
	var req struct {
		Name      string `json:"name"` // 房间名字，可选
		CreatedBy string `json:"createdBy" v:"required#请输入创建者姓名"`
		Type      int    `json:"type" v:"in:0,1,2#类型只能是0,1,2"`
		Level     int    `json:"level" v:"in:0,1,2,3#级别只能是0,1,2,3"`
	}

	if err := r.Parse(&req); err != nil {
		r.Response.WriteJsonExit(g.Map{
			"code": 400,
			"msg":  err.Error(),
		})
	}

	settings := &model.GameRequest{
		Type:  req.Type,
		Level: req.Level,
	}

	room, err := service.RoomService().CreateRoom(r.Context(), req.Name, req.CreatedBy, settings)
	if err != nil {
		r.Response.WriteJsonExit(g.Map{
			"code": 500,
			"msg":  err.Error(),
		})
	}

	r.Response.WriteJsonExit(g.Map{
		"code": 0,
		"msg":  "房间创建成功",
		"data": room,
	})
}

// JoinRoom 加入房间
func (c *GameController) JoinRoom(r *ghttp.Request) {
	var req struct {
		RoomID     string `json:"roomId" v:"required#请输入房间ID"`
		PlayerName string `json:"playerName" v:"required#请输入玩家姓名"`
	}

	if err := r.Parse(&req); err != nil {
		r.Response.WriteJsonExit(g.Map{
			"code": 400,
			"msg":  err.Error(),
		})
	}

	room, err := service.RoomService().JoinRoom(r.Context(), req.RoomID, req.PlayerName)
	if err != nil {
		r.Response.WriteJsonExit(g.Map{
			"code": 500,
			"msg":  err.Error(),
		})
	}

	r.Response.WriteJsonExit(g.Map{
		"code": 0,
		"msg":  "加入房间成功",
		"data": room,
	})
}

// GetRoom 获取房间信息
func (c *GameController) GetRoom(r *ghttp.Request) {
	roomID := r.Get("roomId").String()
	if roomID == "" {
		r.Response.WriteJsonExit(g.Map{
			"code": 400,
			"msg":  "请提供房间ID",
		})
	}

	room, err := service.RoomService().GetRoom(r.Context(), roomID)
	if err != nil {
		r.Response.WriteJsonExit(g.Map{
			"code": 404,
			"msg":  err.Error(),
		})
	}

	r.Response.WriteJsonExit(g.Map{
		"code": 0,
		"msg":  "success",
		"data": room,
	})
}

// StartRoomGame 开始房间游戏
func (c *GameController) StartRoomGame(r *ghttp.Request) {
	roomID := r.Get("roomId").String()
	if roomID == "" {
		r.Response.WriteJsonExit(g.Map{
			"code": 400,
			"msg":  "请提供房间ID",
		})
	}

	result, err := service.RoomService().StartGame(r.Context(), roomID)
	if err != nil {
		r.Response.WriteJsonExit(g.Map{
			"code": 500,
			"msg":  err.Error(),
		})
	}

	r.Response.WriteJsonExit(g.Map{
		"code": 0,
		"msg":  "游戏开始",
		"data": result,
	})
}

// ListRooms 获取房间列表
func (c *GameController) ListRooms(r *ghttp.Request) {
	rooms, err := service.RoomService().ListRooms(r.Context())
	if err != nil {
		r.Response.WriteJsonExit(g.Map{
			"code": 500,
			"msg":  err.Error(),
		})
	}

	r.Response.WriteJsonExit(g.Map{
		"code": 0,
		"msg":  "success",
		"data": rooms,
	})
}

// PhotoThemeList 主题摄影列表
func (c *AdminController) PhotoThemeList(r *ghttp.Request) {
	if !c.checkAuth(r) {
		r.Response.WriteJsonExit(g.Map{
			"code": 401,
			"msg":  "未授权访问",
		})
		return
	}

	page := gconv.Int(r.Get("page", 1))
	limit := gconv.Int(r.Get("limit", 10))

	list, total, err := service.PhotoChallenge().GetPhotoThemeList(r.Context(), page, limit)
	if err != nil {
		r.Response.WriteJsonExit(g.Map{
			"code": 500,
			"msg":  err.Error(),
		})
		return
	}

	r.Response.WriteJsonExit(g.Map{
		"code": 0,
		"msg":  "success",
		"data": g.Map{
			"list":  list,
			"total": total,
		},
	})
}

// PhotoThemeAdd 添加主题摄影
func (c *AdminController) PhotoThemeAdd(r *ghttp.Request) {
	if !c.checkAuth(r) {
		r.Response.WriteJsonExit(g.Map{
			"code": 401,
			"msg":  "未授权访问",
		})
		return
	}

	var req *model.PhotoTheme
	if err := r.Parse(&req); err != nil {
		r.Response.WriteJsonExit(g.Map{
			"code": 400,
			"msg":  err.Error(),
		})
		return
	}

	if err := service.PhotoChallenge().AddPhotoTheme(r.Context(), req); err != nil {
		r.Response.WriteJsonExit(g.Map{
			"code": 500,
			"msg":  err.Error(),
		})
		return
	}

	r.Response.WriteJsonExit(g.Map{
		"code": 0,
		"msg":  "添加成功",
	})
}

// PhotoThemeEdit 编辑主题摄影
func (c *AdminController) PhotoThemeEdit(r *ghttp.Request) {
	if !c.checkAuth(r) {
		r.Response.WriteJsonExit(g.Map{
			"code": 401,
			"msg":  "未授权访问",
		})
		return
	}

	id := gconv.Int(r.Get("id"))
	if id == 0 {
		r.Response.WriteJsonExit(g.Map{
			"code": 400,
			"msg":  "请提供有效的ID",
		})
		return
	}

	if r.Method == "GET" {
		// 获取单个主题信息（这里需要实现GetPhotoThemeById方法）
		r.Response.WriteJsonExit(g.Map{
			"code": 0,
			"msg":  "success",
			"data": g.Map{}, // 暂时返回空数据
		})
		return
	}

	var req *model.PhotoTheme
	if err := r.Parse(&req); err != nil {
		r.Response.WriteJsonExit(g.Map{
			"code": 400,
			"msg":  err.Error(),
		})
		return
	}

	if err := service.PhotoChallenge().UpdatePhotoTheme(r.Context(), id, req); err != nil {
		r.Response.WriteJsonExit(g.Map{
			"code": 500,
			"msg":  err.Error(),
		})
		return
	}

	r.Response.WriteJsonExit(g.Map{
		"code": 0,
		"msg":  "更新成功",
	})
}

// PhotoThemeDelete 删除主题摄影
func (c *AdminController) PhotoThemeDelete(r *ghttp.Request) {
	if !c.checkAuth(r) {
		r.Response.WriteJsonExit(g.Map{
			"code": 401,
			"msg":  "未授权访问",
		})
		return
	}

	id := gconv.Int(r.Get("id"))
	if id == 0 {
		r.Response.WriteJsonExit(g.Map{
			"code": 400,
			"msg":  "请提供有效的ID",
		})
		return
	}

	if err := service.PhotoChallenge().DeletePhotoTheme(r.Context(), id); err != nil {
		r.Response.WriteJsonExit(g.Map{
			"code": 500,
			"msg":  err.Error(),
		})
		return
	}

	r.Response.WriteJsonExit(g.Map{
		"code": 0,
		"msg":  "删除成功",
	})
}

// GetPresetsAdmin 后台获取预设列表
func (c *GameController) GetPresetsAdmin(r *ghttp.Request) {
	category := r.Get("category", "").String()

	presets, err := service.GamePreset().GetPresetList(r.Context(), category)
	if err != nil {
		r.Response.WriteJsonExit(g.Map{
			"code": 500,
			"msg":  "获取预设列表失败: " + err.Error(),
		})
		return
	}

	r.Response.WriteJsonExit(g.Map{
		"code": 0,
		"msg":  "success",
		"data": presets,
	})
}

// CreatePresetAdmin 后台创建预设
func (c *GameController) CreatePresetAdmin(r *ghttp.Request) {
	var req struct {
		Name             string `json:"name" v:"required|length:1,50#请输入预设名称|预设名称长度为1-50个字符"`
		Description      string `json:"description" v:"required|length:1,200#请输入预设描述|预设描述长度为1-200个字符"`
		Category         string `json:"category" v:"required#请选择分类"`
		RelationshipType string `json:"relationshipType" v:"required#请输入关系类型"`
		IntimacyLevel    int    `json:"intimacyLevel" v:"required|between:1,6#请选择亲密程度|亲密程度必须在1-6之间"`
		Settings         string `json:"settings" v:"required#请输入游戏设置"`
		IsSystem         int    `json:"isSystem"`
		IsActive         int    `json:"isActive"`
	}

	if err := r.Parse(&req); err != nil {
		r.Response.WriteJsonExit(g.Map{
			"code": 400,
			"msg":  err.Error(),
		})
		return
	}

	// 验证JSON格式
	var settings map[string]interface{}
	if err := json.Unmarshal([]byte(req.Settings), &settings); err != nil {
		r.Response.WriteJsonExit(g.Map{
			"code": 400,
			"msg":  "游戏设置JSON格式错误: " + err.Error(),
		})
		return
	}

	preset := &model.GamePreset{
		Name:             req.Name,
		Description:      req.Description,
		Category:         req.Category,
		RelationshipType: req.RelationshipType,
		IntimacyLevel:    req.IntimacyLevel,
		Settings:         req.Settings,
		IsSystem:         req.IsSystem,
		IsActive:         req.IsActive,
		CreatedBy:        "admin",
	}

	err := service.GamePreset().CreatePreset(r.Context(), preset)
	if err != nil {
		r.Response.WriteJsonExit(g.Map{
			"code": 500,
			"msg":  "创建预设失败: " + err.Error(),
		})
		return
	}

	r.Response.WriteJsonExit(g.Map{
		"code": 0,
		"msg":  "创建成功",
	})
}

// GetPresetAdmin 后台获取单个预设
func (c *GameController) GetPresetAdmin(r *ghttp.Request) {
	id := r.Get("id").Int()
	if id <= 0 {
		r.Response.WriteJsonExit(g.Map{
			"code": 400,
			"msg":  "无效的预设ID",
		})
		return
	}

	preset, err := service.GamePreset().GetPresetById(r.Context(), id)
	if err != nil {
		r.Response.WriteJsonExit(g.Map{
			"code": 500,
			"msg":  "获取预设失败: " + err.Error(),
		})
		return
	}

	r.Response.WriteJsonExit(g.Map{
		"code": 0,
		"msg":  "success",
		"data": preset,
	})
}

// UpdatePresetAdmin 后台更新预设
func (c *GameController) UpdatePresetAdmin(r *ghttp.Request) {
	id := r.Get("id").Int()
	if id <= 0 {
		r.Response.WriteJsonExit(g.Map{
			"code": 400,
			"msg":  "无效的预设ID",
		})
		return
	}

	var req struct {
		Name             string `json:"name" v:"required|length:1,50#请输入预设名称|预设名称长度为1-50个字符"`
		Description      string `json:"description" v:"required|length:1,200#请输入预设描述|预设描述长度为1-200个字符"`
		Category         string `json:"category" v:"required#请选择分类"`
		RelationshipType string `json:"relationshipType" v:"required#请输入关系类型"`
		IntimacyLevel    int    `json:"intimacyLevel" v:"required|between:1,6#请选择亲密程度|亲密程度必须在1-6之间"`
		Settings         string `json:"settings" v:"required#请输入游戏设置"`
		IsSystem         int    `json:"isSystem"`
		IsActive         int    `json:"isActive"`
	}

	if err := r.Parse(&req); err != nil {
		r.Response.WriteJsonExit(g.Map{
			"code": 400,
			"msg":  err.Error(),
		})
		return
	}

	// 验证JSON格式
	var settings map[string]interface{}
	if err := json.Unmarshal([]byte(req.Settings), &settings); err != nil {
		r.Response.WriteJsonExit(g.Map{
			"code": 400,
			"msg":  "游戏设置JSON格式错误: " + err.Error(),
		})
		return
	}

	preset := &model.GamePreset{
		Name:             req.Name,
		Description:      req.Description,
		Category:         req.Category,
		RelationshipType: req.RelationshipType,
		IntimacyLevel:    req.IntimacyLevel,
		Settings:         req.Settings,
		IsSystem:         req.IsSystem,
		IsActive:         req.IsActive,
	}

	err := service.GamePreset().UpdatePreset(r.Context(), id, preset)
	if err != nil {
		r.Response.WriteJsonExit(g.Map{
			"code": 500,
			"msg":  "更新预设失败: " + err.Error(),
		})
		return
	}

	r.Response.WriteJsonExit(g.Map{
		"code": 0,
		"msg":  "更新成功",
	})
}

// DeletePresetAdmin 后台删除预设
func (c *GameController) DeletePresetAdmin(r *ghttp.Request) {
	id := r.Get("id").Int()
	if id <= 0 {
		r.Response.WriteJsonExit(g.Map{
			"code": 400,
			"msg":  "无效的预设ID",
		})
		return
	}

	err := service.GamePreset().DeletePreset(r.Context(), id)
	if err != nil {
		r.Response.WriteJsonExit(g.Map{
			"code": 500,
			"msg":  "删除预设失败: " + err.Error(),
		})
		return
	}

	r.Response.WriteJsonExit(g.Map{
		"code": 0,
		"msg":  "删除成功",
	})
}

// GetPresetFeatureStatus 获取预设功能状态
func (c *GameController) GetPresetFeatureStatus(r *ghttp.Request) {
	// 首先确保system_settings表存在
	err := c.ensureSystemSettingsTable(r.Context())
	if err != nil {
		r.Response.WriteJsonExit(g.Map{
			"code": 0,
			"msg":  "success",
			"data": g.Map{"enabled": false}, // 默认禁用
		})
		return
	}

	var setting struct {
		Value string `json:"value"`
	}

	err = g.DB().Model("system_settings").Where("key", "preset_feature_enabled").Scan(&setting)
	if err != nil {
		// 默认禁用
		r.Response.WriteJsonExit(g.Map{
			"code": 0,
			"msg":  "success",
			"data": g.Map{"enabled": false},
		})
		return
	}

	enabled := setting.Value == "true"
	r.Response.WriteJsonExit(g.Map{
		"code": 0,
		"msg":  "success",
		"data": g.Map{"enabled": enabled},
	})
}

// TogglePresetFeature 切换预设功能开关
func (c *GameController) TogglePresetFeature(r *ghttp.Request) {
	// 首先确保system_settings表存在
	err := c.ensureSystemSettingsTable(r.Context())
	if err != nil {
		r.Response.WriteJsonExit(g.Map{
			"code": 500,
			"msg":  "初始化系统设置表失败: " + err.Error(),
		})
		return
	}

	var setting struct {
		Value string `json:"value"`
	}

	err = g.DB().Model("system_settings").Where("key", "preset_feature_enabled").Scan(&setting)
	if err != nil {
		// 如果不存在，创建设置
		_, err = g.DB().Exec(r.Context(), "INSERT INTO system_settings (key, value, description) VALUES (?, ?, ?)",
			"preset_feature_enabled", "true", "是否启用前端预设快速切换功能")
		if err != nil {
			r.Response.WriteJsonExit(g.Map{
				"code": 500,
				"msg":  "切换失败: " + err.Error(),
			})
			return
		}

		r.Response.WriteJsonExit(g.Map{
			"code": 0,
			"msg":  "success",
			"data": g.Map{"enabled": true},
		})
		return
	}

	// 切换状态
	newValue := "false"
	if setting.Value == "false" {
		newValue = "true"
	}

	_, err = g.DB().Model("system_settings").Where("key", "preset_feature_enabled").Data(g.Map{
		"value": newValue,
	}).Update()
	if err != nil {
		r.Response.WriteJsonExit(g.Map{
			"code": 500,
			"msg":  "切换失败: " + err.Error(),
		})
		return
	}

	r.Response.WriteJsonExit(g.Map{
		"code": 0,
		"msg":  "success",
		"data": g.Map{"enabled": newValue == "true"},
	})
}

// ensureSystemSettingsTable 确保system_settings表存在
func (c *GameController) ensureSystemSettingsTable(ctx context.Context) error {
	// 检查表是否存在
	result, err := g.DB().GetValue(ctx, "SELECT COUNT(*) FROM sqlite_master WHERE type='table' AND name='system_settings'")
	if err != nil {
		return err
	}

	count := result.Int()

	if count == 0 {
		// 表不存在，创建表
		createTableSQL := `
		CREATE TABLE IF NOT EXISTS system_settings (
			id INTEGER PRIMARY KEY AUTOINCREMENT,
			key TEXT NOT NULL UNIQUE,
			value TEXT NOT NULL,
			description TEXT,
			created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
			updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
		)`

		_, err = g.DB().Exec(ctx, createTableSQL)
		if err != nil {
			return err
		}

		// 插入默认设置
		defaultSettings := [][]interface{}{
			{"preset_feature_enabled", "false", "是否启用前端预设快速切换功能"},
			{"max_presets_display", "6", "前端显示的最大预设数量"},
			{"allow_custom_presets", "true", "是否允许用户创建自定义预设"},
		}

		for _, setting := range defaultSettings {
			_, err = g.DB().Exec(ctx, "INSERT OR IGNORE INTO system_settings (key, value, description) VALUES (?, ?, ?)",
				setting[0], setting[1], setting[2])
			if err != nil {
				return err
			}
		}
	}

	return nil
}
