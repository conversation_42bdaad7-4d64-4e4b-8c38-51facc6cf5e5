package controller

import (
	"lovergame/app/model"
	"lovergame/app/service"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/ghttp"
	"github.com/gogf/gf/v2/util/gconv"
)

// AdminController 后台管理控制器
type AdminController struct{}

func NewAdminController() *AdminController {
	return &AdminController{}
}

// Login 后台登录页面
func (c *AdminController) Login(r *ghttp.Request) {
	if r.Method == "GET" {
		r.Response.WriteTpl("admin/login.html", g.Map{
			"title": "后台管理登录",
		})
		return
	}

	// POST 请求处理登录
	password := r.Get("password").String()
	if password == "loveislove" {
		// 设置会话
		r.Session.Set("admin_logged_in", true)
		r.Response.WriteJsonExit(g.Map{
			"code": 0,
			"msg":  "登录成功",
		})
	} else {
		r.Response.WriteJsonExit(g.Map{
			"code": 400,
			"msg":  "密码错误",
		})
	}
}

// Logout 后台登出
func (c *AdminController) Logout(r *ghttp.Request) {
	r.Session.Remove("admin_logged_in")
	r.Response.RedirectTo("/admin/login")
}

// checkAuth 检查管理员权限
func (c *AdminController) checkAuth(r *ghttp.Request) bool {
	value, _ := r.Session.Get("admin_logged_in")
	return value.Bool()
}

// Index 后台首页
func (c *AdminController) Index(r *ghttp.Request) {
	if !c.checkAuth(r) {
		r.Response.RedirectTo("/admin/login")
		return
	}

	r.Response.WriteTpl("admin/index.html", g.Map{
		"title": "奖惩游戏后台管理",
	})
}

// PunishmentRewardList 奖惩内容列表
func (c *AdminController) PunishmentRewardList(r *ghttp.Request) {
	if !c.checkAuth(r) {
		r.Response.WriteJsonExit(g.Map{
			"code": 401,
			"msg":  "未授权访问",
		})
	}

	page := gconv.Int(r.Get("page", 1))
	limit := gconv.Int(r.Get("limit", 10))
	searchType := gconv.Int(r.Get("type", 0))

	list, total, err := service.PunishmentReward().GetList(r.Context(), page, limit, searchType)
	if err != nil {
		r.Response.WriteJsonExit(g.Map{
			"code": 500,
			"msg":  err.Error(),
		})
	}

	r.Response.WriteJsonExit(g.Map{
		"code":  0,
		"msg":   "success",
		"count": total,
		"data":  list,
	})
}

// PunishmentRewardAdd 添加奖惩内容
func (c *AdminController) PunishmentRewardAdd(r *ghttp.Request) {
	if !c.checkAuth(r) {
		if r.Method == "GET" {
			r.Response.RedirectTo("/admin/login")
		} else {
			r.Response.WriteJsonExit(g.Map{
				"code": 401,
				"msg":  "未授权访问",
			})
		}
		return
	}

	if r.Method == "GET" {
		r.Response.WriteTpl("admin/punishment_reward_add.html", nil)
		return
	}

	var req *model.PunishmentReward
	if err := r.Parse(&req); err != nil {
		r.Response.WriteJsonExit(g.Map{
			"code": 400,
			"msg":  err.Error(),
		})
	}

	if err := service.PunishmentReward().Create(r.Context(), req); err != nil {
		r.Response.WriteJsonExit(g.Map{
			"code": 500,
			"msg":  err.Error(),
		})
	}

	r.Response.WriteJsonExit(g.Map{
		"code": 0,
		"msg":  "添加成功",
	})
}

// PunishmentRewardEdit 编辑奖惩内容
func (c *AdminController) PunishmentRewardEdit(r *ghttp.Request) {
	if !c.checkAuth(r) {
		if r.Method == "GET" {
			r.Response.RedirectTo("/admin/login")
		} else {
			r.Response.WriteJsonExit(g.Map{
				"code": 401,
				"msg":  "未授权访问",
			})
		}
		return
	}

	id := gconv.Int(r.Get("id"))

	if r.Method == "GET" {
		item, err := service.PunishmentReward().GetById(r.Context(), id)
		if err != nil {
			// 如果是AJAX请求，返回JSON错误
			if r.Header.Get("Content-Type") == "application/json" ||
				r.Header.Get("Accept") == "application/json" ||
				r.Header.Get("X-Requested-With") == "XMLHttpRequest" {
				r.Response.WriteJsonExit(g.Map{
					"code": 404,
					"msg":  "内容不存在",
				})
			}
			r.Response.WriteStatusExit(404, "内容不存在")
		}

		// 如果是AJAX请求，返回JSON数据
		if r.Header.Get("Content-Type") == "application/json" ||
			r.Header.Get("Accept") == "application/json" ||
			r.Header.Get("X-Requested-With") == "XMLHttpRequest" {
			r.Response.WriteJsonExit(g.Map{
				"code": 0,
				"msg":  "success",
				"data": item,
			})
		}

		// 否则返回HTML模板
		r.Response.WriteTpl("admin/punishment_reward_edit.html", g.Map{
			"item": item,
		})
		return
	}

	var req *model.PunishmentReward
	if err := r.Parse(&req); err != nil {
		r.Response.WriteJsonExit(g.Map{
			"code": 400,
			"msg":  err.Error(),
		})
	}

	if err := service.PunishmentReward().Update(r.Context(), id, req); err != nil {
		r.Response.WriteJsonExit(g.Map{
			"code": 500,
			"msg":  err.Error(),
		})
	}

	r.Response.WriteJsonExit(g.Map{
		"code": 0,
		"msg":  "更新成功",
	})
}

// PunishmentRewardDelete 删除奖惩内容
func (c *AdminController) PunishmentRewardDelete(r *ghttp.Request) {
	if !c.checkAuth(r) {
		r.Response.WriteJsonExit(g.Map{
			"code": 401,
			"msg":  "未授权访问",
		})
		return
	}

	id := gconv.Int(r.Get("id"))

	if err := service.PunishmentReward().Delete(r.Context(), id); err != nil {
		r.Response.WriteJsonExit(g.Map{
			"code": 500,
			"msg":  err.Error(),
		})
	}

	r.Response.WriteJsonExit(g.Map{
		"code": 0,
		"msg":  "删除成功",
	})
}

// Upload 文件上传
func (c *AdminController) Upload(r *ghttp.Request) {
	if !c.checkAuth(r) {
		r.Response.WriteJsonExit(g.Map{
			"code": 401,
			"msg":  "未授权访问",
		})
		return
	}

	file := r.GetUploadFile("file")
	if file == nil {
		r.Response.WriteJsonExit(g.Map{
			"code": 400,
			"msg":  "请选择文件",
		})
	}

	// 根据文件类型确定上传目录
	var uploadDir string
	var urlPath string
	contentType := file.Header.Get("Content-Type")
	switch {
	case contentType[:5] == "image":
		uploadDir = "resource/public/upload/images/"
		urlPath = "upload/images/"
	case contentType[:5] == "audio":
		uploadDir = "resource/public/upload/audios/"
		urlPath = "upload/audios/"
	case contentType[:5] == "video":
		uploadDir = "resource/public/upload/videos/"
		urlPath = "upload/videos/"
	default:
		r.Response.WriteJsonExit(g.Map{
			"code": 400,
			"msg":  "不支持的文件类型",
		})
	}

	filename, err := file.Save(uploadDir, true)
	if err != nil {
		r.Response.WriteJsonExit(g.Map{
			"code": 500,
			"msg":  err.Error(),
		})
	}

	r.Response.WriteJsonExit(g.Map{
		"code": 0,
		"msg":  "上传成功",
		"data": g.Map{
			"url": urlPath + filename,
		},
	})
}

// GameController 游戏控制器
type GameController struct{}

func NewGameController() *GameController {
	return &GameController{}
}

// Index 游戏首页
func (c *GameController) Index(r *ghttp.Request) {
	r.Response.WriteTpl("game/index.html", g.Map{
		"title": "双人互动奖惩游戏",
	})
}

// Play 开始游戏
func (c *GameController) Play(r *ghttp.Request) {
	g.Log().Infof(r.Context(), "🎮 Play endpoint called")
	g.Log().Infof(r.Context(), "📡 Request method: %s", r.Method)
	g.Log().Infof(r.Context(), "📋 Request body: %s", string(r.GetBody()))

	var req *model.GameRequest
	if err := r.Parse(&req); err != nil {
		g.Log().Errorf(r.Context(), "❌ Failed to parse request: %v", err)
		r.Response.WriteJsonExit(g.Map{
			"code": 400,
			"msg":  err.Error(),
		})
		return
	}

	g.Log().Infof(r.Context(), "✅ Request parsed successfully: %+v", req)

	g.Log().Infof(r.Context(), "🎯 Calling service.Game().PlayGame...")
	result, err := service.Game().PlayGame(r.Context(), req)
	if err != nil {
		g.Log().Errorf(r.Context(), "❌ service.Game().PlayGame failed: %v", err)
		r.Response.WriteJsonExit(g.Map{
			"code": 500,
			"msg":  err.Error(),
		})
		return
	}
	g.Log().Infof(r.Context(), "✅ service.Game().PlayGame succeeded")

	r.Response.WriteJsonExit(g.Map{
		"code": 0,
		"msg":  "success",
		"data": result,
	})
}

// History 游戏历史记录
func (c *GameController) History(r *ghttp.Request) {
	page := gconv.Int(r.Get("page", 1))
	limit := gconv.Int(r.Get("limit", 10))

	list, total, err := service.Game().GetGameHistory(r.Context(), page, limit)
	if err != nil {
		r.Response.WriteJsonExit(g.Map{
			"code": 500,
			"msg":  err.Error(),
		})
	}

	r.Response.WriteJsonExit(g.Map{
		"code":  0,
		"msg":   "success",
		"count": total,
		"data":  list,
	})
}

// TestSlot 测试老虎机数据
func (c *GameController) TestSlot(r *ghttp.Request) {
	symbols, err := service.SlotMachine().GetActiveSymbols(r.Context())
	if err != nil {
		r.Response.WriteJsonExit(g.Map{
			"code": 500,
			"msg":  "获取符号失败: " + err.Error(),
		})
		return
	}

	combinations, err := service.SlotMachine().GetAllCombinations(r.Context())
	if err != nil {
		r.Response.WriteJsonExit(g.Map{
			"code": 500,
			"msg":  "获取组合失败: " + err.Error(),
		})
		return
	}

	r.Response.WriteJsonExit(g.Map{
		"code": 0,
		"msg":  "success",
		"data": g.Map{
			"symbols":      symbols,
			"combinations": combinations,
			"symbolCount":  len(symbols),
			"comboCount":   len(combinations),
		},
	})
}

// CreateRoom 创建房间
func (c *GameController) CreateRoom(r *ghttp.Request) {
	var req struct {
		Name      string `json:"name"` // 房间名字，可选
		CreatedBy string `json:"createdBy" v:"required#请输入创建者姓名"`
		Type      int    `json:"type" v:"in:0,1,2#类型只能是0,1,2"`
		Level     int    `json:"level" v:"in:0,1,2,3#级别只能是0,1,2,3"`
	}

	if err := r.Parse(&req); err != nil {
		r.Response.WriteJsonExit(g.Map{
			"code": 400,
			"msg":  err.Error(),
		})
	}

	settings := &model.GameRequest{
		Type:  req.Type,
		Level: req.Level,
	}

	room, err := service.RoomService().CreateRoom(r.Context(), req.Name, req.CreatedBy, settings)
	if err != nil {
		r.Response.WriteJsonExit(g.Map{
			"code": 500,
			"msg":  err.Error(),
		})
	}

	r.Response.WriteJsonExit(g.Map{
		"code": 0,
		"msg":  "房间创建成功",
		"data": room,
	})
}

// JoinRoom 加入房间
func (c *GameController) JoinRoom(r *ghttp.Request) {
	var req struct {
		RoomID     string `json:"roomId" v:"required#请输入房间ID"`
		PlayerName string `json:"playerName" v:"required#请输入玩家姓名"`
	}

	if err := r.Parse(&req); err != nil {
		r.Response.WriteJsonExit(g.Map{
			"code": 400,
			"msg":  err.Error(),
		})
	}

	room, err := service.RoomService().JoinRoom(r.Context(), req.RoomID, req.PlayerName)
	if err != nil {
		r.Response.WriteJsonExit(g.Map{
			"code": 500,
			"msg":  err.Error(),
		})
	}

	r.Response.WriteJsonExit(g.Map{
		"code": 0,
		"msg":  "加入房间成功",
		"data": room,
	})
}

// GetRoom 获取房间信息
func (c *GameController) GetRoom(r *ghttp.Request) {
	roomID := r.Get("roomId").String()
	if roomID == "" {
		r.Response.WriteJsonExit(g.Map{
			"code": 400,
			"msg":  "请提供房间ID",
		})
	}

	room, err := service.RoomService().GetRoom(r.Context(), roomID)
	if err != nil {
		r.Response.WriteJsonExit(g.Map{
			"code": 404,
			"msg":  err.Error(),
		})
	}

	r.Response.WriteJsonExit(g.Map{
		"code": 0,
		"msg":  "success",
		"data": room,
	})
}

// StartRoomGame 开始房间游戏
func (c *GameController) StartRoomGame(r *ghttp.Request) {
	roomID := r.Get("roomId").String()
	if roomID == "" {
		r.Response.WriteJsonExit(g.Map{
			"code": 400,
			"msg":  "请提供房间ID",
		})
	}

	result, err := service.RoomService().StartGame(r.Context(), roomID)
	if err != nil {
		r.Response.WriteJsonExit(g.Map{
			"code": 500,
			"msg":  err.Error(),
		})
	}

	r.Response.WriteJsonExit(g.Map{
		"code": 0,
		"msg":  "游戏开始",
		"data": result,
	})
}

// ListRooms 获取房间列表
func (c *GameController) ListRooms(r *ghttp.Request) {
	rooms, err := service.RoomService().ListRooms(r.Context())
	if err != nil {
		r.Response.WriteJsonExit(g.Map{
			"code": 500,
			"msg":  err.Error(),
		})
	}

	r.Response.WriteJsonExit(g.Map{
		"code": 0,
		"msg":  "success",
		"data": rooms,
	})
}

// PhotoThemeList 主题摄影列表
func (c *AdminController) PhotoThemeList(r *ghttp.Request) {
	if !c.checkAuth(r) {
		r.Response.WriteJsonExit(g.Map{
			"code": 401,
			"msg":  "未授权访问",
		})
		return
	}

	page := gconv.Int(r.Get("page", 1))
	limit := gconv.Int(r.Get("limit", 10))

	list, total, err := service.PhotoChallenge().GetPhotoThemeList(r.Context(), page, limit)
	if err != nil {
		r.Response.WriteJsonExit(g.Map{
			"code": 500,
			"msg":  err.Error(),
		})
		return
	}

	r.Response.WriteJsonExit(g.Map{
		"code": 0,
		"msg":  "success",
		"data": g.Map{
			"list":  list,
			"total": total,
		},
	})
}

// PhotoThemeAdd 添加主题摄影
func (c *AdminController) PhotoThemeAdd(r *ghttp.Request) {
	if !c.checkAuth(r) {
		r.Response.WriteJsonExit(g.Map{
			"code": 401,
			"msg":  "未授权访问",
		})
		return
	}

	var req *model.PhotoTheme
	if err := r.Parse(&req); err != nil {
		r.Response.WriteJsonExit(g.Map{
			"code": 400,
			"msg":  err.Error(),
		})
		return
	}

	if err := service.PhotoChallenge().AddPhotoTheme(r.Context(), req); err != nil {
		r.Response.WriteJsonExit(g.Map{
			"code": 500,
			"msg":  err.Error(),
		})
		return
	}

	r.Response.WriteJsonExit(g.Map{
		"code": 0,
		"msg":  "添加成功",
	})
}

// PhotoThemeEdit 编辑主题摄影
func (c *AdminController) PhotoThemeEdit(r *ghttp.Request) {
	if !c.checkAuth(r) {
		r.Response.WriteJsonExit(g.Map{
			"code": 401,
			"msg":  "未授权访问",
		})
		return
	}

	id := gconv.Int(r.Get("id"))
	if id == 0 {
		r.Response.WriteJsonExit(g.Map{
			"code": 400,
			"msg":  "请提供有效的ID",
		})
		return
	}

	if r.Method == "GET" {
		// 获取单个主题信息（这里需要实现GetPhotoThemeById方法）
		r.Response.WriteJsonExit(g.Map{
			"code": 0,
			"msg":  "success",
			"data": g.Map{}, // 暂时返回空数据
		})
		return
	}

	var req *model.PhotoTheme
	if err := r.Parse(&req); err != nil {
		r.Response.WriteJsonExit(g.Map{
			"code": 400,
			"msg":  err.Error(),
		})
		return
	}

	if err := service.PhotoChallenge().UpdatePhotoTheme(r.Context(), id, req); err != nil {
		r.Response.WriteJsonExit(g.Map{
			"code": 500,
			"msg":  err.Error(),
		})
		return
	}

	r.Response.WriteJsonExit(g.Map{
		"code": 0,
		"msg":  "更新成功",
	})
}

// PhotoThemeDelete 删除主题摄影
func (c *AdminController) PhotoThemeDelete(r *ghttp.Request) {
	if !c.checkAuth(r) {
		r.Response.WriteJsonExit(g.Map{
			"code": 401,
			"msg":  "未授权访问",
		})
		return
	}

	id := gconv.Int(r.Get("id"))
	if id == 0 {
		r.Response.WriteJsonExit(g.Map{
			"code": 400,
			"msg":  "请提供有效的ID",
		})
		return
	}

	if err := service.PhotoChallenge().DeletePhotoTheme(r.Context(), id); err != nil {
		r.Response.WriteJsonExit(g.Map{
			"code": 500,
			"msg":  err.Error(),
		})
		return
	}

	r.Response.WriteJsonExit(g.Map{
		"code": 0,
		"msg":  "删除成功",
	})
}
