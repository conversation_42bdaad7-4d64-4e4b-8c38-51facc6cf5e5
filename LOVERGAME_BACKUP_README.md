# 🎮 双人互动奖惩游戏 - 完整备份文档

## 📦 备份信息
- **备份文件**: `lovergame_backup_20250101.zip`
- **备份时间**: 2025年1月1日
- **项目版本**: v1.0 完整版
- **备份大小**: 包含完整源码、数据库、资源文件

## 🎯 项目概述

这是一个基于Go语言开发的双人互动奖惩游戏系统，包含4种不同的游戏模式，支持媒体内容展示和完整的后台管理功能。

### 🎮 游戏模式
1. **奖惩游戏** - 经典的随机奖惩轮盘
2. **爱情老虎机** - 华丽的老虎机游戏，支持概率调节
3. **真心话大冒险** - 互动问答游戏
4. **浪漫骰子** - 双骰子挑战游戏

### ✨ 核心特性
- 🎨 精美的UI界面设计
- 🎰 可调节的老虎机中奖概率
- 📱 响应式设计，支持移动端
- 🎵 支持图片、视频、音频媒体内容
- 👥 双人互动显示系统
- ⚙️ 完整的后台管理系统
- 🎯 智能的游戏状态管理

## 🏗️ 技术架构

### 后端技术栈
- **语言**: Go 1.19+
- **框架**: GoFrame v2
- **数据库**: SQLite3
- **ORM**: GoFrame ORM
- **路由**: GoFrame Router

### 前端技术栈
- **HTML5** + **CSS3** + **JavaScript**
- **Bootstrap 5** - 响应式UI框架
- **Font Awesome** - 图标库
- **原生JavaScript** - 无依赖的纯JS实现

### 数据库设计
- `punishment_reward` - 奖惩内容表
- `slot_symbols` - 老虎机符号表
- `slot_combinations` - 老虎机组合表
- `truth_dare_questions` - 真心话大冒险问题表
- `dice_challenges` - 骰子挑战表
- `game_settings` - 游戏设置表
- `game_record` - 游戏记录表
- `game_modes` - 游戏模式表

## 📁 项目结构
```
lovergame/
├── app/                    # 应用核心代码
│   ├── controller/         # 控制器层
│   ├── model/             # 数据模型
│   └── service/           # 业务逻辑层
├── config/                # 配置文件
├── database/              # 数据库初始化
├── resource/              # 静态资源
│   ├── template/          # HTML模板
│   └── static/           # CSS/JS/图片
├── sql/                   # SQL脚本
├── upload/               # 上传文件目录
├── main.go               # 程序入口
├── go.mod                # Go模块文件
└── start.bat             # Windows启动脚本
```

## 🚀 部署说明

### 环境要求
- Go 1.19 或更高版本
- Windows/Linux/macOS 系统
- 8080端口可用

### 快速启动
1. 解压备份文件 `lovergame_backup_20250101.zip`
2. 进入项目目录: `cd lovergame`
3. 安装依赖: `go mod tidy`
4. 启动服务: `go run main.go` 或双击 `start.bat`
5. 访问游戏: http://localhost:8080
6. 管理后台: http://localhost:8080/admin (密码: admin123)

### 数据库说明
- 使用SQLite3，无需额外安装数据库
- 首次启动自动创建数据库和表结构
- 自动导入示例数据
- 数据库文件: `lovergame.db`

## 🎮 功能特性详解

### 1. 奖惩游戏
- 随机选择玩家执行奖励或惩罚
- 支持难度等级设置
- 丰富的奖惩内容库
- 媒体内容支持

### 2. 爱情老虎机
- 15个精美符号，5个稀有度等级
- 可调节中奖概率 (1-100%)
- 特殊组合概率控制
- 华丽的转动动画效果
- 智能的中奖算法

### 3. 真心话大冒险
- 真心话和大冒险两种类型
- 多难度等级问题
- 轮流互动机制
- 问题分类管理

### 4. 浪漫骰子
- 双骰子随机组合
- 12种不同的挑战类型
- 协作完成机制
- 道具需求提示

## ⚙️ 管理后台功能

### 内容管理
- **奖惩内容管理** - 增删改查奖惩项目
- **老虎机符号管理** - 符号和组合配置
- **问题库管理** - 真心话大冒险问题
- **挑战管理** - 骰子挑战内容

### 游戏设置
- **中奖概率控制** - 老虎机概率调节
- **动画速度设置** - 游戏动画时长
- **难度模式选择** - 游戏整体难度
- **系统参数配置** - 音效、人数等

### 数据统计
- **游戏记录查看** - 历史游戏数据
- **使用统计分析** - 游戏模式偏好
- **内容效果评估** - 受欢迎内容

## 🎨 界面特色

### 视觉设计
- 渐变色彩搭配
- 圆角卡片设计
- 动画过渡效果
- 响应式布局

### 交互体验
- 玩家状态显示
- 角色分工明确
- 实时状态更新
- 鼓励性提示

### 移动端适配
- 触摸友好的按钮
- 自适应屏幕尺寸
- 优化的字体大小
- 流畅的滑动体验

## 🔧 自定义扩展

### 添加新游戏模式
1. 在 `game_modes` 表中添加新模式
2. 创建对应的数据表
3. 实现服务层逻辑
4. 添加前端界面
5. 更新路由配置

### 添加新内容类型
1. 设计数据表结构
2. 创建模型文件
3. 实现CRUD服务
4. 添加管理界面
5. 集成到游戏流程

### 自定义主题样式
1. 修改 CSS 变量
2. 替换颜色方案
3. 调整动画效果
4. 更新图标资源

## 📝 更新日志

### v1.0 (2025-01-01)
- ✅ 完整的4种游戏模式
- ✅ 媒体内容支持系统
- ✅ 老虎机概率控制
- ✅ 双人互动显示
- ✅ 完整的管理后台
- ✅ 响应式界面设计
- ✅ 智能状态管理
- ✅ 数据库自动初始化

## 🎯 未来规划

### 功能扩展
- [ ] 多人房间系统
- [ ] 用户账号系统
- [ ] 游戏历史记录
- [ ] 社交分享功能
- [ ] 自定义主题
- [ ] 语音提示功能

### 技术优化
- [ ] Redis缓存支持
- [ ] WebSocket实时通信
- [ ] 微服务架构
- [ ] Docker容器化
- [ ] 云端部署支持

## 📞 技术支持

这个项目是一个完整的、功能丰富的双人互动游戏系统。所有代码都经过精心设计和测试，具有良好的可扩展性和维护性。

如需技术支持或功能定制，请参考项目文档或联系开发团队。

---

**🎮 享受游戏，增进感情！💕**
