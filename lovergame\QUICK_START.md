# 快速编译版本

如果你不想安装Go开发环境，可以下载预编译的可执行文件：

## Windows 64位预编译版本

1. **下载预编译文件**（需要有Go环境才能编译）
   ```cmd
   # 在有Go环境的机器上编译
   go build -o lovergame.exe main.go
   ```

2. **直接运行**
   ```cmd
   # 双击 lovergame.exe 或在命令行运行
   lovergame.exe
   ```

## 使用Docker运行（推荐给开发者）

如果你有Docker环境，可以直接运行：

```bash
# 创建Dockerfile
FROM golang:1.21-alpine AS builder
WORKDIR /app
COPY . .
RUN go mod tidy && go build -o lovergame main.go

FROM alpine:latest
RUN apk --no-cache add ca-certificates tzdata
WORKDIR /root/
COPY --from=builder /app/lovergame .
COPY --from=builder /app/config ./config
COPY --from=builder /app/resource ./resource
COPY --from=builder /app/sql ./sql
COPY --from=builder /app/database ./database
EXPOSE 8080
CMD ["./lovergame"]
```

```bash
# 构建并运行
docker build -t lovergame .
docker run -p 8080:8080 -v $(pwd)/database:/root/database lovergame
```

## 在线体验版本

如果你不想本地安装，可以考虑以下方案：

1. **使用GitHub Codespaces**（需要GitHub账号）
2. **使用Gitpod**（在线IDE）
3. **部署到Vercel/Railway等平台**