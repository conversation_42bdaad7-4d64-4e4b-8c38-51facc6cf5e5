# 🚀 双人互动奖惩游戏 - 部署指南

## 📦 备份文件说明

### 主要备份文件
- **`lovergame_backup_20250101.zip`** - 完整项目备份 (推荐使用)
- **`backup_database.bat`** - 数据库备份工具
- **`restore_backup.bat`** - 备份恢复工具
- **`LOVERGAME_BACKUP_README.md`** - 详细项目文档

## 🛠️ 快速部署步骤

### 方法一：使用恢复脚本 (推荐)
```bash
# 1. 双击运行恢复脚本
restore_backup.bat

# 2. 选择选项 1 (恢复完整项目)
# 3. 等待自动解压完成
```

### 方法二：手动部署
```bash
# 1. 解压备份文件
# 右键点击 lovergame_backup_20250101.zip -> 解压到当前文件夹

# 2. 进入项目目录
cd lovergame

# 3. 安装Go依赖
go mod tidy

# 4. 启动服务
go run main.go
# 或者双击 start.bat
```

## 🌐 访问地址

启动成功后，访问以下地址：

- **游戏主页**: http://localhost:8080
- **管理后台**: http://localhost:8080/admin
- **管理密码**: admin123

## 📋 环境要求

### 必需环境
- **Go语言**: 1.19 或更高版本
- **操作系统**: Windows/Linux/macOS
- **端口**: 8080 (确保未被占用)

### 可选环境
- **Git**: 用于版本控制
- **IDE**: VS Code, GoLand 等

## 🔧 配置说明

### 数据库配置
- 使用 SQLite3，无需额外安装
- 数据库文件: `lovergame.db`
- 首次启动自动创建表结构和示例数据

### 端口配置
如需修改端口，编辑 `config/config.yaml`:
```yaml
server:
  address: ":8080"  # 修改为其他端口
```

### 上传目录
- 媒体文件上传目录: `upload/`
- 确保目录有写入权限

## 🎮 功能验证

### 基础功能测试
1. **访问主页** - 确认界面正常显示
2. **游戏测试** - 尝试每种游戏模式
3. **管理后台** - 登录并查看管理功能
4. **媒体上传** - 测试图片/视频上传

### 高级功能测试
1. **概率调节** - 测试老虎机概率设置
2. **内容管理** - 添加/编辑游戏内容
3. **数据备份** - 运行备份脚本测试

## 🔒 安全配置

### 管理员密码修改
1. 访问管理后台
2. 登录后修改默认密码
3. 建议使用强密码

### 文件权限
- 确保 `upload/` 目录有写入权限
- 数据库文件需要读写权限

## 📊 性能优化

### 生产环境建议
```bash
# 编译为可执行文件
go build -o lovergame main.go

# 后台运行 (Linux/macOS)
nohup ./lovergame &

# Windows 服务运行
# 使用 NSSM 或类似工具将程序注册为Windows服务
```

### 资源优化
- 定期清理上传文件
- 监控数据库大小
- 设置日志轮转

## 🔄 备份策略

### 自动备份
```bash
# 设置定时任务运行备份脚本
# Windows: 任务计划程序
# Linux: crontab
0 2 * * * /path/to/backup_database.bat
```

### 手动备份
```bash
# 运行备份工具
backup_database.bat

# 或手动复制重要文件
copy lovergame.db backup_lovergame_%date%.db
```

## 🐛 故障排除

### 常见问题

#### 1. 端口被占用
```bash
# 查看端口占用 (Windows)
netstat -ano | findstr :8080

# 结束占用进程
taskkill /PID <进程ID> /F
```

#### 2. Go依赖问题
```bash
# 清理模块缓存
go clean -modcache

# 重新下载依赖
go mod download
go mod tidy
```

#### 3. 数据库问题
```bash
# 删除数据库文件，重新初始化
del lovergame.db
go run main.go
```

#### 4. 权限问题
- 确保程序有读写当前目录的权限
- Windows: 以管理员身份运行
- Linux/macOS: 检查文件权限

### 日志查看
程序运行时会输出详细日志，包括：
- 数据库初始化信息
- HTTP请求日志
- 错误信息

## 🔄 更新升级

### 版本更新流程
1. **备份当前版本**
   ```bash
   backup_database.bat
   ```

2. **下载新版本**
   - 获取新的备份文件
   - 解压到临时目录

3. **迁移数据**
   ```bash
   # 复制数据库文件
   copy old_version\lovergame.db new_version\lovergame.db
   
   # 复制上传文件
   xcopy old_version\upload new_version\upload /E /I
   ```

4. **测试新版本**
   - 启动新版本
   - 验证功能正常
   - 检查数据完整性

5. **切换版本**
   - 停止旧版本服务
   - 启动新版本服务

## 📞 技术支持

### 联系方式
- 查看项目文档: `LOVERGAME_BACKUP_README.md`
- 检查故障排除部分
- 查看程序运行日志

### 自助解决
1. 检查环境要求是否满足
2. 查看错误日志信息
3. 尝试重新安装依赖
4. 恢复备份文件测试

---

## 🎉 部署完成

恭喜！如果按照以上步骤操作，你的双人互动奖惩游戏应该已经成功部署并运行。

**享受游戏，增进感情！** 💕

### 快速启动命令
```bash
cd lovergame
go run main.go
```

然后访问: http://localhost:8080
