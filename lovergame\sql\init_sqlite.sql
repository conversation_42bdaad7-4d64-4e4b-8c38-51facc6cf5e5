-- SQLite初始化脚本
-- 奖惩内容表
CREATE TABLE IF NOT EXISTS `punishment_reward` (
    `id` INTEGER PRIMARY KEY AUTOINCREMENT,
    `title` TEXT NOT NULL,
    `content` TEXT NOT NULL,
    `type` INTEGER NOT NULL CHECK (type IN (1, 2)),
    `media_type` INTEGER NOT NULL DEFAULT 0 CHECK (media_type IN (0, 1, 2, 3)),
    `media_url` TEXT,
    `level` INTEGER NOT NULL DEFAULT 1 CHECK (level IN (1, 2, 3)),
    `is_active` INTEGER NOT NULL DEFAULT 1 CHECK (is_active IN (0, 1)),
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引
CREATE INDEX IF NOT EXISTS `idx_punishment_reward_type` ON `punishment_reward` (`type`);
CREATE INDEX IF NOT EXISTS `idx_punishment_reward_level` ON `punishment_reward` (`level`);
CREATE INDEX IF NOT EXISTS `idx_punishment_reward_is_active` ON `punishment_reward` (`is_active`);

-- 游戏记录表
CREATE TABLE IF NOT EXISTS `game_record` (
    `id` INTEGER PRIMARY KEY AUTOINCREMENT,
    `player1_name` TEXT NOT NULL,
    `player2_name` TEXT NOT NULL,
    `selected_item_id` INTEGER NOT NULL,
    `selected_player` INTEGER NOT NULL CHECK (selected_player IN (1, 2)),
    `game_session` TEXT NOT NULL,
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (`selected_item_id`) REFERENCES `punishment_reward` (`id`)
);

-- 创建索引
CREATE INDEX IF NOT EXISTS `idx_game_record_game_session` ON `game_record` (`game_session`);
CREATE INDEX IF NOT EXISTS `idx_game_record_selected_item_id` ON `game_record` (`selected_item_id`);

-- 插入示例数据
INSERT OR IGNORE INTO `punishment_reward` (`id`, `title`, `content`, `type`, `media_type`, `level`) VALUES
(1, '做家务', '洗碗或扫地30分钟', 2, 0, 1),
(2, '表演才艺', '唱歌或跳舞表演一首', 2, 0, 1),
(3, '按摩服务', '为对方按摩10分钟', 1, 0, 1),
(4, '购买礼物', '为对方买一份小礼物', 2, 0, 2),
(5, '浪漫晚餐', '准备一顿浪漫晚餐', 1, 0, 2),
(6, '做运动', '做50个俯卧撑', 2, 0, 2),
(7, '写情书', '手写一封情书', 1, 0, 1),
(8, '禁用手机', '1小时内不能使用手机', 2, 0, 2);