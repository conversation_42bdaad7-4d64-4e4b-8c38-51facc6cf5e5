-- SQLite初始化脚本
-- 奖惩内容表
CREATE TABLE IF NOT EXISTS `punishment_reward` (
    `id` INTEGER PRIMARY KEY AUTOINCREMENT,
    `title` TEXT NOT NULL,
    `content` TEXT NOT NULL,
    `type` INTEGER NOT NULL CHECK (type IN (1, 2)),
    `media_type` INTEGER NOT NULL DEFAULT 0 CHECK (media_type IN (0, 1, 2, 3)),
    `media_url` TEXT,
    `level` INTEGER NOT NULL DEFAULT 1 CHECK (level IN (1, 2, 3)),
    `tags` TEXT DEFAULT '',
    `relationship_types` TEXT DEFAULT '',
    `is_active` INTEGER NOT NULL DEFAULT 1 CHECK (is_active IN (0, 1)),
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引
CREATE INDEX IF NOT EXISTS `idx_punishment_reward_type` ON `punishment_reward` (`type`);
CREATE INDEX IF NOT EXISTS `idx_punishment_reward_level` ON `punishment_reward` (`level`);
CREATE INDEX IF NOT EXISTS `idx_punishment_reward_is_active` ON `punishment_reward` (`is_active`);

-- 游戏记录表
CREATE TABLE IF NOT EXISTS `game_record` (
    `id` INTEGER PRIMARY KEY AUTOINCREMENT,
    `player1_name` TEXT NOT NULL,
    `player2_name` TEXT NOT NULL,
    `game_mode_id` INTEGER NOT NULL DEFAULT 1,
    `selected_item_id` INTEGER,
    `selected_player` INTEGER CHECK (selected_player IN (1, 2)),
    `slot_result` TEXT,
    `game_session` TEXT NOT NULL,
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (`game_mode_id`) REFERENCES `game_modes` (`id`),
    FOREIGN KEY (`selected_item_id`) REFERENCES `punishment_reward` (`id`)
);

-- 创建索引
CREATE INDEX IF NOT EXISTS `idx_game_record_game_session` ON `game_record` (`game_session`);
CREATE INDEX IF NOT EXISTS `idx_game_record_selected_item_id` ON `game_record` (`selected_item_id`);

-- 游戏模式表
CREATE TABLE IF NOT EXISTS `game_modes` (
    `id` INTEGER PRIMARY KEY AUTOINCREMENT,
    `name` TEXT NOT NULL,
    `display_name` TEXT NOT NULL,
    `description` TEXT,
    `is_active` INTEGER NOT NULL DEFAULT 1 CHECK (is_active IN (0, 1)),
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 老虎机符号表
CREATE TABLE IF NOT EXISTS `slot_symbols` (
    `id` INTEGER PRIMARY KEY AUTOINCREMENT,
    `name` TEXT NOT NULL,
    `display_name` TEXT NOT NULL,
    `emoji` TEXT NOT NULL,
    `color` TEXT NOT NULL DEFAULT '#FF6B6B',
    `rarity` INTEGER NOT NULL DEFAULT 1 CHECK (rarity IN (1, 2, 3, 4, 5)),
    `is_active` INTEGER NOT NULL DEFAULT 1 CHECK (is_active IN (0, 1)),
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 老虎机组合表
CREATE TABLE IF NOT EXISTS `slot_combinations` (
    `id` INTEGER PRIMARY KEY AUTOINCREMENT,
    `name` TEXT NOT NULL,
    `symbol1_id` INTEGER NOT NULL,
    `symbol2_id` INTEGER NOT NULL,
    `symbol3_id` INTEGER NOT NULL,
    `activity_title` TEXT NOT NULL,
    `activity_content` TEXT NOT NULL,
    `activity_type` INTEGER NOT NULL DEFAULT 1 CHECK (activity_type IN (1, 2)),
    `intensity_level` INTEGER NOT NULL DEFAULT 1 CHECK (intensity_level IN (1, 2, 3)),
    `media_type` INTEGER NOT NULL DEFAULT 0 CHECK (media_type IN (0, 1, 2, 3)),
    `media_url` TEXT,
    `is_active` INTEGER NOT NULL DEFAULT 1 CHECK (is_active IN (0, 1)),
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (`symbol1_id`) REFERENCES `slot_symbols` (`id`),
    FOREIGN KEY (`symbol2_id`) REFERENCES `slot_symbols` (`id`),
    FOREIGN KEY (`symbol3_id`) REFERENCES `slot_symbols` (`id`)
);

-- 真心话大冒险问题表
CREATE TABLE IF NOT EXISTS `truth_dare_questions` (
    `id` INTEGER PRIMARY KEY AUTOINCREMENT,
    `type` INTEGER NOT NULL CHECK (type IN (1, 2)),
    `title` TEXT NOT NULL,
    `content` TEXT NOT NULL,
    `difficulty` INTEGER NOT NULL DEFAULT 1 CHECK (difficulty IN (1, 2, 3)),
    `category` TEXT NOT NULL DEFAULT 'general',
    `tags` TEXT DEFAULT '',
    `relationship_types` TEXT DEFAULT '',
    `media_type` INTEGER NOT NULL DEFAULT 0 CHECK (media_type IN (0, 1, 2, 3)),
    `media_url` TEXT,
    `is_active` INTEGER NOT NULL DEFAULT 1 CHECK (is_active IN (0, 1)),
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 浪漫骰子挑战表
CREATE TABLE IF NOT EXISTS `dice_challenges` (
    `id` INTEGER PRIMARY KEY AUTOINCREMENT,
    `name` TEXT NOT NULL,
    `description` TEXT NOT NULL,
    `dice_combination` TEXT NOT NULL,
    `activity_title` TEXT NOT NULL,
    `activity_content` TEXT NOT NULL,
    `difficulty` INTEGER NOT NULL DEFAULT 1 CHECK (difficulty IN (1, 2, 3)),
    `duration_minutes` INTEGER NOT NULL DEFAULT 10,
    `requires_props` INTEGER NOT NULL DEFAULT 0 CHECK (requires_props IN (0, 1)),
    `props_needed` TEXT,
    `tags` TEXT DEFAULT '',
    `relationship_types` TEXT DEFAULT '',
    `media_type` INTEGER NOT NULL DEFAULT 0 CHECK (media_type IN (0, 1, 2, 3)),
    `media_url` TEXT,
    `is_active` INTEGER NOT NULL DEFAULT 1 CHECK (is_active IN (0, 1)),
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 游戏设置表
CREATE TABLE IF NOT EXISTS `game_settings` (
    `id` INTEGER PRIMARY KEY AUTOINCREMENT,
    `setting_key` TEXT NOT NULL UNIQUE,
    `setting_value` TEXT NOT NULL,
    `setting_type` TEXT NOT NULL DEFAULT 'string',
    `description` TEXT,
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 爱情问答表
CREATE TABLE IF NOT EXISTS `love_quiz` (
    `id` INTEGER PRIMARY KEY AUTOINCREMENT,
    `question_type` INTEGER NOT NULL DEFAULT 1,
    `difficulty` INTEGER NOT NULL DEFAULT 1,
    `category` TEXT NOT NULL DEFAULT 'general',
    `question` TEXT NOT NULL,
    `answer_type` INTEGER NOT NULL DEFAULT 1,
    `options` TEXT,
    `correct_answer` TEXT,
    `points` INTEGER NOT NULL DEFAULT 10,
    `tags` TEXT DEFAULT '',
    `relationship_types` TEXT DEFAULT '',
    `media_type` INTEGER NOT NULL DEFAULT 0,
    `media_url` TEXT,
    `is_active` INTEGER NOT NULL DEFAULT 1,
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 爱情问答游戏状态表
CREATE TABLE IF NOT EXISTS `love_quiz_game` (
    `id` INTEGER PRIMARY KEY AUTOINCREMENT,
    `game_session` TEXT NOT NULL UNIQUE,
    `player1_name` TEXT NOT NULL,
    `player2_name` TEXT NOT NULL,
    `player1_score` INTEGER NOT NULL DEFAULT 0,
    `player2_score` INTEGER NOT NULL DEFAULT 0,
    `current_turn` INTEGER NOT NULL DEFAULT 1,
    `total_rounds` INTEGER NOT NULL DEFAULT 5,
    `current_round` INTEGER NOT NULL DEFAULT 1,
    `is_game_over` INTEGER NOT NULL DEFAULT 0,
    `winner` TEXT,
    `current_question_id` INTEGER,
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 系统设置表
CREATE TABLE IF NOT EXISTS `system_settings` (
    `id` INTEGER PRIMARY KEY AUTOINCREMENT,
    `key` TEXT NOT NULL UNIQUE,
    `value` TEXT NOT NULL,
    `description` TEXT,
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 预设玩法套表
CREATE TABLE IF NOT EXISTS `game_presets` (
    `id` INTEGER PRIMARY KEY AUTOINCREMENT,
    `name` TEXT NOT NULL,
    `description` TEXT NOT NULL,
    `category` TEXT NOT NULL DEFAULT 'custom',
    `relationship_type` TEXT NOT NULL,
    `intimacy_level` INTEGER NOT NULL DEFAULT 1,
    `settings` TEXT NOT NULL,
    `is_system` INTEGER NOT NULL DEFAULT 0,
    `is_active` INTEGER NOT NULL DEFAULT 1,
    `created_by` TEXT,
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 当前应用的预设状态表
CREATE TABLE IF NOT EXISTS `current_preset_state` (
    `id` INTEGER PRIMARY KEY AUTOINCREMENT,
    `session_id` TEXT NOT NULL DEFAULT 'default',
    `preset_id` INTEGER,
    `preset_settings` TEXT,
    `applied_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (`preset_id`) REFERENCES `game_presets` (`id`)
);

-- 内容标签管理表
CREATE TABLE IF NOT EXISTS `content_tags` (
    `id` INTEGER PRIMARY KEY AUTOINCREMENT,
    `tag_name` TEXT NOT NULL UNIQUE,
    `tag_label` TEXT NOT NULL,
    `description` TEXT,
    `color` TEXT DEFAULT '#007bff',
    `is_active` INTEGER NOT NULL DEFAULT 1,
    `sort_order` INTEGER NOT NULL DEFAULT 0,
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 关系类型管理表
CREATE TABLE IF NOT EXISTS `relationship_types` (
    `id` INTEGER PRIMARY KEY AUTOINCREMENT,
    `type_key` TEXT NOT NULL UNIQUE,
    `type_label` TEXT NOT NULL,
    `description` TEXT,
    `intimacy_level` INTEGER NOT NULL DEFAULT 1,
    `icon` TEXT DEFAULT '👫',
    `is_active` INTEGER NOT NULL DEFAULT 1,
    `sort_order` INTEGER NOT NULL DEFAULT 0,
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 文字接龙词库表
CREATE TABLE IF NOT EXISTS `word_chain` (
    `id` INTEGER PRIMARY KEY AUTOINCREMENT,
    `word` TEXT NOT NULL UNIQUE,
    `category` TEXT NOT NULL DEFAULT 'love',
    `difficulty` INTEGER NOT NULL DEFAULT 1,
    `first_char` TEXT NOT NULL,
    `last_char` TEXT NOT NULL,
    `meaning` TEXT,
    `is_active` INTEGER NOT NULL DEFAULT 1,
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 文字接龙游戏记录表
CREATE TABLE IF NOT EXISTS `word_chain_game` (
    `id` INTEGER PRIMARY KEY AUTOINCREMENT,
    `game_session` TEXT NOT NULL,
    `player1_name` TEXT NOT NULL,
    `player2_name` TEXT NOT NULL,
    `current_word` TEXT NOT NULL,
    `word_chain_history` TEXT,
    `current_player` INTEGER NOT NULL DEFAULT 1,
    `player1_score` INTEGER NOT NULL DEFAULT 0,
    `player2_score` INTEGER NOT NULL DEFAULT 0,
    `time_limit` INTEGER NOT NULL DEFAULT 30,
    `is_finished` INTEGER NOT NULL DEFAULT 0,
    `winner` TEXT,
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 情话接力表
CREATE TABLE IF NOT EXISTS `love_letter` (
    `id` INTEGER PRIMARY KEY AUTOINCREMENT,
    `game_session` TEXT NOT NULL,
    `player1_name` TEXT NOT NULL,
    `player2_name` TEXT NOT NULL,
    `title` TEXT NOT NULL DEFAULT '我们的爱情信件',
    `content` TEXT NOT NULL DEFAULT '',
    `current_player` INTEGER NOT NULL DEFAULT 1,
    `sentence_count` INTEGER NOT NULL DEFAULT 0,
    `max_sentences` INTEGER NOT NULL DEFAULT 10,
    `is_finished` INTEGER NOT NULL DEFAULT 0,
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 情话接力句子记录表
CREATE TABLE IF NOT EXISTS `love_letter_sentence` (
    `id` INTEGER PRIMARY KEY AUTOINCREMENT,
    `letter_id` INTEGER NOT NULL,
    `player_id` INTEGER NOT NULL,
    `sentence` TEXT NOT NULL,
    `sentence_order` INTEGER NOT NULL,
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (`letter_id`) REFERENCES `love_letter` (`id`)
);

-- 角色扮演卡片表
CREATE TABLE IF NOT EXISTS `role_play_card` (
    `id` INTEGER PRIMARY KEY AUTOINCREMENT,
    `character_name` TEXT NOT NULL,
    `character_type` TEXT NOT NULL DEFAULT 'classic',
    `description` TEXT NOT NULL,
    `personality` TEXT,
    `background` TEXT,
    `dialogue_style` TEXT,
    `difficulty` INTEGER NOT NULL DEFAULT 1,
    `is_active` INTEGER NOT NULL DEFAULT 1,
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 角色扮演游戏记录表
CREATE TABLE IF NOT EXISTS `role_play_game` (
    `id` INTEGER PRIMARY KEY AUTOINCREMENT,
    `game_session` TEXT NOT NULL,
    `player1_name` TEXT NOT NULL,
    `player2_name` TEXT NOT NULL,
    `player1_character_id` INTEGER NOT NULL,
    `player2_character_id` INTEGER NOT NULL,
    `player1_score` INTEGER NOT NULL DEFAULT 0,
    `player2_score` INTEGER NOT NULL DEFAULT 0,
    `duration_minutes` INTEGER NOT NULL DEFAULT 5,
    `is_finished` INTEGER NOT NULL DEFAULT 0,
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (`player1_character_id`) REFERENCES `role_play_card` (`id`),
    FOREIGN KEY (`player2_character_id`) REFERENCES `role_play_card` (`id`)
);

-- 未来预测表
CREATE TABLE IF NOT EXISTS `future_prediction` (
    `id` INTEGER PRIMARY KEY AUTOINCREMENT,
    `game_session` TEXT NOT NULL,
    `predictor_name` TEXT NOT NULL,
    `target_name` TEXT NOT NULL,
    `prediction_text` TEXT NOT NULL,
    `prediction_date` DATE NOT NULL,
    `verification_date` DATE,
    `is_correct` INTEGER DEFAULT NULL,
    `actual_result` TEXT,
    `points` INTEGER NOT NULL DEFAULT 0,
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 寻宝游戏表
CREATE TABLE IF NOT EXISTS `treasure_hunt` (
    `id` INTEGER PRIMARY KEY AUTOINCREMENT,
    `game_session` TEXT NOT NULL,
    `creator_name` TEXT NOT NULL,
    `hunter_name` TEXT NOT NULL,
    `title` TEXT NOT NULL DEFAULT '寻宝游戏',
    `description` TEXT,
    `total_clues` INTEGER NOT NULL DEFAULT 1,
    `current_clue` INTEGER NOT NULL DEFAULT 1,
    `is_completed` INTEGER NOT NULL DEFAULT 0,
    `completion_time` DATETIME,
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 寻宝线索表
CREATE TABLE IF NOT EXISTS `treasure_clue` (
    `id` INTEGER PRIMARY KEY AUTOINCREMENT,
    `hunt_id` INTEGER NOT NULL,
    `clue_order` INTEGER NOT NULL,
    `clue_type` TEXT NOT NULL DEFAULT 'text',
    `clue_content` TEXT NOT NULL,
    `clue_image` TEXT,
    `location_hint` TEXT,
    `is_found` INTEGER NOT NULL DEFAULT 0,
    `found_at` DATETIME,
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (`hunt_id`) REFERENCES `treasure_hunt` (`id`)
);

-- 主题摄影游戏表
CREATE TABLE IF NOT EXISTS `photo_challenge` (
    `id` INTEGER PRIMARY KEY AUTOINCREMENT,
    `game_session` TEXT NOT NULL,
    `player1_name` TEXT NOT NULL,
    `player2_name` TEXT NOT NULL,
    `theme` TEXT NOT NULL,
    `description` TEXT NOT NULL,
    `player1_photo` TEXT,
    `player2_photo` TEXT,
    `player1_score` INTEGER NOT NULL DEFAULT 0,
    `player2_score` INTEGER NOT NULL DEFAULT 0,
    `voting_enabled` INTEGER NOT NULL DEFAULT 1,
    `is_finished` INTEGER NOT NULL DEFAULT 0,
    `winner` TEXT,
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 摄影主题表
CREATE TABLE IF NOT EXISTS `photo_theme` (
    `id` INTEGER PRIMARY KEY AUTOINCREMENT,
    `theme_name` TEXT NOT NULL,
    `description` TEXT NOT NULL,
    `difficulty` INTEGER NOT NULL DEFAULT 1,
    `category` TEXT NOT NULL DEFAULT 'general',
    `tips` TEXT,
    `is_active` INTEGER NOT NULL DEFAULT 1,
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 音乐接龙游戏表
CREATE TABLE IF NOT EXISTS `music_chain` (
    `id` INTEGER PRIMARY KEY AUTOINCREMENT,
    `game_session` TEXT NOT NULL,
    `player1_name` TEXT NOT NULL,
    `player2_name` TEXT NOT NULL,
    `current_player` INTEGER NOT NULL DEFAULT 1,
    `round_number` INTEGER NOT NULL DEFAULT 1,
    `max_rounds` INTEGER NOT NULL DEFAULT 5,
    `player1_score` INTEGER NOT NULL DEFAULT 0,
    `player2_score` INTEGER NOT NULL DEFAULT 0,
    `is_finished` INTEGER NOT NULL DEFAULT 0,
    `winner` TEXT,
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 音乐接龙回合表
CREATE TABLE IF NOT EXISTS `music_round` (
    `id` INTEGER PRIMARY KEY AUTOINCREMENT,
    `game_id` INTEGER NOT NULL,
    `round_number` INTEGER NOT NULL,
    `singer_name` TEXT NOT NULL,
    `guesser_name` TEXT NOT NULL,
    `audio_url` TEXT NOT NULL,
    `song_title` TEXT,
    `artist` TEXT,
    `guessed_title` TEXT,
    `is_correct` INTEGER DEFAULT NULL,
    `points_earned` INTEGER NOT NULL DEFAULT 0,
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (`game_id`) REFERENCES `music_chain` (`id`)
);

-- 语音消息游戏表
CREATE TABLE IF NOT EXISTS `voice_challenge` (
    `id` INTEGER PRIMARY KEY AUTOINCREMENT,
    `game_session` TEXT NOT NULL,
    `player1_name` TEXT NOT NULL,
    `player2_name` TEXT NOT NULL,
    `challenge_type` TEXT NOT NULL DEFAULT 'animal',
    `current_player` INTEGER NOT NULL DEFAULT 1,
    `player1_score` INTEGER NOT NULL DEFAULT 0,
    `player2_score` INTEGER NOT NULL DEFAULT 0,
    `is_finished` INTEGER NOT NULL DEFAULT 0,
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 语音挑战题目表
CREATE TABLE IF NOT EXISTS `voice_prompt` (
    `id` INTEGER PRIMARY KEY AUTOINCREMENT,
    `prompt_text` TEXT NOT NULL,
    `category` TEXT NOT NULL DEFAULT 'animal',
    `difficulty` INTEGER NOT NULL DEFAULT 1,
    `tips` TEXT,
    `is_active` INTEGER NOT NULL DEFAULT 1,
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 成就系统表
CREATE TABLE IF NOT EXISTS `achievement` (
    `id` INTEGER PRIMARY KEY AUTOINCREMENT,
    `achievement_key` TEXT NOT NULL UNIQUE,
    `title` TEXT NOT NULL,
    `description` TEXT NOT NULL,
    `icon` TEXT NOT NULL DEFAULT '🏆',
    `category` TEXT NOT NULL DEFAULT 'general',
    `requirement_type` TEXT NOT NULL DEFAULT 'count',
    `requirement_value` INTEGER NOT NULL DEFAULT 1,
    `points` INTEGER NOT NULL DEFAULT 10,
    `is_active` INTEGER NOT NULL DEFAULT 1,
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 用户成就表
CREATE TABLE IF NOT EXISTS `user_achievement` (
    `id` INTEGER PRIMARY KEY AUTOINCREMENT,
    `player_name` TEXT NOT NULL,
    `achievement_key` TEXT NOT NULL,
    `progress` INTEGER NOT NULL DEFAULT 0,
    `is_completed` INTEGER NOT NULL DEFAULT 0,
    `completed_at` DATETIME,
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (`achievement_key`) REFERENCES `achievement` (`achievement_key`)
);

-- 游戏统计表
CREATE TABLE IF NOT EXISTS `game_stats` (
    `id` INTEGER PRIMARY KEY AUTOINCREMENT,
    `player_name` TEXT NOT NULL,
    `game_type` TEXT NOT NULL,
    `games_played` INTEGER NOT NULL DEFAULT 0,
    `games_won` INTEGER NOT NULL DEFAULT 0,
    `total_score` INTEGER NOT NULL DEFAULT 0,
    `best_score` INTEGER NOT NULL DEFAULT 0,
    `total_time_minutes` INTEGER NOT NULL DEFAULT 0,
    `last_played` DATETIME,
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 情侣档案表
CREATE TABLE IF NOT EXISTS `couple_profile` (
    `id` INTEGER PRIMARY KEY AUTOINCREMENT,
    `couple_id` TEXT NOT NULL UNIQUE,
    `player1_name` TEXT NOT NULL,
    `player2_name` TEXT NOT NULL,
    `relationship_start` DATE,
    `anniversary_date` DATE,
    `couple_avatar` TEXT,
    `couple_motto` TEXT,
    `total_games` INTEGER NOT NULL DEFAULT 0,
    `total_score` INTEGER NOT NULL DEFAULT 0,
    `favorite_game` TEXT,
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 纪念日表
CREATE TABLE IF NOT EXISTS `anniversary` (
    `id` INTEGER PRIMARY KEY AUTOINCREMENT,
    `couple_id` TEXT NOT NULL,
    `title` TEXT NOT NULL,
    `date` DATE NOT NULL,
    `description` TEXT,
    `is_recurring` INTEGER NOT NULL DEFAULT 0,
    `reminder_days` INTEGER NOT NULL DEFAULT 1,
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (`couple_id`) REFERENCES `couple_profile` (`couple_id`)
);

-- 情侣回忆表
CREATE TABLE IF NOT EXISTS `couple_memory` (
    `id` INTEGER PRIMARY KEY AUTOINCREMENT,
    `couple_id` TEXT NOT NULL,
    `title` TEXT NOT NULL,
    `content` TEXT NOT NULL,
    `memory_date` DATE NOT NULL,
    `photos` TEXT,
    `tags` TEXT,
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (`couple_id`) REFERENCES `couple_profile` (`couple_id`)
);

-- 自定义内容表
CREATE TABLE IF NOT EXISTS `custom_content` (
    `id` INTEGER PRIMARY KEY AUTOINCREMENT,
    `creator_name` TEXT NOT NULL,
    `content_type` TEXT NOT NULL,
    `title` TEXT NOT NULL,
    `content` TEXT NOT NULL,
    `category` TEXT NOT NULL DEFAULT 'general',
    `difficulty` INTEGER NOT NULL DEFAULT 1,
    `is_private` INTEGER NOT NULL DEFAULT 0,
    `is_active` INTEGER NOT NULL DEFAULT 1,
    `usage_count` INTEGER NOT NULL DEFAULT 0,
    `rating` REAL NOT NULL DEFAULT 0.0,
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 分享记录表
CREATE TABLE IF NOT EXISTS `share_record` (
    `id` INTEGER PRIMARY KEY AUTOINCREMENT,
    `player_name` TEXT NOT NULL,
    `share_type` TEXT NOT NULL,
    `game_type` TEXT NOT NULL,
    `content` TEXT NOT NULL,
    `share_url` TEXT,
    `platform` TEXT NOT NULL DEFAULT 'general',
    `view_count` INTEGER NOT NULL DEFAULT 0,
    `like_count` INTEGER NOT NULL DEFAULT 0,
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 游戏分析表
CREATE TABLE IF NOT EXISTS `game_analytics` (
    `id` INTEGER PRIMARY KEY AUTOINCREMENT,
    `date` DATE NOT NULL,
    `game_type` TEXT NOT NULL,
    `total_games` INTEGER NOT NULL DEFAULT 0,
    `total_players` INTEGER NOT NULL DEFAULT 0,
    `avg_score` REAL NOT NULL DEFAULT 0.0,
    `avg_duration` REAL NOT NULL DEFAULT 0.0,
    `completion_rate` REAL NOT NULL DEFAULT 0.0,
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(`date`, `game_type`)
);

-- 创建索引
CREATE INDEX IF NOT EXISTS `idx_slot_symbols_rarity` ON `slot_symbols` (`rarity`);
CREATE INDEX IF NOT EXISTS `idx_slot_symbols_is_active` ON `slot_symbols` (`is_active`);
CREATE INDEX IF NOT EXISTS `idx_slot_combinations_symbols` ON `slot_combinations` (`symbol1_id`, `symbol2_id`, `symbol3_id`);
CREATE INDEX IF NOT EXISTS `idx_slot_combinations_type` ON `slot_combinations` (`activity_type`);
CREATE INDEX IF NOT EXISTS `idx_slot_combinations_level` ON `slot_combinations` (`intensity_level`);
CREATE INDEX IF NOT EXISTS `idx_truth_dare_type` ON `truth_dare_questions` (`type`);
CREATE INDEX IF NOT EXISTS `idx_truth_dare_difficulty` ON `truth_dare_questions` (`difficulty`);
CREATE INDEX IF NOT EXISTS `idx_dice_challenges_difficulty` ON `dice_challenges` (`difficulty`);
CREATE INDEX IF NOT EXISTS `idx_game_settings_key` ON `game_settings` (`setting_key`);
CREATE INDEX IF NOT EXISTS `idx_love_quiz_type` ON `love_quiz` (`question_type`);
CREATE INDEX IF NOT EXISTS `idx_love_quiz_difficulty` ON `love_quiz` (`difficulty`);
CREATE INDEX IF NOT EXISTS `idx_love_quiz_active` ON `love_quiz` (`is_active`);
CREATE INDEX IF NOT EXISTS `idx_love_quiz_game_session` ON `love_quiz_game` (`game_session`);
CREATE INDEX IF NOT EXISTS `idx_love_quiz_game_players` ON `love_quiz_game` (`player1_name`, `player2_name`);
CREATE INDEX IF NOT EXISTS `idx_game_presets_category` ON `game_presets` (`category`);
CREATE INDEX IF NOT EXISTS `idx_game_presets_relationship` ON `game_presets` (`relationship_type`);
CREATE INDEX IF NOT EXISTS `idx_game_presets_intimacy` ON `game_presets` (`intimacy_level`);
CREATE INDEX IF NOT EXISTS `idx_word_chain_category` ON `word_chain` (`category`);
CREATE INDEX IF NOT EXISTS `idx_word_chain_first_char` ON `word_chain` (`first_char`);
CREATE INDEX IF NOT EXISTS `idx_word_chain_last_char` ON `word_chain` (`last_char`);
CREATE INDEX IF NOT EXISTS `idx_word_chain_game_session` ON `word_chain_game` (`game_session`);
CREATE INDEX IF NOT EXISTS `idx_love_letter_session` ON `love_letter` (`game_session`);
CREATE INDEX IF NOT EXISTS `idx_love_letter_sentence_letter_id` ON `love_letter_sentence` (`letter_id`);
CREATE INDEX IF NOT EXISTS `idx_love_letter_sentence_order` ON `love_letter_sentence` (`sentence_order`);
CREATE INDEX IF NOT EXISTS `idx_role_play_card_type` ON `role_play_card` (`character_type`);
CREATE INDEX IF NOT EXISTS `idx_role_play_card_difficulty` ON `role_play_card` (`difficulty`);
CREATE INDEX IF NOT EXISTS `idx_role_play_game_session` ON `role_play_game` (`game_session`);
CREATE INDEX IF NOT EXISTS `idx_future_prediction_session` ON `future_prediction` (`game_session`);
CREATE INDEX IF NOT EXISTS `idx_future_prediction_date` ON `future_prediction` (`prediction_date`);
CREATE INDEX IF NOT EXISTS `idx_treasure_hunt_session` ON `treasure_hunt` (`game_session`);
CREATE INDEX IF NOT EXISTS `idx_treasure_clue_hunt_id` ON `treasure_clue` (`hunt_id`);
CREATE INDEX IF NOT EXISTS `idx_treasure_clue_order` ON `treasure_clue` (`clue_order`);
CREATE INDEX IF NOT EXISTS `idx_photo_challenge_session` ON `photo_challenge` (`game_session`);
CREATE INDEX IF NOT EXISTS `idx_photo_theme_category` ON `photo_theme` (`category`);
CREATE INDEX IF NOT EXISTS `idx_photo_theme_difficulty` ON `photo_theme` (`difficulty`);
CREATE INDEX IF NOT EXISTS `idx_music_chain_session` ON `music_chain` (`game_session`);
CREATE INDEX IF NOT EXISTS `idx_music_round_game_id` ON `music_round` (`game_id`);
CREATE INDEX IF NOT EXISTS `idx_music_round_number` ON `music_round` (`round_number`);
CREATE INDEX IF NOT EXISTS `idx_voice_challenge_session` ON `voice_challenge` (`game_session`);
CREATE INDEX IF NOT EXISTS `idx_voice_prompt_category` ON `voice_prompt` (`category`);
CREATE INDEX IF NOT EXISTS `idx_achievement_category` ON `achievement` (`category`);
CREATE INDEX IF NOT EXISTS `idx_user_achievement_player` ON `user_achievement` (`player_name`);
CREATE INDEX IF NOT EXISTS `idx_user_achievement_key` ON `user_achievement` (`achievement_key`);
CREATE INDEX IF NOT EXISTS `idx_game_stats_player` ON `game_stats` (`player_name`);
CREATE INDEX IF NOT EXISTS `idx_game_stats_type` ON `game_stats` (`game_type`);
CREATE INDEX IF NOT EXISTS `idx_couple_profile_id` ON `couple_profile` (`couple_id`);
CREATE INDEX IF NOT EXISTS `idx_anniversary_couple` ON `anniversary` (`couple_id`);
CREATE INDEX IF NOT EXISTS `idx_anniversary_date` ON `anniversary` (`date`);
CREATE INDEX IF NOT EXISTS `idx_couple_memory_couple` ON `couple_memory` (`couple_id`);
CREATE INDEX IF NOT EXISTS `idx_couple_memory_date` ON `couple_memory` (`memory_date`);
CREATE INDEX IF NOT EXISTS `idx_custom_content_type` ON `custom_content` (`content_type`);
CREATE INDEX IF NOT EXISTS `idx_custom_content_creator` ON `custom_content` (`creator_name`);
CREATE INDEX IF NOT EXISTS `idx_share_record_player` ON `share_record` (`player_name`);
CREATE INDEX IF NOT EXISTS `idx_share_record_type` ON `share_record` (`share_type`);
CREATE INDEX IF NOT EXISTS `idx_game_analytics_date` ON `game_analytics` (`date`);
CREATE INDEX IF NOT EXISTS `idx_game_analytics_type` ON `game_analytics` (`game_type`);

-- 插入游戏模式数据
INSERT OR IGNORE INTO `game_modes` (`id`, `name`, `display_name`, `description`) VALUES
(1, 'punishment_reward', '奖惩游戏', '经典的奖励与惩罚随机选择游戏'),
(2, 'slot_machine', '爱情老虎机', '转动爱情转盘，获得浪漫活动组合'),
(3, 'truth_dare', '真心话大冒险', '经典的真心话或大冒险游戏'),
(4, 'dice_challenge', '浪漫骰子', '掷骰子决定浪漫挑战内容'),
(5, 'love_quiz', '爱情问答', '情侣问答挑战游戏，增进彼此了解');

-- 插入老虎机符号数据
INSERT OR IGNORE INTO `slot_symbols` (`id`, `name`, `display_name`, `emoji`, `color`, `rarity`) VALUES
(1, 'heart', '爱心', '❤️', '#FF6B6B', 2),
(2, 'kiss', '亲吻', '💋', '#FF1493', 3),
(3, 'rose', '玫瑰', '🌹', '#DC143C', 2),
(4, 'ring', '戒指', '💍', '#FFD700', 4),
(5, 'couple', '情侣', '👫', '#FF69B4', 2),
(6, 'dinner', '晚餐', '🍽️', '#FFA500', 1),
(7, 'movie', '电影', '🎬', '#4169E1', 1),
(8, 'massage', '按摩', '💆', '#9370DB', 2),
(9, 'gift', '礼物', '🎁', '#32CD32', 3),
(10, 'dance', '舞蹈', '💃', '#FF4500', 2),
(11, 'music', '音乐', '🎵', '#1E90FF', 1),
(12, 'star', '星星', '⭐', '#FFD700', 4),
(13, 'moon', '月亮', '🌙', '#C0C0C0', 3),
(14, 'fire', '火焰', '🔥', '#FF4500', 3),
(15, 'cherry', '樱桃', '🍒', '#DC143C', 1);

-- 插入示例数据
-- 插入真心话大冒险问题数据
INSERT OR IGNORE INTO `truth_dare_questions` (`id`, `type`, `title`, `content`, `difficulty`, `category`, `tags`, `relationship_types`) VALUES
-- 真心话问题 - 朋友关系类 (type=1)
(1, 1, '兴趣爱好', '你最喜欢的兴趣爱好是什么？为什么？', 1, 'general', '轻松,友谊', 'same_gender_friend,opposite_gender_friend,close_friend'),
(2, 1, '童年趣事', '分享一个你童年最有趣的回忆', 1, 'childhood', '轻松,友谊,搞笑', 'same_gender_friend,opposite_gender_friend,close_friend'),
(3, 1, '梦想职业', '如果可以重新选择，你最想从事什么职业？', 1, 'dreams', '轻松,友谊', 'same_gender_friend,opposite_gender_friend,close_friend'),
(4, 1, '最尴尬时刻', '说出你最尴尬的一次经历', 2, 'funny', '轻松,友谊,搞笑', 'same_gender_friend,opposite_gender_friend,close_friend'),
(5, 1, '理想假期', '描述你理想中的假期是什么样的？', 1, 'general', '轻松,友谊', 'same_gender_friend,opposite_gender_friend,close_friend'),
(6, 1, '最佳朋友', '你觉得什么样的人可以成为最好的朋友？', 1, 'friendship', '友谊,表达', 'same_gender_friend,opposite_gender_friend,close_friend'),
(7, 1, '秘密才能', '你有什么隐藏的才能或技能？', 2, 'personal', '友谊,表达', 'close_friend'),
(8, 1, '人生目标', '你人生中最重要的目标是什么？', 2, 'deep', '友谊,表达', 'close_friend'),

-- 真心话问题 - 暧昧关系类
(9, 1, '理想伴侣', '描述你心目中完美伴侣的三个特质', 1, 'love', '浪漫,表达', 'ambiguous,new_couple'),
(10, 1, '初恋回忆', '你的初恋是什么时候？当时的感觉是怎样的？', 2, 'love', '浪漫,表达', 'ambiguous,new_couple,passionate_couple'),
(11, 1, '心动瞬间', '什么样的瞬间会让你心动？', 1, 'love', '浪漫,表达', 'ambiguous,new_couple'),
(12, 1, '约会想象', '你理想中的约会是什么样的？', 1, 'love', '浪漫,表达', 'ambiguous,new_couple'),

-- 真心话问题 - 情侣关系类
(13, 1, '最感动瞬间', '在这段感情中，什么时刻让你最感动？', 1, 'love', '浪漫,情侣,表达', 'new_couple,passionate_couple,intimate_couple'),
(14, 1, '未来规划', '你希望我们的关系在5年后是什么样的？', 2, 'future', '浪漫,情侣,表达', 'passionate_couple,intimate_couple'),
(15, 1, '最珍贵回忆', '我们在一起最珍贵的回忆是什么？', 1, 'love', '浪漫,情侣,表达', 'new_couple,passionate_couple,intimate_couple'),
(16, 1, '深层恐惧', '在感情中，你最害怕的是什么？', 3, 'deep', '情侣,表达', 'passionate_couple,intimate_couple'),
(17, 1, '爱的表达', '你最喜欢用什么方式表达爱意？', 2, 'love', '浪漫,情侣,表达', 'new_couple,passionate_couple,intimate_couple'),
(18, 1, '完美一天', '和我在一起最完美的一天应该怎么度过？', 2, 'love', '浪漫,情侣,表达', 'passionate_couple,intimate_couple'),

-- 大冒险任务 - 朋友关系类 (type=2)
(19, 2, '搞笑表演', '表演一个搞笑的动物，让对方猜是什么', 1, 'funny', '轻松,友谊,搞笑,娱乐', 'same_gender_friend,opposite_gender_friend,close_friend'),
(20, 2, '模仿秀', '模仿一个明星或网红，持续2分钟', 2, 'funny', '轻松,友谊,搞笑,娱乐', 'same_gender_friend,opposite_gender_friend,close_friend'),
(21, 2, '才艺展示', '展示一个你的特殊才艺', 1, 'performance', '轻松,友谊,娱乐', 'same_gender_friend,opposite_gender_friend,close_friend'),
(22, 2, '创意合影', '摆出最有创意的姿势合影留念', 1, 'photo', '轻松,友谊,娱乐', 'same_gender_friend,opposite_gender_friend,close_friend'),
(23, 2, '绕口令挑战', '快速说出一个绕口令5遍', 1, 'verbal', '轻松,友谊,搞笑,挑战', 'same_gender_friend,opposite_gender_friend,close_friend'),
(24, 2, '运动挑战', '做20个俯卧撑或仰卧起坐', 2, 'physical', '轻松,友谊,运动,挑战', 'same_gender_friend,close_friend'),

-- 大冒险任务 - 暧昧关系类
(25, 2, '甜蜜拥抱', '给对方一个持续30秒的拥抱', 1, 'physical', '浪漫,轻松', 'ambiguous,new_couple'),
(26, 2, '牵手散步', '牵手在房间里走三圈', 1, 'physical', '浪漫,轻松', 'ambiguous,new_couple'),
(27, 2, '甜言蜜语', '用最甜蜜的话夸奖对方1分钟', 2, 'verbal', '浪漫,表达', 'ambiguous,new_couple,passionate_couple'),
(28, 2, '深情凝视', '深情地看着对方的眼睛30秒', 2, 'physical', '浪漫', 'ambiguous,new_couple'),

-- 大冒险任务 - 情侣关系类
(29, 2, '情歌表演', '为对方唱一首情歌（哪怕跑调也要唱完）', 2, 'performance', '浪漫,情侣,娱乐,表达', 'new_couple,passionate_couple,intimate_couple'),
(30, 2, '舞蹈时光', '和对方跳一支慢舞', 2, 'physical', '浪漫,情侣', 'new_couple,passionate_couple,intimate_couple'),
(31, 2, '按摩服务', '为对方按摩肩膀5分钟', 2, 'physical', '浪漫,情侣', 'new_couple,passionate_couple,intimate_couple'),
(32, 2, '深情告白', '看着对方的眼睛说"我爱你"三遍', 3, 'verbal', '浪漫,情侣,表达', 'passionate_couple,intimate_couple');
(20, 2, '料理挑战', '为对方制作一份简单的小点心', 3, 'activity');

-- 插入浪漫骰子挑战数据
INSERT OR IGNORE INTO `dice_challenges` (`id`, `name`, `description`, `dice_combination`, `activity_title`, `activity_content`, `difficulty`, `duration_minutes`, `requires_props`, `props_needed`, `tags`, `relationship_types`) VALUES
-- 朋友关系类挑战
(1, '友谊游戏', '两个1', '1+1', '简单快乐', '一起玩一个简单的小游戏或聊天15分钟', 1, 15, 0, '', '轻松,友谊,娱乐', 'same_gender_friend,opposite_gender_friend,close_friend'),
(2, '运动挑战', '两个骰子都是奇数', '奇数+奇数', '户外运动', '一起去公园散步或做简单运动30分钟', 2, 30, 0, '', '轻松,友谊,运动', 'same_gender_friend,opposite_gender_friend,close_friend'),
(3, '才艺展示', '总和为5', '1+4或2+3', '才艺比拼', '互相展示一个特殊才艺或技能', 1, 20, 0, '', '轻松,友谊,娱乐', 'same_gender_friend,opposite_gender_friend,close_friend'),
(4, '创意时光', '一奇一偶', '奇数+偶数', '手工制作', '一起制作一个简单的手工艺品', 2, 60, 1, '手工材料', '轻松,友谊,娱乐', 'same_gender_friend,opposite_gender_friend,close_friend'),
(5, '音乐时光', '总和为8', '2+6或3+5或4+4', '音乐分享', '一起听音乐并分享各自喜欢的歌曲', 1, 45, 0, '', '轻松,友谊,娱乐', 'same_gender_friend,opposite_gender_friend,close_friend'),
(6, '游戏竞技', '总和为10', '4+6或5+5', '游戏对战', '玩一个双人游戏，输的人要完成一个小任务', 2, 45, 0, '', '轻松,友谊,娱乐,挑战', 'same_gender_friend,opposite_gender_friend,close_friend'),
(7, '美食分享', '总和为6', '1+5或2+4或3+3', '美食制作', '一起制作简单的小点心或饮品', 2, 60, 1, '食材', '轻松,友谊', 'same_gender_friend,opposite_gender_friend,close_friend'),
(8, '拍照留念', '总和为9', '3+6或4+5', '创意摄影', '一起拍一组有趣的照片', 1, 30, 0, '', '轻松,友谊,娱乐', 'same_gender_friend,opposite_gender_friend,close_friend'),

-- 暧昧关系类挑战
(9, '甜蜜时光', '两个骰子都是偶数', '偶数+偶数', '浪漫电影', '一起看一部浪漫电影', 2, 120, 1, '电影、零食', '浪漫,轻松', 'ambiguous,new_couple'),
(10, '牵手散步', '总和为7', '1+6或2+5或3+4', '浪漫散步', '牵手在附近散步30分钟', 2, 30, 0, '', '浪漫,轻松', 'ambiguous,new_couple'),
(11, '音乐约会', '总和为11', '5+6', '音乐欣赏', '一起听浪漫音乐并聊天', 2, 60, 0, '', '浪漫,轻松', 'ambiguous,new_couple'),
(12, '甜蜜制作', '两个3', '3+3', '甜品制作', '一起制作甜品或饮品', 2, 90, 1, '制作材料', '浪漫,轻松', 'ambiguous,new_couple'),

-- 情侣关系类挑战
(13, '浪漫晚餐', '两个4', '4+4', '烛光晚餐', '准备一顿浪漫的烛光晚餐', 3, 120, 1, '食材、蜡烛', '浪漫,情侣', 'new_couple,passionate_couple,intimate_couple'),
(14, '情侣舞蹈', '两个5', '5+5', '慢舞时光', '在家里跳一支慢舞', 2, 30, 0, '', '浪漫,情侣', 'new_couple,passionate_couple,intimate_couple'),
(15, '爱的表达', '总和为12', '6+6', '深情告白', '用最真诚的话表达对彼此的爱', 3, 45, 0, '', '浪漫,情侣,表达', 'passionate_couple,intimate_couple'),
(16, '回忆之旅', '两个2', '2+2', '回忆分享', '分享你们在一起最美好的回忆', 2, 60, 0, '', '浪漫,情侣,表达', 'new_couple,passionate_couple,intimate_couple'),
(17, '未来规划', '总和为13', '6+6+1', '未来畅想', '一起规划未来的美好生活', 3, 90, 0, '', '浪漫,情侣,表达', 'passionate_couple,intimate_couple'),
(18, '惊喜准备', '总和为14', '6+6+2', '惊喜制作', '为对方准备一个特别的惊喜', 3, 120, 1, '惊喜道具', '浪漫,情侣', 'passionate_couple,intimate_couple'),
(19, '情书写作', '总和为15', '6+6+3', '情书创作', '为对方写一封深情的情书', 3, 60, 1, '纸笔', '浪漫,情侣,表达', 'new_couple,passionate_couple,intimate_couple'),
(20, '完美约会', '双倍幸运', '6+6', '豪华约会', '计划一次特别的约会（看电影+晚餐+散步）', 3, 240, 1, '约会预算', '浪漫,情侣', 'passionate_couple,intimate_couple'),

-- 通用挑战
(21, '随机挑战', '其他组合', '其他', '自由选择', '从以上任意选择一个挑战完成', 1, 30, 0, '', '轻松,友谊', 'same_gender_friend,opposite_gender_friend,close_friend,ambiguous,new_couple,passionate_couple,intimate_couple');

-- 插入系统设置数据
INSERT OR IGNORE INTO `system_settings` (`key`, `value`, `description`) VALUES
('preset_feature_enabled', 'false', '是否启用前端预设快速切换功能'),
('max_presets_display', '6', '前端显示的最大预设数量'),
('allow_custom_presets', 'true', '是否允许用户创建自定义预设');

-- 插入内容标签数据
INSERT OR IGNORE INTO `content_tags` (`id`, `tag_name`, `tag_label`, `description`, `color`, `sort_order`) VALUES
(1, '轻松', '轻松', '轻松愉快的内容，适合放松心情', '#28a745', 1),
(2, '友谊', '友谊', '强调友谊关系的内容', '#17a2b8', 2),
(3, '搞笑', '搞笑', '幽默搞笑的内容，能带来欢乐', '#ffc107', 3),
(4, '浪漫', '浪漫', '浪漫温馨的内容，适合情侣', '#e83e8c', 4),
(5, '情侣', '情侣', '专门为情侣设计的内容', '#dc3545', 5),
(6, '挑战', '挑战', '有一定挑战性的内容', '#fd7e14', 6),
(7, '娱乐', '娱乐', '娱乐性强的内容', '#6f42c1', 7),
(8, '运动', '运动', '涉及运动或体能的内容', '#20c997', 8),
(9, '表达', '表达', '需要表达情感或想法的内容', '#6c757d', 9);

-- 插入关系类型数据
INSERT OR IGNORE INTO `relationship_types` (`id`, `type_key`, `type_label`, `description`, `intimacy_level`, `icon`, `sort_order`) VALUES
(1, 'same_gender_friend', '普通同性朋友', '普通的同性朋友关系，内容保守友好', 1, '🤝', 1),
(2, 'opposite_gender_friend', '普通异性朋友', '普通的异性朋友关系，避免过于亲密的内容', 1, '👋', 2),
(3, 'close_friend', '铁哥们/闺蜜', '关系很好的朋友，可以有一些调侃内容', 2, '👫', 3),
(4, 'ambiguous', '暧昧期朋友', '暧昧期的朋友关系，有一些试探性内容', 3, '😏', 4),
(5, 'new_couple', '刚确认关系的情侣', '刚确认关系的情侣，甜蜜但保守', 3, '💕', 5),
(6, 'passionate_couple', '热恋期情侣', '热恋期的情侣，浪漫亲密内容', 4, '❤️', 6),
(7, 'intimate_couple', '深度亲密情侣', '深度亲密的情侣关系，包含亲密内容', 5, '🔥', 7);

-- 插入预设玩法套数据
INSERT OR IGNORE INTO `game_presets` (`id`, `name`, `description`, `category`, `relationship_type`, `intimacy_level`, `settings`, `is_system`) VALUES
-- 同性朋友类
(1, '🤝 普通同性朋友', '适合普通同性朋友的轻松游戏，避免尴尬内容', 'friendship', 'same_gender_friend', 1, '{"punishment_reward":{"enabled":true,"level":1,"type":0,"content_filter":["轻松","友谊","搞笑"]},"slot_machine":{"enabled":true,"themes":["友谊","娱乐","轻松"]},"truth_dare":{"enabled":true,"level":1,"type":0,"question_types":["了解朋友","兴趣爱好","轻松话题"]},"dice_challenge":{"enabled":true,"level":1,"challenge_types":["友谊挑战","轻松任务"]},"love_quiz":{"enabled":false},"photo_challenge":{"enabled":true,"level":1,"themes":["友谊","日常","搞笑"]}}', 1),
(2, '👫 铁哥们/闺蜜', '适合关系很好的同性朋友，可以有一些调侃内容', 'friendship', 'close_same_gender', 2, '{"punishment_reward":{"enabled":true,"level":2,"type":0},"slot_machine":{"enabled":true},"truth_dare":{"enabled":true,"level":2,"type":0},"dice_challenge":{"enabled":true,"level":2},"love_quiz":{"enabled":false},"photo_challenge":{"enabled":true,"level":2}}', 1),
(3, '🎭 损友模式', '适合损友之间的搞怪游戏，互相调侃', 'friendship', 'funny_friends', 2, '{"punishment_reward":{"enabled":true,"level":2,"type":2},"slot_machine":{"enabled":true},"truth_dare":{"enabled":true,"level":2,"type":0},"dice_challenge":{"enabled":true,"level":2},"love_quiz":{"enabled":false},"photo_challenge":{"enabled":true,"level":2}}', 1),

-- 异性朋友类
(4, '👋 普通异性朋友', '适合普通异性朋友，保持距离的纯友谊向游戏', 'friendship', 'opposite_gender_friend', 1, '{"punishment_reward":{"enabled":true,"level":1,"type":1},"slot_machine":{"enabled":true},"truth_dare":{"enabled":true,"level":1,"type":0},"dice_challenge":{"enabled":true,"level":1},"love_quiz":{"enabled":false},"photo_challenge":{"enabled":true,"level":1}}', 1),
(5, '💫 关系很好的异性朋友', '适合关系很好的异性朋友，稍微亲密但不越界', 'friendship', 'close_opposite_gender', 2, '{"punishment_reward":{"enabled":true,"level":2,"type":1},"slot_machine":{"enabled":true},"truth_dare":{"enabled":true,"level":2,"type":0},"dice_challenge":{"enabled":true,"level":2},"love_quiz":{"enabled":true,"level":1},"photo_challenge":{"enabled":true,"level":2}}', 1),
(6, '😏 暧昧期朋友', '适合暧昧期的朋友，有一些试探性内容', 'ambiguous', 'ambiguous_friends', 3, '{"punishment_reward":{"enabled":true,"level":2,"type":1},"slot_machine":{"enabled":true},"truth_dare":{"enabled":true,"level":2,"type":1},"dice_challenge":{"enabled":true,"level":2},"love_quiz":{"enabled":true,"level":2},"photo_challenge":{"enabled":true,"level":2}}', 1),

-- 情侣类
(7, '💕 刚确认关系的情侣', '适合刚确认关系的情侣，甜蜜但保守的内容', 'couple', 'new_couple', 3, '{"punishment_reward":{"enabled":true,"level":2,"type":1},"slot_machine":{"enabled":true},"truth_dare":{"enabled":true,"level":2,"type":1},"dice_challenge":{"enabled":true,"level":2},"love_quiz":{"enabled":true,"level":2},"photo_challenge":{"enabled":true,"level":2}}', 1),
(8, '❤️ 热恋期情侣', '适合热恋期情侣，浪漫亲密内容', 'couple', 'passionate_couple', 4, '{"punishment_reward":{"enabled":true,"level":3,"type":1},"slot_machine":{"enabled":true},"truth_dare":{"enabled":true,"level":3,"type":1},"dice_challenge":{"enabled":true,"level":3},"love_quiz":{"enabled":true,"level":3},"photo_challenge":{"enabled":true,"level":3}}', 1),
(9, '🔥 深度亲密情侣', '适合深度亲密的情侣，包含亲密行为的内容', 'couple', 'intimate_couple', 5, '{"punishment_reward":{"enabled":true,"level":3,"type":1},"slot_machine":{"enabled":true},"truth_dare":{"enabled":true,"level":3,"type":1},"dice_challenge":{"enabled":true,"level":3},"love_quiz":{"enabled":true,"level":3},"photo_challenge":{"enabled":true,"level":3}}', 1),
(10, '💋 有特殊癖好的情侣', '适合有特殊玩法的开放情侣', 'couple', 'kinky_couple', 6, '{"punishment_reward":{"enabled":true,"level":3,"type":1},"slot_machine":{"enabled":true},"truth_dare":{"enabled":true,"level":3,"type":1},"dice_challenge":{"enabled":true,"level":3},"love_quiz":{"enabled":true,"level":3},"photo_challenge":{"enabled":true,"level":3}}', 1),

-- 特殊关系类
(11, '💑 老夫老妻', '适合老夫老妻，日常生活向，增进感情', 'couple', 'married_couple', 3, '{"punishment_reward":{"enabled":true,"level":2,"type":1},"slot_machine":{"enabled":true},"truth_dare":{"enabled":true,"level":2,"type":1},"dice_challenge":{"enabled":true,"level":2},"love_quiz":{"enabled":true,"level":2},"photo_challenge":{"enabled":true,"level":2}}', 1),
(12, '🌈 同性情侣', '适合同性恋情侣的甜蜜内容', 'couple', 'same_gender_couple', 4, '{"punishment_reward":{"enabled":true,"level":3,"type":1},"slot_machine":{"enabled":true},"truth_dare":{"enabled":true,"level":3,"type":1},"dice_challenge":{"enabled":true,"level":3},"love_quiz":{"enabled":true,"level":3},"photo_challenge":{"enabled":true,"level":3}}', 1),
(13, '🎪 角色扮演爱好者', '适合喜欢角色扮演的情侣', 'special', 'roleplay_lovers', 5, '{"punishment_reward":{"enabled":true,"level":3,"type":1},"slot_machine":{"enabled":true},"truth_dare":{"enabled":true,"level":3,"type":1},"dice_challenge":{"enabled":true,"level":3},"love_quiz":{"enabled":true,"level":3},"photo_challenge":{"enabled":true,"level":3}}', 1),
(14, '🎯 挑战极限', '最大胆的内容，适合非常开放的情侣', 'special', 'extreme_challenge', 6, '{"punishment_reward":{"enabled":true,"level":3,"type":1},"slot_machine":{"enabled":true},"truth_dare":{"enabled":true,"level":3,"type":1},"dice_challenge":{"enabled":true,"level":3},"love_quiz":{"enabled":true,"level":3},"photo_challenge":{"enabled":true,"level":3}}', 1),

-- 年龄向预设
(15, '🎓 学生党', '适合学生情侣，青春活泼的内容', 'age_group', 'student_couple', 2, '{"punishment_reward":{"enabled":true,"level":2,"type":1},"slot_machine":{"enabled":true},"truth_dare":{"enabled":true,"level":2,"type":1},"dice_challenge":{"enabled":true,"level":2},"love_quiz":{"enabled":true,"level":2},"photo_challenge":{"enabled":true,"level":2}}', 1),
(16, '💼 职场人士', '适合职场情侣，成熟稳重的内容', 'age_group', 'working_couple', 3, '{"punishment_reward":{"enabled":true,"level":2,"type":1},"slot_machine":{"enabled":true},"truth_dare":{"enabled":true,"level":2,"type":1},"dice_challenge":{"enabled":true,"level":2},"love_quiz":{"enabled":true,"level":2},"photo_challenge":{"enabled":true,"level":2}}', 1);

-- 插入爱情问答数据
INSERT OR IGNORE INTO `love_quiz` (`id`, `question_type`, `difficulty`, `category`, `question`, `answer_type`, `options`, `correct_answer`, `points`, `tags`, `relationship_types`) VALUES
-- 了解对方类型 - 暧昧/新情侣 (question_type=1)
(1, 1, 1, 'personality', '对方最喜欢的颜色是什么？', 1, '["红色","蓝色","绿色","粉色","黑色","白色","紫色","黄色"]', '', 10, '轻松,友谊', 'ambiguous,new_couple'),
(2, 1, 1, 'food', '对方最喜欢吃什么类型的食物？', 1, '["中餐","西餐","日料","韩料","泰餐","意餐","川菜","粤菜"]', '', 10, '轻松,友谊', 'ambiguous,new_couple'),
(3, 1, 1, 'hobby', '对方最喜欢的休闲活动是什么？', 1, '["看电影","听音乐","运动","阅读","游戏","旅行","购物","烹饪"]', '', 10, '轻松,友谊', 'ambiguous,new_couple'),
(4, 1, 1, 'personality', '对方最喜欢的季节是什么？', 1, '["春天","夏天","秋天","冬天"]', '', 10, '轻松,友谊', 'ambiguous,new_couple'),
(5, 1, 2, 'personality', '对方在压力大的时候通常会做什么？', 1, '["听音乐","运动","睡觉","吃东西","找朋友聊天","独自思考"]', '', 15, '友谊,表达', 'ambiguous,new_couple'),
(6, 1, 2, 'dreams', '对方最想去哪个国家旅行？', 2, '', '', 15, '友谊,表达', 'ambiguous,new_couple'),
(7, 1, 2, 'personality', '对方生气时最希望你怎么做？', 1, '["给TA空间","主动道歉","买礼物哄","陪在身边","幽默化解","认真倾听"]', '', 15, '友谊,表达', 'ambiguous,new_couple'),
(8, 1, 2, 'dreams', '对方最想学会的技能是什么？', 2, '', '', 15, '友谊,表达', 'ambiguous,new_couple'),

-- 了解对方类型 - 热恋/深度情侣 (question_type=1)
(9, 1, 3, 'personality', '描述对方最害怕的三件事情', 3, '', '', 20, '情侣,表达', 'passionate_couple,intimate_couple'),
(10, 1, 3, 'personality', '描述对方最自豪的三个成就', 3, '', '', 20, '情侣,表达', 'passionate_couple,intimate_couple'),
(11, 1, 3, 'deep', '对方内心最深的愿望是什么？', 3, '', '', 25, '情侣,表达', 'passionate_couple,intimate_couple'),
(12, 1, 3, 'deep', '对方觉得自己最大的缺点是什么？', 3, '', '', 25, '情侣,表达', 'passionate_couple,intimate_couple'),

-- 共同回忆类型 - 新情侣 (question_type=2)
(13, 2, 1, 'memory', '你们第一次见面是在什么时候？', 2, '', '', 10, '浪漫,情侣', 'new_couple,passionate_couple,intimate_couple'),
(14, 2, 1, 'memory', '你们的第一次约会去了哪里？', 2, '', '', 10, '浪漫,情侣', 'new_couple,passionate_couple,intimate_couple'),
(15, 2, 1, 'memory', '你们第一次牵手是在什么时候？', 2, '', '', 10, '浪漫,情侣', 'new_couple,passionate_couple,intimate_couple'),
(16, 2, 2, 'memory', '你们第一次说"我爱你"是什么时候？', 2, '', '', 15, '浪漫,情侣,表达', 'new_couple,passionate_couple,intimate_couple'),
(17, 2, 2, 'memory', '你们在一起后的第一个纪念日是怎么度过的？', 3, '', '', 15, '浪漫,情侣', 'new_couple,passionate_couple,intimate_couple'),
(18, 2, 2, 'memory', '你们一起做过最浪漫的事情是什么？', 3, '', '', 15, '浪漫,情侣', 'new_couple,passionate_couple,intimate_couple'),

-- 共同回忆类型 - 深度情侣 (question_type=2)
(19, 2, 3, 'memory', '回忆一次你们之间最深刻的谈话内容', 3, '', '', 20, '浪漫,情侣,表达', 'passionate_couple,intimate_couple'),
(20, 2, 3, 'memory', '你们一起克服的最大困难是什么？', 3, '', '', 20, '情侣,表达', 'passionate_couple,intimate_couple'),
(21, 2, 3, 'memory', '什么时刻让你觉得最爱对方？', 3, '', '', 25, '浪漫,情侣,表达', 'passionate_couple,intimate_couple'),

-- 未来计划类型 - 所有情侣 (question_type=3)
(22, 3, 1, 'future', '你们计划什么时候一起旅行？', 1, '["下个月","三个月内","半年内","一年内","还没计划","随时都可以"]', '', 10, '浪漫,情侣', 'new_couple,passionate_couple,intimate_couple'),
(23, 3, 2, 'future', '你们希望将来住在什么样的房子里？', 3, '', '', 15, '浪漫,情侣,表达', 'new_couple,passionate_couple,intimate_couple'),
(24, 3, 2, 'future', '你们想要几个孩子？', 1, '["0个","1个","2个","3个","顺其自然","还没想过"]', '', 15, '情侣,表达', 'passionate_couple,intimate_couple'),
(25, 3, 2, 'future', '你们希望多久结婚？', 1, '["一年内","两年内","三年内","五年内","还没想过","顺其自然"]', '', 15, '情侣,表达', 'passionate_couple,intimate_couple'),

-- 未来计划类型 - 深度情侣 (question_type=3)
(26, 3, 3, 'future', '描述你们理想中的退休生活', 3, '', '', 20, '情侣,表达', 'passionate_couple,intimate_couple'),
(27, 3, 3, 'future', '如果可以实现一个共同愿望，你们最想要什么？', 3, '', '', 20, '浪漫,情侣,表达', 'passionate_couple,intimate_couple'),
(28, 3, 3, 'future', '你们希望给孩子什么样的教育？', 3, '', '', 25, '情侣,表达', 'passionate_couple,intimate_couple'),

-- 特殊类型问题
(29, 1, 2, 'fun', '如果对方是一种动物，会是什么？为什么？', 3, '', '', 15, '轻松,搞笑,表达', 'ambiguous,new_couple,passionate_couple'),
(30, 2, 2, 'fun', '你们一起做过最搞笑的事情是什么？', 3, '', '', 15, '轻松,搞笑,情侣', 'new_couple,passionate_couple,intimate_couple');
(22, 2, 1, 'memory', '你们一起看过的第一部电影是什么？', 2, '', '', 10),
(23, 2, 2, 'memory', '你们最难忘的一次旅行是去哪里？', 3, '', '', 15),
(24, 2, 2, 'memory', '你们第一次说"我爱你"是在什么情况下？', 3, '', '', 15),
(25, 2, 3, 'memory', '回忆一次你们一起克服困难的经历', 3, '', '', 20),

-- 更多未来计划类型问题
(26, 3, 1, 'future', '你们希望多久后结婚？', 1, '["已经结婚了","1年内","2-3年","3-5年","还没想过","顺其自然"]', '', 10),
(27, 3, 1, 'future', '你们想要养宠物吗？', 1, '["想养狗","想养猫","想养其他","不想养","还没讨论过"]', '', 10),
(28, 3, 2, 'future', '你们理想中的家是什么样子的？', 3, '', '', 15),
(29, 3, 2, 'future', '你们希望在哪个城市定居？', 2, '', '', 15),
(30, 3, 3, 'future', '描述你们理想中的老年生活', 3, '', '', 20),

-- 性格特点类问题
(31, 1, 1, 'personality', '你的伴侣是内向还是外向？', 1, '["很内向","偏内向","中性","偏外向","很外向"]', '', 10),
(32, 1, 2, 'personality', '你的伴侣做决定时通常是什么风格？', 1, '["很果断","比较果断","需要时间考虑","很纠结","依赖别人意见"]', '', 15),
(33, 1, 3, 'personality', '你的伴侣最大的优点和最需要改进的地方是什么？', 3, '', '', 20),

-- 兴趣爱好类问题
(34, 1, 1, 'hobby', '你的伴侣最喜欢的音乐类型是什么？', 1, '["流行音乐","摇滚","古典","民谣","电子音乐","说唱"]', '', 10),
(35, 1, 2, 'hobby', '你的伴侣最喜欢的休闲活动是什么？', 2, '', '', 15),
(36, 1, 3, 'hobby', '如果你的伴侣有一整天自由时间，TA最想做什么？', 3, '', '', 20),

-- 生活习惯类问题
(37, 1, 1, 'life', '你的伴侣是早起型还是夜猫子？', 1, '["很早起","比较早起","正常作息","比较晚睡","夜猫子"]', '', 10),
(38, 1, 2, 'life', '你的伴侣最不能忍受的生活习惯是什么？', 2, '', '', 15),
(39, 1, 3, 'life', '描述你的伴侣理想中的一天是怎样度过的', 3, '', '', 20),

-- 价值观类问题
(40, 1, 2, 'values', '对你的伴侣来说，最重要的三样东西是什么？', 3, '', '', 15),
(41, 1, 3, 'values', '你的伴侣如何定义成功？', 3, '', '', 20),

-- 更多回忆类问题
(42, 2, 1, 'memory', '你们第一次吵架是因为什么？', 2, '', '', 10),
(43, 2, 2, 'memory', '你们一起做过最疯狂的事情是什么？', 3, '', '', 15),
(44, 2, 3, 'memory', '哪个瞬间让你确定TA就是对的人？', 3, '', '', 20),

-- 更多未来规划问题
(45, 3, 1, 'future', '你们计划什么时候要孩子？', 1, '["已经有了","1-2年内","3-5年内","5年以后","不要孩子","还没讨论"]', '', 10),
(46, 3, 2, 'future', '你们希望将来的工作和生活如何平衡？', 3, '', '', 15),
(47, 3, 3, 'future', '如果有机会移居国外，你们会选择哪个国家？为什么？', 3, '', '', 20),

-- 趣味问题
(48, 1, 1, 'fun', '如果你的伴侣是一种动物，会是什么？', 2, '', '', 10),
(49, 1, 2, 'fun', '你的伴侣最害怕什么？', 1, '["蟑螂","老鼠","蛇","高处","黑暗","鬼故事"]', '', 15),
(50, 1, 3, 'fun', '如果你的伴侣中了彩票，TA会怎么花这笔钱？', 3, '', '', 20);

-- 插入文字接龙词汇数据
INSERT OR IGNORE INTO `word_chain` (`id`, `word`, `category`, `difficulty`, `first_char`, `last_char`, `meaning`) VALUES
-- 爱情相关词汇
(1, '爱情', 'love', 1, '爱', '情', '男女之间的感情'),
(2, '情人', 'love', 1, '情', '人', '恋爱中的对象'),
(3, '人生', 'love', 1, '人', '生', '人的一生'),
(4, '生活', 'love', 1, '生', '活', '日常的生活'),
(5, '活力', 'love', 2, '活', '力', '充满活力'),
(6, '力量', 'love', 2, '力', '量', '强大的力量'),
(7, '量身', 'love', 3, '量', '身', '量身定制'),
(8, '身心', 'love', 2, '身', '心', '身体和心灵'),
(9, '心意', 'love', 1, '心', '意', '内心的想法'),
(10, '意义', 'love', 2, '意', '义', '重要的意义'),

-- 浪漫词汇
(11, '浪漫', 'romance', 1, '浪', '漫', '充满诗意的情调'),
(12, '漫步', 'romance', 1, '漫', '步', '悠闲地走路'),
(13, '步调', 'romance', 2, '步', '调', '行走的节奏'),
(14, '调情', 'romance', 2, '调', '情', '表达爱意'),
(15, '情调', 'romance', 2, '情', '调', '情感的氛围'),
(16, '调和', 'romance', 3, '调', '和', '和谐统一'),
(17, '和谐', 'romance', 2, '和', '谐', '协调一致'),
(18, '谐音', 'romance', 3, '谐', '音', '相同的读音'),
(19, '音乐', 'romance', 1, '音', '乐', '美妙的声音'),
(20, '乐趣', 'romance', 1, '乐', '趣', '快乐的感受'),

-- 幸福词汇
(21, '幸福', 'happiness', 1, '幸', '福', '美好的感受'),
(22, '福气', 'happiness', 1, '福', '气', '好运气'),
(23, '气质', 'happiness', 2, '气', '质', '个人的品质'),
(24, '质量', 'happiness', 2, '质', '量', '品质的好坏'),
(25, '量化', 'happiness', 3, '量', '化', '用数字表示'),
(26, '化学', 'happiness', 3, '化', '学', '研究物质的学科'),
(27, '学习', 'happiness', 1, '学', '习', '获取知识'),
(28, '习惯', 'happiness', 2, '习', '惯', '经常做的事'),
(29, '惯例', 'happiness', 3, '惯', '例', '习惯的做法'),
(30, '例外', 'happiness', 2, '例', '外', '特殊情况'),

-- 美好词汇
(31, '美好', 'beauty', 1, '美', '好', '令人愉快的'),
(32, '好运', 'beauty', 1, '好', '运', '幸运'),
(33, '运气', 'beauty', 1, '运', '气', '机遇'),
(34, '气息', 'beauty', 2, '气', '息', '呼吸的空气'),
(35, '息息', 'beauty', 3, '息', '息', '密切相关'),
(36, '息怒', 'beauty', 3, '息', '怒', '平息愤怒'),
(37, '怒火', 'beauty', 2, '怒', '火', '愤怒的情绪'),
(38, '火热', 'beauty', 2, '火', '热', '非常热烈'),
(39, '热情', 'beauty', 1, '热', '情', '积极的态度'),
(40, '情感', 'beauty', 1, '情', '感', '内心的感受'),

-- 甜蜜词汇
(41, '甜蜜', 'sweet', 1, '甜', '蜜', '甜美的感觉'),
(42, '蜜语', 'sweet', 2, '蜜', '语', '甜美的话语'),
(43, '语言', 'sweet', 1, '语', '言', '表达思想的工具'),
(44, '言语', 'sweet', 2, '言', '语', '说话的内容'),
(45, '语音', 'sweet', 2, '语', '音', '说话的声音'),
(46, '音信', 'sweet', 3, '音', '信', '消息'),
(47, '信任', 'sweet', 1, '信', '任', '相信和依赖'),
(48, '任务', 'sweet', 2, '任', '务', '需要完成的工作'),
(49, '务实', 'sweet', 3, '务', '实', '注重实际'),
(50, '实际', 'sweet', 2, '实', '际', '真实的情况');

-- 插入角色扮演卡片数据
INSERT OR IGNORE INTO `role_play_card` (`id`, `character_name`, `character_type`, `description`, `personality`, `background`, `dialogue_style`, `difficulty`) VALUES
-- 经典角色
(1, '霸道总裁', 'classic', '冷酷外表下有颗温柔的心', '强势、专一、占有欲强', '商界精英，掌控一切', '简洁有力，偶尔温柔', 2),
(2, '温柔小秘书', 'classic', '贴心体贴的助理', '细心、温柔、善解人意', '职场新人，努力上进', '礼貌温和，略带羞涩', 1),
(3, '古代皇帝', 'classic', '九五之尊的帝王', '威严、深情、责任感强', '统治天下的君主', '威严庄重，偶露真情', 3),
(4, '古代妃子', 'classic', '美丽聪慧的后宫佳人', '聪明、美丽、有心机', '宫廷生活，争宠夺爱', '优雅婉转，暗藏机锋', 3),

-- 现代角色
(5, '学霸同桌', 'modern', '成绩优异的同班同学', '认真、聪明、有点傲娇', '校园生活，学习第一', '理性分析，偶尔可爱', 1),
(6, '运动健将', 'modern', '阳光帅气的体育明星', '阳光、自信、有活力', '体育特长生，人气很高', '直爽热情，充满活力', 1),
(7, '咖啡店老板', 'modern', '文艺范的小店主', '文艺、浪漫、有品味', '经营温馨咖啡店', '温文尔雅，富有诗意', 2),
(8, '程序员', 'modern', '技术宅但很有趣', '理性、专注、内心温暖', 'IT行业，热爱技术', '逻辑清晰，偶尔呆萌', 2),

-- 动漫角色
(9, '傲娇大小姐', 'anime', '外冷内热的千金小姐', '傲娇、善良、有点任性', '富家千金，被宠坏了', '高傲中带着可爱', 2),
(10, '温柔学长', 'anime', '体贴入微的前辈', '温柔、成熟、有责任感', '学校的完美前辈', '温和耐心，充满关怀', 1),
(11, '中二少年', 'anime', '有着特殊设定的少年', '中二、热血、有梦想', '相信自己有特殊能力', '夸张表演，充满激情', 3),
(12, '治愈系少女', 'anime', '能够治愈人心的女孩', '温暖、纯真、有治愈力', '总是给人带来温暖', '轻柔甜美，充满正能量', 1),

-- 历史人物
(13, '诗人李白', 'historical', '浪漫主义诗人', '豪放、浪漫、才华横溢', '唐代大诗人，酷爱饮酒', '诗意盎然，豪情万丈', 3),
(14, '才女李清照', 'historical', '宋代著名女词人', '才华出众、感情丰富', '文学家，经历坎坷', '文雅细腻，情感真挚', 3),
(15, '武侠大侠', 'historical', '行走江湖的侠客', '正义、勇敢、有侠义心', '武功高强，行侠仗义', '豪爽直率，有江湖气', 2),

-- 奇幻角色
(16, '精灵王子', 'fantasy', '优雅高贵的精灵族', '优雅、高贵、有魔法', '森林王国的王子', '优美动听，带有魔性', 3),
(17, '魔法少女', 'fantasy', '拥有魔法力量的少女', '勇敢、善良、有正义感', '保护世界的魔法使者', '充满希望，略带神秘', 2),
(18, '龙族王者', 'fantasy', '强大的龙族首领', '强大、高傲、有威严', '统治龙族的王者', '威严霸气，偶显温情', 3),

-- 职业角色
(19, '医生', 'profession', '救死扶伤的白衣天使', '专业、细心、有爱心', '医院工作，治病救人', '专业严谨，温暖关怀', 2),
(20, '老师', 'profession', '传道授业的教育者', '耐心、负责、有爱心', '教书育人，桃李满天下', '循循善诱，温和有力', 1);

-- 插入摄影主题数据
INSERT OR IGNORE INTO `photo_theme` (`id`, `theme_name`, `description`, `difficulty`, `category`, `tips`, `tags`, `relationship_types`) VALUES
-- 朋友关系类主题
(1, '最搞笑的姿势', '用最搞笑的姿势逗对方开心', 1, 'funny', '夸张的表情和动作会更有趣', '轻松,友谊,搞笑,娱乐', 'same_gender_friend,opposite_gender_friend,close_friend'),
(2, '模仿动物', '模仿你最喜欢的动物', 2, 'funny', '可以模仿动物的表情、动作或叫声', '轻松,友谊,搞笑,娱乐', 'same_gender_friend,opposite_gender_friend,close_friend'),
(3, '童年回忆', '重现童年时的经典动作', 2, 'memory', '想想小时候最喜欢做的事情', '轻松,友谊', 'same_gender_friend,opposite_gender_friend,close_friend'),
(4, '开心大笑', '展现最开心的笑容', 1, 'emotion', '想想最开心的事情，自然地笑出来', '轻松,友谊,娱乐', 'same_gender_friend,opposite_gender_friend,close_friend'),
(5, '惊讶表情', '做出最夸张的惊讶表情', 1, 'emotion', '眼睛和嘴巴都要夸张一些', '轻松,友谊,搞笑', 'same_gender_friend,opposite_gender_friend,close_friend'),
(6, '比划数字', '用手势比划一个数字', 1, 'interactive', '让对方猜猜是什么数字', '轻松,友谊,娱乐', 'same_gender_friend,opposite_gender_friend,close_friend'),
(7, '才艺展示', '展示你的特殊才艺', 2, 'performance', '可以是唱歌、跳舞或其他技能', '轻松,友谊,娱乐', 'same_gender_friend,opposite_gender_friend,close_friend'),
(8, '运动姿势', '展示一个运动动作', 2, 'sport', '可以是跑步、跳跃或健身动作', '轻松,友谊,运动', 'same_gender_friend,opposite_gender_friend,close_friend'),
(9, '用物品遮脸', '用身边的物品创意遮脸', 2, 'creative', '可以用书、杯子、花等物品', '轻松,友谊,娱乐', 'same_gender_friend,opposite_gender_friend,close_friend'),
(10, '模仿对方', '模仿对方的经典动作', 2, 'interactive', '观察对方的习惯动作', '轻松,友谊,搞笑', 'close_friend'),

-- 暧昧关系类主题
(11, '最可爱的表情', '展现你最可爱的一面', 1, 'cute', '可以做鬼脸、卖萌或者甜美微笑', '轻松,浪漫', 'ambiguous,new_couple'),
(12, '害羞表情', '展现最害羞的样子', 2, 'emotion', '可以捂脸或者低头害羞', '浪漫,轻松', 'ambiguous,new_couple'),
(13, '甜美微笑', '展现最甜美的笑容', 1, 'cute', '真诚自然的笑容最迷人', '浪漫,轻松', 'ambiguous,new_couple'),
(14, '思考状态', '展现深思的样子', 2, 'emotion', '可以托腮或者皱眉思考', '轻松,浪漫', 'ambiguous,new_couple'),

-- 情侣关系类主题
(15, '最迷人的微笑', '展现你最迷人的笑容', 1, 'romantic', '真诚的笑容最打动人心', '浪漫,情侣', 'new_couple,passionate_couple,intimate_couple'),
(16, '深情凝视', '用眼神传达爱意', 2, 'romantic', '眼神要温柔而深情', '浪漫,情侣,表达', 'new_couple,passionate_couple,intimate_couple'),
(17, '心形手势', '用手比出心形', 1, 'romantic', '可以单手或双手比心', '浪漫,情侣', 'new_couple,passionate_couple,intimate_couple'),
(18, '飞吻瞬间', '给对方一个飞吻', 2, 'romantic', '表情要自然甜美', '浪漫,情侣,表达', 'new_couple,passionate_couple,intimate_couple'),
(19, '拥抱姿势', '展现最温暖的拥抱', 2, 'romantic', '可以抱抱玩偶或做拥抱手势', '浪漫,情侣', 'new_couple,passionate_couple,intimate_couple'),
(20, '爱心拼图', '用手指拼出爱心形状', 2, 'romantic', '两只手配合拼出完整爱心', '浪漫,情侣', 'new_couple,passionate_couple,intimate_couple'),
(21, '情书阅读', '假装在读情书的样子', 2, 'romantic', '表情要甜蜜陶醉', '浪漫,情侣,表达', 'new_couple,passionate_couple,intimate_couple'),
(22, '梦幻表情', '展现最梦幻的表情', 2, 'romantic', '眼神迷离，表情甜美', '浪漫,情侣', 'passionate_couple,intimate_couple'),

-- 创意挑战类主题
(23, '影子游戏', '用影子创造有趣的形状', 3, 'creative', '利用光线和手势创造影子', '娱乐,挑战', 'close_friend,ambiguous,new_couple,passionate_couple'),
(24, '镜子自拍', '利用镜子拍出创意照片', 2, 'creative', '可以拍镜中镜的效果', '娱乐,挑战', 'close_friend,ambiguous,new_couple,passionate_couple'),
(25, '倒立世界', '拍摄颠倒的视角', 3, 'creative', '可以倒立或者把照片倒过来看', '娱乐,挑战', 'close_friend,new_couple,passionate_couple'),
(26, '无声表达', '不说话表达一句话', 3, 'interactive', '用表情和动作传达信息', '挑战,表达', 'close_friend,ambiguous,new_couple,passionate_couple'),
(27, '角色扮演', '扮演一个角色', 3, 'interactive', '可以是明星、动漫角色等', '娱乐,挑战', 'close_friend,passionate_couple,intimate_couple'),
(28, '时尚造型', '展现最时尚的造型', 2, 'fashion', '可以用衣服、配饰搭配', '娱乐', 'close_friend,ambiguous,new_couple,passionate_couple'),
(29, '复古风格', '展现复古年代的风格', 3, 'vintage', '模仿不同年代的经典造型', '娱乐,挑战', 'close_friend,new_couple,passionate_couple,intimate_couple'),
(30, '未来科幻', '展现未来科幻的感觉', 3, 'futuristic', '用创意姿势和表情展现科幻感', '娱乐,挑战', 'close_friend,passionate_couple,intimate_couple');

-- 插入语音挑战题目数据
INSERT OR IGNORE INTO `voice_prompt` (`id`, `prompt_text`, `category`, `difficulty`, `tips`) VALUES
-- 动物叫声
(1, '模仿小猫叫声', 'animal', 1, '喵喵喵，要可爱一点'),
(2, '模仿小狗叫声', 'animal', 1, '汪汪汪，要有活力'),
(3, '模仿小鸟叫声', 'animal', 2, '叽叽喳喳，要清脆'),
(4, '模仿老虎叫声', 'animal', 2, '要有威严的感觉'),
(5, '模仿青蛙叫声', 'animal', 1, '呱呱呱，要有节奏'),
(6, '模仿鸭子叫声', 'animal', 1, '嘎嘎嘎，要搞笑'),
(7, '模仿猪叫声', 'animal', 2, '哼哼哼，要憨厚'),
(8, '模仿牛叫声', 'animal', 2, '哞哞哞，要低沉'),

-- 方言模仿
(9, '用东北话说"这嘎达真好"', 'dialect', 2, '要有东北味儿'),
(10, '用四川话说"巴适得很"', 'dialect', 3, '要有川味'),
(11, '用广东话说"好靓仔"', 'dialect', 3, '要有粤语味道'),
(12, '用台湾腔说"超级棒"', 'dialect', 2, '要有台湾腔调'),

-- 声音特效
(13, '模仿机器人说话', 'effect', 2, '要有电子音效果'),
(14, '模仿老爷爷说话', 'effect', 2, '声音要苍老'),
(15, '模仿小婴儿说话', 'effect', 2, '要奶声奶气'),
(16, '模仿外星人说话', 'effect', 3, '要有神秘感'),

-- 情感表达
(17, '用最开心的语气说"我爱你"', 'emotion', 1, '要充满喜悦'),
(18, '用最害羞的语气说"你好帅"', 'emotion', 2, '要很害羞'),
(19, '用最生气的语气说"不可以"', 'emotion', 2, '要有怒气'),
(20, '用最温柔的语气说"晚安"', 'emotion', 1, '要很温柔');

-- 插入成就数据
INSERT OR IGNORE INTO `achievement` (`achievement_key`, `title`, `description`, `icon`, `category`, `requirement_type`, `requirement_value`, `points`) VALUES
-- 游戏入门成就
('first_game', '初次体验', '完成第一个游戏', '🎮', 'beginner', 'count', 1, 10),
('game_explorer', '游戏探索者', '尝试5种不同的游戏模式', '🗺️', 'beginner', 'count', 5, 25),
('daily_player', '每日玩家', '连续7天游戏', '📅', 'beginner', 'count', 7, 30),

-- 游戏精通成就
('quiz_master', '问答达人', '爱情问答获得100分', '🧠', 'mastery', 'score', 100, 50),
('word_chain_pro', '接龙高手', '文字接龙连续答对10个', '🔤', 'mastery', 'count', 10, 40),
('photo_artist', '摄影艺术家', '主题摄影获得5次胜利', '📸', 'mastery', 'count', 5, 35),
('music_genius', '音乐天才', '音乐接龙猜对10首歌', '🎵', 'mastery', 'count', 10, 45),

-- 情侣互动成就
('sweet_couple', '甜蜜情侣', '完成10次情话接力', '💕', 'couple', 'count', 10, 40),
('role_player', '角色扮演家', '尝试20个不同角色', '🎭', 'couple', 'count', 20, 50),
('future_prophet', '未来预言家', '预测准确率达到80%', '🔮', 'couple', 'percentage', 80, 60),

-- 特殊成就
('lucky_winner', '幸运儿', '老虎机中奖10次', '🍀', 'special', 'count', 10, 30),
('treasure_hunter', '寻宝专家', '完成5次寻宝游戏', '🗺️', 'special', 'count', 5, 35),
('voice_mimic', '声音模仿家', '语音挑战获得满分5次', '🎤', 'special', 'count', 5, 40),

-- 里程碑成就
('century_club', '百场俱乐部', '总共游戏100场', '💯', 'milestone', 'count', 100, 100),
('score_master', '得分大师', '累计获得1000分', '⭐', 'milestone', 'score', 1000, 80),
('time_lord', '时间领主', '累计游戏时间达到10小时', '⏰', 'milestone', 'time', 600, 70),

-- 社交成就
('social_butterfly', '社交达人', '与5个不同的人游戏', '🦋', 'social', 'count', 5, 45),
('team_player', '团队合作', '合作游戏获胜20次', '🤝', 'social', 'count', 20, 55);

-- 插入游戏设置数据
INSERT OR IGNORE INTO `game_settings` (`setting_key`, `setting_value`, `setting_type`, `description`) VALUES
('slot_win_probability', '30', 'integer', '老虎机中奖概率百分比(1-100)'),
('slot_special_probability', '15', 'integer', '老虎机特殊组合概率百分比(1-100)'),
('slot_animation_speed', '3000', 'integer', '老虎机动画持续时间(毫秒)'),
('game_difficulty_mode', 'normal', 'string', '游戏难度模式: easy, normal, hard'),
('enable_sound_effects', 'true', 'boolean', '是否启用音效'),
('max_players_per_room', '10', 'integer', '每个房间最大玩家数量');

-- 插入老虎机组合数据
INSERT OR IGNORE INTO `slot_combinations` (`id`, `name`, `symbol1_id`, `symbol2_id`, `symbol3_id`, `activity_title`, `activity_content`, `activity_type`, `intensity_level`) VALUES
-- 三个相同符号的组合
(1, '三心连击', 1, 1, 1, '深情告白时刻', '看着对方的眼睛，说出三句最想对TA说的话', 1, 2),
(2, '热吻三连', 2, 2, 2, '激情亲吻挑战', '在三个不同的地点各亲吻对方一次', 1, 3),
(3, '玫瑰花园', 3, 3, 3, '浪漫玫瑰仪式', '为对方准备一束玫瑰花或画一朵玫瑰', 1, 2),
(4, '钻石永恒', 4, 4, 4, '永恒承诺', '互相许下一个关于未来的美好承诺', 1, 3),
(5, '完美情侣', 5, 5, 5, '情侣默契挑战', '不说话的情况下，猜对方现在在想什么', 1, 2),

-- 浪漫组合
(6, '烛光晚餐', 6, 3, 1, '浪漫烛光晚餐', '一起准备一顿烛光晚餐，营造浪漫氛围', 1, 2),
(7, '电影之夜', 7, 11, 15, '私人电影院', '选择一部两人都喜欢的电影一起观看', 1, 1),
(8, '按摩时光', 8, 1, 12, '放松按摩', '为对方进行15分钟的肩颈按摩', 1, 1),
(9, '礼物惊喜', 9, 12, 4, '神秘礼物', '为对方准备一个小惊喜礼物', 1, 2),
(10, '月下舞蹈', 10, 13, 11, '月光下的舞蹈', '在月光下或灯光下一起慢舞', 1, 2),

-- 挑战组合
(11, '火热挑战', 14, 2, 10, '激情挑战', '学习一支简单的双人舞蹈', 2, 3),
(12, '甜蜜诱惑', 15, 2, 1, '甜蜜喂食', '互相喂对方吃樱桃或其他水果', 2, 2),
(13, '星空许愿', 12, 13, 1, '星空下许愿', '一起看星星并许下共同的愿望', 1, 1),
(14, '音乐情缘', 11, 1, 3, '情歌对唱', '一起唱一首两人都喜欢的情歌', 1, 2),
(15, '浪漫野餐', 6, 12, 3, '户外野餐', '准备野餐篮，找个美丽的地方野餐', 1, 2),

-- 更多随机组合
(16, '创意组合', 7, 8, 9, '创意时光', '一起制作一个小手工艺品作为纪念', 1, 1),
(17, '运动挑战', 10, 14, 5, '运动情侣', '一起做30分钟的运动或瑜伽', 2, 2),
(18, '美食探险', 6, 15, 11, '美食制作', '一起尝试制作一道新的菜肴', 1, 2),
(19, '艺术创作', 3, 12, 7, '艺术时光', '一起画画或写诗，表达对彼此的爱', 1, 1),
(20, '冒险时刻', 14, 13, 10, '小冒险', '计划一次小小的冒险活动（如夜游）', 2, 3);

INSERT OR IGNORE INTO `punishment_reward` (`id`, `title`, `content`, `type`, `media_type`, `level`, `tags`, `relationship_types`) VALUES
-- 朋友关系类 (Level 1-2)
(1, '做家务', '洗碗或扫地30分钟', 2, 0, 1, '轻松,友谊,搞笑', 'same_gender_friend,opposite_gender_friend,close_friend'),
(2, '表演才艺', '唱歌或跳舞表演一首', 2, 0, 1, '轻松,友谊,搞笑,娱乐', 'same_gender_friend,opposite_gender_friend,close_friend'),
(3, '做运动', '做50个俯卧撑', 2, 0, 2, '轻松,友谊,搞笑,运动', 'same_gender_friend,opposite_gender_friend,close_friend'),
(4, '禁用手机', '1小时内不能使用手机', 2, 0, 2, '轻松,友谊,挑战', 'same_gender_friend,opposite_gender_friend,close_friend'),
(5, '模仿动物', '模仿一种动物叫声和动作', 2, 0, 1, '轻松,友谊,搞笑,娱乐', 'same_gender_friend,opposite_gender_friend,close_friend'),
(6, '讲笑话', '讲三个笑话让对方开心', 2, 0, 1, '轻松,友谊,搞笑,娱乐', 'same_gender_friend,opposite_gender_friend,close_friend'),
(7, '请客吃饭', '请对方吃一顿饭', 2, 0, 2, '友谊,轻松', 'same_gender_friend,opposite_gender_friend,close_friend'),
(8, '夸奖对方', '真诚地夸奖对方三个优点', 1, 0, 1, '轻松,友谊,表达', 'same_gender_friend,opposite_gender_friend,close_friend'),
(9, '分享秘密', '分享一个小秘密', 1, 0, 1, '友谊,表达', 'close_friend'),
(10, '拍搞笑照片', '一起拍三张搞笑照片', 1, 0, 1, '轻松,友谊,搞笑,娱乐', 'same_gender_friend,opposite_gender_friend,close_friend'),

-- 暧昧关系类 (Level 2-3)
(11, '按摩服务', '为对方按摩10分钟', 1, 0, 2, '轻松,友谊', 'close_friend,ambiguous'),
(12, '购买礼物', '为对方买一份小礼物', 2, 0, 2, '友谊,浪漫', 'close_friend,ambiguous,new_couple'),
(13, '牵手散步', '牵手散步15分钟', 1, 0, 2, '浪漫,轻松', 'ambiguous,new_couple'),
(14, '拥抱30秒', '给对方一个30秒的拥抱', 1, 0, 2, '浪漫,轻松', 'ambiguous,new_couple'),
(15, '喂对方吃东西', '亲手喂对方吃一样东西', 1, 0, 3, '浪漫,情侣', 'ambiguous,new_couple'),
(16, '说甜言蜜语', '对对方说三句甜言蜜语', 1, 0, 2, '浪漫,情侣,表达', 'ambiguous,new_couple,passionate_couple'),
(17, '唱情歌', '为对方唱一首情歌', 2, 0, 2, '浪漫,情侣,娱乐,表达', 'ambiguous,new_couple,passionate_couple'),
(18, '写小纸条', '写一张浪漫的小纸条', 1, 0, 2, '浪漫,情侣,表达', 'ambiguous,new_couple'),

-- 情侣关系类 (Level 3-5)
(19, '浪漫晚餐', '准备一顿浪漫晚餐', 1, 0, 3, '浪漫,情侣', 'new_couple,passionate_couple,intimate_couple'),
(20, '写情书', '手写一封情书', 1, 0, 3, '浪漫,情侣,表达', 'new_couple,passionate_couple,intimate_couple'),
(21, '计划约会', '计划一次浪漫约会', 1, 0, 3, '浪漫,情侣', 'new_couple,passionate_couple,intimate_couple'),
(22, '拍情侣照', '拍一组情侣照片', 1, 0, 3, '浪漫,情侣', 'new_couple,passionate_couple,intimate_couple'),
(23, '制作视频', '制作一个爱情视频', 1, 0, 4, '浪漫,情侣,表达', 'passionate_couple,intimate_couple'),
(24, '准备惊喜', '为对方准备一个惊喜', 1, 0, 4, '浪漫,情侣', 'passionate_couple,intimate_couple'),
(25, '情侣装扮', '穿情侣装一整天', 2, 0, 3, '浪漫,情侣', 'new_couple,passionate_couple'),
(26, '学习新技能', '一起学习对方感兴趣的技能', 1, 0, 3, '情侣,挑战', 'new_couple,passionate_couple,intimate_couple'),
(27, '制作礼物', '亲手制作一份礼物', 1, 0, 4, '浪漫,情侣,表达', 'passionate_couple,intimate_couple'),
(28, '写日记', '写一篇关于对方的日记', 1, 0, 3, '浪漫,情侣,表达', 'new_couple,passionate_couple,intimate_couple'),
(29, '角色扮演', '进行一次角色扮演', 2, 0, 5, '情侣,娱乐,挑战', 'intimate_couple'),
(30, '深度对话', '进行一次深度心灵对话', 1, 0, 4, '情侣,表达', 'passionate_couple,intimate_couple');