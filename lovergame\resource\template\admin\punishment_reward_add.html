<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>添加奖惩内容 - 后台管理</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css">
    <style>
        body {
            background-color: #f8f9fa;
        }
        .sidebar {
            min-height: 100vh;
            background: linear-gradient(180deg, #667eea 0%, #764ba2 100%);
        }
        .sidebar .nav-link {
            color: rgba(255,255,255,0.8);
            padding: 12px 20px;
            margin: 5px 10px;
            border-radius: 8px;
            transition: all 0.3s;
        }
        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            color: white;
            background-color: rgba(255,255,255,0.2);
        }
        .main-content {
            padding: 20px;
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .form-label {
            font-weight: 600;
            color: #495057;
        }
        .btn-gradient {
            background: linear-gradient(45deg, #667eea, #764ba2);
            border: none;
            color: white;
        }
        .btn-gradient:hover {
            background: linear-gradient(45deg, #764ba2, #667eea);
            color: white;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- 侧边栏 -->
            <div class="col-md-2 sidebar">
                <div class="p-3">
                    <h4 class="text-white mb-4">
                        <i class="bi bi-gear-fill"></i> 后台管理
                    </h4>
                    <nav class="nav flex-column">
                        <a class="nav-link" href="/admin">
                            <i class="bi bi-house-door"></i> 首页
                        </a>
                        <a class="nav-link active" href="/admin/punishment-reward">
                            <i class="bi bi-list-ul"></i> 奖惩管理
                        </a>
                        <a class="nav-link" href="/">
                            <i class="bi bi-arrow-left"></i> 返回游戏
                        </a>
                    </nav>
                </div>
            </div>

            <!-- 主内容区 -->
            <div class="col-md-10 main-content">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2><i class="bi bi-plus-circle"></i> 添加奖惩内容</h2>
                    <a href="/admin/punishment-reward" class="btn btn-outline-secondary">
                        <i class="bi bi-arrow-left"></i> 返回列表
                    </a>
                </div>

                <div class="card">
                    <div class="card-body">
                        <form id="addForm">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="title" class="form-label">标题 *</label>
                                        <input type="text" class="form-control" id="title" name="title" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="type" class="form-label">类型 *</label>
                                        <select class="form-select" id="type" name="type" required>
                                            <option value="">请选择类型</option>
                                            <option value="1">🎁 奖励</option>
                                            <option value="2">⚡ 惩罚</option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="content" class="form-label">内容描述 *</label>
                                <textarea class="form-control" id="content" name="content" rows="3" required placeholder="请详细描述奖惩内容..."></textarea>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="level" class="form-label">级别 *</label>
                                        <select class="form-select" id="level" name="level" required>
                                            <option value="">请选择级别</option>
                                            <option value="1">😊 轻微</option>
                                            <option value="2">😐 一般</option>
                                            <option value="3">😰 严重</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="isActive" class="form-label">状态 *</label>
                                        <select class="form-select" id="isActive" name="isActive" required>
                                            <option value="1">✅ 启用</option>
                                            <option value="0">❌ 禁用</option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="mediaType" class="form-label">媒体类型</label>
                                        <select class="form-select" id="mediaType" name="mediaType">
                                            <option value="0">无媒体</option>
                                            <option value="1">📷 图片</option>
                                            <option value="2">🎵 音频</option>
                                            <option value="3">🎬 视频</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="mediaFile" class="form-label">媒体文件</label>
                                        <input type="file" class="form-control" id="mediaFile" accept="image/*,audio/*,video/*">
                                        <input type="hidden" id="mediaUrl" name="mediaUrl">
                                        <small class="text-muted">支持图片、音频、视频文件</small>
                                    </div>
                                </div>
                            </div>

                            <div class="d-flex gap-2">
                                <button type="submit" class="btn btn-gradient">
                                    <i class="bi bi-check-lg"></i> 添加内容
                                </button>
                                <button type="reset" class="btn btn-outline-secondary">
                                    <i class="bi bi-arrow-clockwise"></i> 重置
                                </button>
                                <a href="/admin/punishment-reward" class="btn btn-outline-secondary">
                                    <i class="bi bi-x-lg"></i> 取消
                                </a>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 文件上传处理
        document.getElementById('mediaFile').addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (!file) return;

            const formData = new FormData();
            formData.append('file', file);

            fetch('/admin/upload', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.code === 0) {
                    document.getElementById('mediaUrl').value = data.data.url;
                    alert('文件上传成功！');
                } else {
                    alert('文件上传失败：' + data.msg);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('文件上传失败，请重试');
            });
        });

        // 表单提交处理
        document.getElementById('addForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const formData = new FormData(this);
            const data = Object.fromEntries(formData);

            // 转换数据类型
            data.type = parseInt(data.type);
            data.level = parseInt(data.level);
            data.mediaType = parseInt(data.mediaType);
            data.isActive = parseInt(data.isActive);

            fetch('/admin/punishment-reward/add', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(data)
            })
            .then(response => response.json())
            .then(data => {
                if (data.code === 0) {
                    alert('添加成功！');
                    window.location.href = '/admin/punishment-reward';
                } else {
                    alert('添加失败：' + data.msg);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('添加失败，请重试');
            });
        });
    </script>
</body>
</html>
