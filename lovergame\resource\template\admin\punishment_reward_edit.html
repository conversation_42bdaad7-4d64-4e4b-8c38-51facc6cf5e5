<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>编辑奖惩内容 - 后台管理</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css">
    <style>
        body {
            background-color: #f8f9fa;
        }
        .sidebar {
            min-height: 100vh;
            background: linear-gradient(180deg, #667eea 0%, #764ba2 100%);
        }
        .sidebar .nav-link {
            color: rgba(255,255,255,0.8);
            padding: 12px 20px;
            margin: 5px 10px;
            border-radius: 8px;
            transition: all 0.3s;
        }
        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            color: white;
            background-color: rgba(255,255,255,0.2);
        }
        .main-content {
            padding: 20px;
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .form-label {
            font-weight: 600;
            color: #495057;
        }
        .btn-gradient {
            background: linear-gradient(45deg, #667eea, #764ba2);
            border: none;
            color: white;
        }
        .btn-gradient:hover {
            background: linear-gradient(45deg, #764ba2, #667eea);
            color: white;
        }
        .media-preview {
            max-width: 200px;
            max-height: 200px;
            border-radius: 8px;
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- 侧边栏 -->
            <div class="col-md-2 sidebar">
                <div class="p-3">
                    <h4 class="text-white mb-4">
                        <i class="bi bi-gear-fill"></i> 后台管理
                    </h4>
                    <nav class="nav flex-column">
                        <a class="nav-link" href="/admin">
                            <i class="bi bi-house-door"></i> 首页
                        </a>
                        <a class="nav-link active" href="/admin/punishment-reward">
                            <i class="bi bi-list-ul"></i> 奖惩管理
                        </a>
                        <a class="nav-link" href="/">
                            <i class="bi bi-arrow-left"></i> 返回游戏
                        </a>
                    </nav>
                </div>
            </div>

            <!-- 主内容区 -->
            <div class="col-md-10 main-content">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2><i class="bi bi-pencil-square"></i> 编辑奖惩内容</h2>
                    <a href="/admin/punishment-reward" class="btn btn-outline-secondary">
                        <i class="bi bi-arrow-left"></i> 返回列表
                    </a>
                </div>

                <div class="card">
                    <div class="card-body">
                        <form id="editForm">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="title" class="form-label">标题 *</label>
                                        <input type="text" class="form-control" id="title" name="title" value="{{.item.Title}}" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="type" class="form-label">类型 *</label>
                                        <select class="form-select" id="type" name="type" required>
                                            <option value="1" {{if eq .item.Type 1}}selected{{end}}>🎁 奖励</option>
                                            <option value="2" {{if eq .item.Type 2}}selected{{end}}>⚡ 惩罚</option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="content" class="form-label">内容描述 *</label>
                                <textarea class="form-control" id="content" name="content" rows="3" required>{{.item.Content}}</textarea>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="level" class="form-label">级别 *</label>
                                        <select class="form-select" id="level" name="level" required>
                                            <option value="1" {{if eq .item.Level 1}}selected{{end}}>😊 轻微</option>
                                            <option value="2" {{if eq .item.Level 2}}selected{{end}}>😐 一般</option>
                                            <option value="3" {{if eq .item.Level 3}}selected{{end}}>😰 严重</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="isActive" class="form-label">状态 *</label>
                                        <select class="form-select" id="isActive" name="isActive" required>
                                            <option value="1" {{if eq .item.IsActive 1}}selected{{end}}>✅ 启用</option>
                                            <option value="0" {{if eq .item.IsActive 0}}selected{{end}}>❌ 禁用</option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="tags" class="form-label">内容标签</label>
                                        <div id="tagsContainer">
                                            <div class="form-check form-check-inline">
                                                <input class="form-check-input" type="checkbox" id="tag_轻松" value="轻松">
                                                <label class="form-check-label" for="tag_轻松">轻松</label>
                                            </div>
                                            <div class="form-check form-check-inline">
                                                <input class="form-check-input" type="checkbox" id="tag_友谊" value="友谊">
                                                <label class="form-check-label" for="tag_友谊">友谊</label>
                                            </div>
                                            <div class="form-check form-check-inline">
                                                <input class="form-check-input" type="checkbox" id="tag_搞笑" value="搞笑">
                                                <label class="form-check-label" for="tag_搞笑">搞笑</label>
                                            </div>
                                            <div class="form-check form-check-inline">
                                                <input class="form-check-input" type="checkbox" id="tag_浪漫" value="浪漫">
                                                <label class="form-check-label" for="tag_浪漫">浪漫</label>
                                            </div>
                                            <div class="form-check form-check-inline">
                                                <input class="form-check-input" type="checkbox" id="tag_情侣" value="情侣">
                                                <label class="form-check-label" for="tag_情侣">情侣</label>
                                            </div>
                                            <div class="form-check form-check-inline">
                                                <input class="form-check-input" type="checkbox" id="tag_挑战" value="挑战">
                                                <label class="form-check-label" for="tag_挑战">挑战</label>
                                            </div>
                                            <div class="form-check form-check-inline">
                                                <input class="form-check-input" type="checkbox" id="tag_娱乐" value="娱乐">
                                                <label class="form-check-label" for="tag_娱乐">娱乐</label>
                                            </div>
                                            <div class="form-check form-check-inline">
                                                <input class="form-check-input" type="checkbox" id="tag_运动" value="运动">
                                                <label class="form-check-label" for="tag_运动">运动</label>
                                            </div>
                                            <div class="form-check form-check-inline">
                                                <input class="form-check-input" type="checkbox" id="tag_表达" value="表达">
                                                <label class="form-check-label" for="tag_表达">表达</label>
                                            </div>
                                        </div>
                                        <input type="hidden" id="tags" name="tags" value="{{.item.Tags}}">
                                        <small class="text-muted">选择适合的内容标签，用于预设筛选</small>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="relationshipTypes" class="form-label">适用关系类型</label>
                                        <div id="relationshipTypesContainer">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="rel_same_gender_friend" value="same_gender_friend">
                                                <label class="form-check-label" for="rel_same_gender_friend">普通同性朋友</label>
                                            </div>
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="rel_opposite_gender_friend" value="opposite_gender_friend">
                                                <label class="form-check-label" for="rel_opposite_gender_friend">普通异性朋友</label>
                                            </div>
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="rel_close_friend" value="close_friend">
                                                <label class="form-check-label" for="rel_close_friend">铁哥们/闺蜜</label>
                                            </div>
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="rel_ambiguous" value="ambiguous">
                                                <label class="form-check-label" for="rel_ambiguous">暧昧期朋友</label>
                                            </div>
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="rel_new_couple" value="new_couple">
                                                <label class="form-check-label" for="rel_new_couple">刚确认关系的情侣</label>
                                            </div>
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="rel_passionate_couple" value="passionate_couple">
                                                <label class="form-check-label" for="rel_passionate_couple">热恋期情侣</label>
                                            </div>
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="rel_intimate_couple" value="intimate_couple">
                                                <label class="form-check-label" for="rel_intimate_couple">深度亲密情侣</label>
                                            </div>
                                        </div>
                                        <input type="hidden" id="relationshipTypes" name="relationshipTypes" value="{{.item.RelationshipTypes}}">
                                        <small class="text-muted">选择适合的关系类型，用于预设筛选</small>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="mediaType" class="form-label">媒体类型</label>
                                        <select class="form-select" id="mediaType" name="mediaType">
                                            <option value="0" {{if eq .item.MediaType 0}}selected{{end}}>无媒体</option>
                                            <option value="1" {{if eq .item.MediaType 1}}selected{{end}}>📷 图片</option>
                                            <option value="2" {{if eq .item.MediaType 2}}selected{{end}}>🎵 音频</option>
                                            <option value="3" {{if eq .item.MediaType 3}}selected{{end}}>🎬 视频</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="mediaFile" class="form-label">媒体文件</label>
                                        <input type="file" class="form-control" id="mediaFile" accept="image/*,audio/*,video/*">
                                        <input type="hidden" id="mediaUrl" name="mediaUrl" value="{{.item.MediaUrl}}">
                                        <small class="text-muted">支持图片、音频、视频文件</small>
                                    </div>
                                </div>
                            </div>

                            {{if .item.MediaUrl}}
                            <div class="mb-3">
                                <label class="form-label">当前媒体文件</label>
                                <div id="currentMedia">
                                    {{if eq .item.MediaType 1}}
                                        <img src="{{.item.MediaUrl}}" class="media-preview" alt="当前图片">
                                    {{else if eq .item.MediaType 2}}
                                        <audio controls class="d-block">
                                            <source src="{{.item.MediaUrl}}" type="audio/mpeg">
                                        </audio>
                                    {{else if eq .item.MediaType 3}}
                                        <video controls class="media-preview">
                                            <source src="{{.item.MediaUrl}}" type="video/mp4">
                                        </video>
                                    {{end}}
                                </div>
                            </div>
                            {{end}}

                            <div class="d-flex gap-2">
                                <button type="submit" class="btn btn-gradient">
                                    <i class="bi bi-check-lg"></i> 保存修改
                                </button>
                                <a href="/admin/punishment-reward" class="btn btn-outline-secondary">
                                    <i class="bi bi-x-lg"></i> 取消
                                </a>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 文件上传处理
        document.getElementById('mediaFile').addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (!file) return;

            const formData = new FormData();
            formData.append('file', file);

            fetch('/admin/upload', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.code === 0) {
                    document.getElementById('mediaUrl').value = data.data.url;
                    alert('文件上传成功！');
                } else {
                    alert('文件上传失败：' + data.msg);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('文件上传失败，请重试');
            });
        });

        // 收集选中的标签
        function collectTags() {
            const selectedTags = [];
            document.querySelectorAll('#tagsContainer input[type="checkbox"]:checked').forEach(checkbox => {
                selectedTags.push(checkbox.value);
            });
            document.getElementById('tags').value = selectedTags.join(',');
        }

        // 收集选中的关系类型
        function collectRelationshipTypes() {
            const selectedTypes = [];
            document.querySelectorAll('#relationshipTypesContainer input[type="checkbox"]:checked').forEach(checkbox => {
                selectedTypes.push(checkbox.value);
            });
            document.getElementById('relationshipTypes').value = selectedTypes.join(',');
        }

        // 初始化已选中的标签和关系类型
        function initializeSelections() {
            const tags = document.getElementById('tags').value;
            if (tags) {
                const tagArray = tags.split(',');
                tagArray.forEach(tag => {
                    const checkbox = document.getElementById('tag_' + tag.trim());
                    if (checkbox) checkbox.checked = true;
                });
            }

            const relationshipTypes = document.getElementById('relationshipTypes').value;
            if (relationshipTypes) {
                const typeArray = relationshipTypes.split(',');
                typeArray.forEach(type => {
                    const checkbox = document.getElementById('rel_' + type.trim());
                    if (checkbox) checkbox.checked = true;
                });
            }
        }

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            initializeSelections();
        });

        // 表单提交处理
        document.getElementById('editForm').addEventListener('submit', function(e) {
            e.preventDefault();

            // 收集标签和关系类型
            collectTags();
            collectRelationshipTypes();

            const formData = new FormData(this);
            const data = Object.fromEntries(formData);

            // 转换数据类型
            data.type = parseInt(data.type);
            data.level = parseInt(data.level);
            data.mediaType = parseInt(data.mediaType);
            data.isActive = parseInt(data.isActive);

            fetch(window.location.href, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(data)
            })
            .then(response => response.json())
            .then(data => {
                if (data.code === 0) {
                    alert('保存成功！');
                    window.location.href = '/admin/punishment-reward';
                } else {
                    alert('保存失败：' + data.msg);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('保存失败，请重试');
            });
        });
    </script>
</body>
</html>
