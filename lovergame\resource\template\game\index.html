<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{.title}}</title>
    <link rel="icon" href="data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 viewBox=%220 0 100 100%22><text y=%22.9em%22 font-size=%2290%22>🎮</text></svg>">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Arial', sans-serif;
        }
        .game-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            padding: 40px;
            margin-top: 50px;
        }
        .player-card {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 20px;
            margin: 10px;
            border: 3px solid transparent;
            transition: all 0.3s ease;
        }
        .player-card.selected {
            border-color: #dc3545;
            background: #fff5f5;
            transform: scale(1.05);
        }
        .start-btn {
            background: linear-gradient(45deg, #FF6B6B, #4ECDC4);
            border: none;
            border-radius: 50px;
            padding: 15px 40px;
            font-size: 18px;
            font-weight: bold;
            color: white;
            transition: all 0.3s ease;
        }
        .start-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.2);
        }
        .result-card {
            background: #fff;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            margin-top: 20px;
            display: none;
        }
        .media-container {
            text-align: center;
            margin: 20px 0;
        }
        .media-container img, .media-container video {
            max-width: 100%;
            max-height: 300px;
            border-radius: 10px;
        }
        .roulette {
            width: 200px;
            height: 200px;
            border: 10px solid #ddd;
            border-radius: 50%;
            margin: 20px auto;
            position: relative;
            animation: spin 2s linear infinite;
        }
        .roulette.stopped {
            animation: none;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .roulette::before {
            content: '';
            position: absolute;
            top: -20px;
            left: 50%;
            transform: translateX(-50%);
            width: 0;
            height: 0;
            border-left: 15px solid transparent;
            border-right: 15px solid transparent;
            border-bottom: 20px solid #dc3545;
        }

        /* 老虎机样式 */
        .slot-machine-container {
            background: linear-gradient(145deg, #FFD700, #FFA500);
            border-radius: 20px;
            padding: 30px;
            margin: 20px auto;
            max-width: 400px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            border: 3px solid #B8860B;
        }
        .slot-reels {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin: 20px 0;
        }
        .slot-reel {
            width: 90px;
            height: 120px;
            background: linear-gradient(180deg, #f8f9fa, #e9ecef);
            border: 4px solid #333;
            border-radius: 15px;
            position: relative;
            overflow: hidden;
            box-shadow: inset 0 2px 10px rgba(0,0,0,0.2);
        }
        .slot-reel::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(180deg,
                rgba(255,255,255,0.3) 0%,
                transparent 20%,
                transparent 80%,
                rgba(0,0,0,0.1) 100%);
            pointer-events: none;
            z-index: 2;
        }
        .slot-symbol-container {
            position: absolute;
            width: 100%;
            height: 100%;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }
        .slot-reel.spinning .slot-symbol-container {
            animation: slotSpin 0.15s ease-in-out infinite;
        }
        .slot-symbol {
            font-size: 45px;
            line-height: 1;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
            transition: transform 0.3s ease;
        }
        .slot-reel:not(.spinning) .slot-symbol {
            animation: symbolBounce 0.6s ease-out;
        }

        /* 改进的转动动画 */
        @keyframes slotSpin {
            0% { transform: translateY(0px); }
            25% { transform: translateY(-30px); }
            50% { transform: translateY(-60px); }
            75% { transform: translateY(-30px); }
            100% { transform: translateY(0px); }
        }

        /* 停止时的弹跳效果 */
        @keyframes symbolBounce {
            0% { transform: scale(0.8) rotateY(90deg); }
            50% { transform: scale(1.1) rotateY(0deg); }
            100% { transform: scale(1) rotateY(0deg); }
        }

        /* 发光效果 */
        .slot-reel.winning {
            animation: slotGlow 1s ease-in-out infinite alternate;
            border-color: #FFD700;
        }

        @keyframes slotGlow {
            0% {
                box-shadow: inset 0 2px 10px rgba(0,0,0,0.2),
                           0 0 20px rgba(255, 215, 0, 0.5);
            }
            100% {
                box-shadow: inset 0 2px 10px rgba(0,0,0,0.2),
                           0 0 30px rgba(255, 215, 0, 0.8);
            }
        }

        /* 老虎机结果样式 */
        .slot-result {
            background: linear-gradient(145deg, #FF6B6B, #4ECDC4);
            color: white;
            border-radius: 15px;
            padding: 20px;
            margin: 20px 0;
            animation: resultAppear 0.8s ease-out;
        }
        .slot-combination {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin: 15px 0;
        }
        .slot-combination .symbol {
            font-size: 35px;
            background: rgba(255,255,255,0.3);
            border-radius: 50%;
            width: 60px;
            height: 60px;
            display: flex;
            align-items: center;
            justify-content: center;
            border: 2px solid rgba(255,255,255,0.5);
            animation: symbolPop 0.6s ease-out;
            animation-delay: calc(var(--delay) * 0.2s);
            transform: scale(0);
            animation-fill-mode: forwards;
        }

        @keyframes resultAppear {
            0% {
                opacity: 0;
                transform: translateY(30px) scale(0.8);
            }
            100% {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        @keyframes symbolPop {
            0% {
                transform: scale(0) rotate(180deg);
                opacity: 0;
            }
            70% {
                transform: scale(1.2) rotate(0deg);
                opacity: 1;
            }
            100% {
                transform: scale(1) rotate(0deg);
                opacity: 1;
            }
        }

        /* 获胜时的特殊效果 */
        .slot-result.winning {
            background: linear-gradient(145deg, #FFD700, #FFA500);
            animation: resultAppear 0.8s ease-out, winningPulse 2s ease-in-out infinite;
        }

        @keyframes winningPulse {
            0%, 100% {
                box-shadow: 0 0 20px rgba(255, 215, 0, 0.5);
                transform: scale(1);
            }
            50% {
                box-shadow: 0 0 40px rgba(255, 215, 0, 0.8);
                transform: scale(1.02);
            }
        }

        /* 真心话大冒险样式 */
        .truth-dare-container {
            background: linear-gradient(145deg, #FF6B6B, #4ECDC4);
            border-radius: 20px;
            padding: 30px;
            margin: 20px auto;
            max-width: 500px;
            color: white;
            text-align: center;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        }

        .truth-dare-type {
            font-size: 60px;
            margin-bottom: 20px;
            animation: typeReveal 1s ease-out;
        }

        .truth-dare-question {
            background: rgba(255,255,255,0.2);
            border-radius: 15px;
            padding: 20px;
            margin: 20px 0;
            animation: questionSlide 1.2s ease-out;
        }

        @keyframes typeReveal {
            0% {
                opacity: 0;
                transform: rotateY(90deg) scale(0.5);
            }
            100% {
                opacity: 1;
                transform: rotateY(0deg) scale(1);
            }
        }

        @keyframes questionSlide {
            0% {
                opacity: 0;
                transform: translateY(30px);
            }
            100% {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* 玩家信息样式 */
        .player-info {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .player-card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            padding: 15px;
            text-align: center;
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }

        .player-card.active {
            background: rgba(255, 255, 255, 0.2);
            border-color: #ffd700;
            transform: scale(1.05);
            box-shadow: 0 5px 15px rgba(255, 215, 0, 0.3);
        }

        .player-card.winner {
            background: rgba(76, 175, 80, 0.2);
            border-color: #4caf50;
            animation: winnerPulse 2s infinite;
        }

        @keyframes winnerPulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .player-avatar {
            font-size: 2.5rem;
            margin-bottom: 10px;
            filter: drop-shadow(0 2px 4px rgba(0,0,0,0.3));
        }

        .player-name {
            font-size: 1.1rem;
            font-weight: bold;
            color: white;
            margin-bottom: 5px;
        }

        .player-status {
            font-size: 0.9rem;
            color: rgba(255, 255, 255, 0.8);
            min-height: 20px;
        }

        /* 骰子游戏样式 */
        .dice-container {
            background: linear-gradient(145deg, #667eea, #764ba2);
            border-radius: 20px;
            padding: 30px;
            margin: 20px auto;
            max-width: 500px;
            color: white;
            text-align: center;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        }

        .dice-display {
            display: flex;
            justify-content: center;
            gap: 30px;
            margin: 30px 0;
        }

        .dice {
            width: 80px;
            height: 80px;
            background: white;
            color: #333;
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 40px;
            font-weight: bold;
            box-shadow: 0 5px 15px rgba(0,0,0,0.3);
            animation: diceRoll 2s ease-out;
        }

        .dice-challenge {
            background: rgba(255,255,255,0.2);
            border-radius: 15px;
            padding: 20px;
            margin: 20px 0;
            animation: challengeAppear 1.5s ease-out;
        }

        @keyframes diceRoll {
            0% {
                transform: rotateX(0deg) rotateY(0deg);
            }
            25% {
                transform: rotateX(180deg) rotateY(90deg);
            }
            50% {
                transform: rotateX(360deg) rotateY(180deg);
            }
            75% {
                transform: rotateX(540deg) rotateY(270deg);
            }
            100% {
                transform: rotateX(720deg) rotateY(360deg);
            }
        }

        @keyframes challengeAppear {
            0% {
                opacity: 0;
                transform: scale(0.8) translateY(20px);
            }
            100% {
                opacity: 1;
                transform: scale(1) translateY(0);
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="game-container">
                    <h1 class="text-center mb-4">🎮 双人互动奖惩游戏</h1>
                    <p class="text-center text-muted mb-4">让命运决定谁来接受挑战吧！</p>

                    <!-- 游戏类型选择 -->
                    <div id="gameTypeSelection" class="text-center mb-4">
                        <h5>选择游戏类型</h5>
                        <div class="btn-group" role="group">
                            <button type="button" class="btn btn-outline-primary active" onclick="selectGameType('punishment_reward', event)">奖惩游戏</button>
                            <button type="button" class="btn btn-outline-danger" onclick="selectGameType('slot_machine', event)">爱情老虎机</button>
                            <button type="button" class="btn btn-outline-info" onclick="selectGameType('truth_dare', event)">真心话大冒险</button>
                            <button type="button" class="btn btn-outline-warning" onclick="selectGameType('dice_challenge', event)">浪漫骰子</button>
                            <button type="button" class="btn btn-outline-success" onclick="selectGameType('love_quiz', event)">💕 爱情问答</button>
                            <button type="button" class="btn btn-outline-secondary" onclick="selectGameType('photo_challenge', event)">📸 主题摄影</button>
                        </div>
                    </div>

                    <!-- 游戏模式选择 -->
                    <div id="modeSelection" class="text-center mb-4">
                        <h5>选择游戏模式</h5>
                        <div class="btn-group" role="group">
                            <button type="button" class="btn btn-outline-primary active" onclick="showLocalGame(event)">本地游戏</button>
                            <button type="button" class="btn btn-outline-success" onclick="showRoomGame(event)">房间游戏</button>
                        </div>
                    </div>

                    <!-- 本地游戏设置 -->
                    <div id="gameSetup">
                        <!-- 预设玩法选择 -->
                        <div class="row mb-4" id="presetSection" style="display: none;">
                            <div class="col-12">
                                <div class="alert alert-info">
                                    <h6>🎯 快速选择预设玩法</h6>
                                    <p class="mb-2">根据你们的关系选择合适的游戏内容，一键应用最佳设置！</p>
                                    <div class="row" id="presetButtons">
                                        <!-- 预设按钮将通过JavaScript动态加载 -->
                                    </div>
                                    <div class="mt-3">
                                        <button class="btn btn-outline-primary btn-sm" onclick="showAllPresets()">📋 查看所有预设</button>
                                        <button class="btn btn-outline-success btn-sm" onclick="showSavePresetModal()">💾 保存当前设置</button>
                                    </div>
                                </div>

                                <!-- 当前预设显示 -->
                                <div id="currentPresetInfo"></div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="player-card">
                                    <h5>🎯 玩家1</h5>
                                    <input type="text" class="form-control" id="player1Name" placeholder="请输入玩家1姓名">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="player-card">
                                    <h5>🎯 玩家2</h5>
                                    <input type="text" class="form-control" id="player2Name" placeholder="请输入玩家2姓名">
                                </div>
                            </div>
                        </div>
                        
                        <!-- 奖惩游戏设置 -->
                        <div id="punishmentRewardSettings" class="row mt-4">
                            <div class="col-md-6">
                                <label for="gameType" class="form-label">游戏类型</label>
                                <select class="form-select" id="gameType">
                                    <option value="0">随机（奖励+惩罚）</option>
                                    <option value="1">只有奖励</option>
                                    <option value="2">只有惩罚</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label for="gameLevel" class="form-label">游戏级别</label>
                                <select class="form-select" id="gameLevel">
                                    <option value="0">随机级别</option>
                                    <option value="1">轻微</option>
                                    <option value="2">一般</option>
                                    <option value="3">严重</option>
                                </select>
                            </div>
                        </div>

                        <!-- 老虎机游戏设置 -->
                        <div id="slotMachineSettings" class="row mt-4" style="display: none;">
                            <div class="col-md-12">
                                <div class="alert alert-info">
                                    <h6>🎰 爱情老虎机</h6>
                                    <p class="mb-0">转动爱情转盘，获得浪漫活动组合！三个符号的不同组合将决定你们的浪漫活动内容。</p>
                                </div>
                            </div>
                        </div>

                        <!-- 爱情问答设置 -->
                        <div id="loveQuizSettings" class="row mt-4" style="display: none;">
                            <div class="col-md-12">
                                <div class="alert alert-info">
                                    <h6>💕 爱情问答挑战</h6>
                                    <p class="mb-0">通过问答游戏增进彼此了解，看看谁更了解对方！</p>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <label for="quizType" class="form-label">问题类型</label>
                                <select class="form-select" id="quizType">
                                    <option value="0">随机类型</option>
                                    <option value="1">了解对方</option>
                                    <option value="2">共同回忆</option>
                                    <option value="3">未来计划</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label for="quizDifficulty" class="form-label">难度等级</label>
                                <select class="form-select" id="quizDifficulty">
                                    <option value="0">随机难度</option>
                                    <option value="1">简单 (10分)</option>
                                    <option value="2">中等 (15分)</option>
                                    <option value="3">困难 (20分)</option>
                                </select>
                            </div>
                            <div class="col-md-6 mt-3">
                                <label for="totalRounds" class="form-label">游戏轮数</label>
                                <select class="form-select" id="totalRounds">
                                    <option value="3">3轮</option>
                                    <option value="5" selected>5轮</option>
                                    <option value="10">10轮</option>
                                    <option value="15">15轮</option>
                                </select>
                            </div>
                            <div class="col-md-6 mt-3">
                                <label for="quizGameMode" class="form-label">游戏模式</label>
                                <select class="form-select" id="quizGameMode">
                                    <option value="competitive">竞争模式 (比分数)</option>
                                    <option value="cooperative">合作模式 (共同答题)</option>
                                </select>
                            </div>
                        </div>

                        <!-- 主题摄影设置 -->
                        <div id="photoChallengeSettings" class="row mt-4" style="display: none;">
                            <div class="col-md-12">
                                <div class="alert alert-info">
                                    <h6>📸 主题摄影挑战</h6>
                                    <p class="mb-0">根据给定主题拍摄创意照片，展现你们的想象力和创造力！</p>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <label for="photoCategory" class="form-label">主题类型</label>
                                <select class="form-select" id="photoCategory">
                                    <option value="all">随机主题</option>
                                    <option value="cute">可爱主题</option>
                                    <option value="romantic">浪漫主题</option>
                                    <option value="creative">创意主题</option>
                                    <option value="emotion">情感主题</option>
                                    <option value="interactive">互动主题</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label for="photoDifficulty" class="form-label">难度等级</label>
                                <select class="form-select" id="photoDifficulty">
                                    <option value="0">随机难度</option>
                                    <option value="1">简单</option>
                                    <option value="2">中等</option>
                                    <option value="3">困难</option>
                                </select>
                            </div>
                            <div class="col-md-12 mt-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="enableVoting" checked>
                                    <label class="form-check-label" for="enableVoting">
                                        启用投票功能（互相评选最佳照片）
                                    </label>
                                </div>
                            </div>
                        </div>

                        <!-- 其他游戏设置 -->
                        <div id="otherGameSettings" class="row mt-4" style="display: none;">
                            <div class="col-md-12">
                                <div class="alert alert-info">
                                    <h6>🎮 游戏说明</h6>
                                    <p class="mb-0">真心话大冒险和浪漫骰子游戏无需额外设置，直接开始游戏即可！</p>
                                </div>
                            </div>
                        </div>
                        
                        <div class="text-center mt-4">
                            <button class="btn start-btn" onclick="startGame()">🎲 开始游戏</button>
                        </div>
                    </div>
                    
                    <!-- 游戏进行中 -->
                    <div id="gameProgress" style="display: none;">
                        <!-- 奖惩游戏进行中 -->
                        <div id="punishmentRewardProgress" class="text-center">
                            <h3>🎰 正在选择...</h3>
                            <div class="roulette" id="roulette"></div>
                            <p class="text-muted">命运之轮正在转动...</p>
                        </div>

                        <!-- 老虎机游戏进行中 -->
                        <div id="slotMachineProgress" class="text-center" style="display: none;">
                            <h3>🎰 爱情老虎机转动中...</h3>
                            <div class="slot-machine-container">
                                <div class="slot-reels">
                                    <div class="slot-reel" id="reel1">
                                        <div class="slot-symbol-container">
                                            <div class="slot-symbol">❤️</div>
                                        </div>
                                    </div>
                                    <div class="slot-reel" id="reel2">
                                        <div class="slot-symbol-container">
                                            <div class="slot-symbol">💋</div>
                                        </div>
                                    </div>
                                    <div class="slot-reel" id="reel3">
                                        <div class="slot-symbol-container">
                                            <div class="slot-symbol">🌹</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <p class="text-muted">爱情转盘正在为你们选择浪漫活动...</p>
                        </div>
                    </div>
                    
                    <!-- 游戏结果 -->
                    <div id="gameResult" class="result-card">
                        <!-- 玩家信息显示 -->
                        <div id="playerInfo" class="player-info mb-4">
                            <div class="row">
                                <div class="col-6">
                                    <div class="player-card" id="player1Card">
                                        <div class="player-avatar">👤</div>
                                        <div class="player-name" id="displayPlayer1Name">玩家1</div>
                                        <div class="player-status" id="player1Status"></div>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="player-card" id="player2Card">
                                        <div class="player-avatar">👤</div>
                                        <div class="player-name" id="displayPlayer2Name">玩家2</div>
                                        <div class="player-status" id="player2Status"></div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="text-center">
                            <h3 id="resultTitle"></h3>
                            <div class="alert alert-info" id="selectedPlayer"></div>
                            <div class="card mt-3">
                                <div class="card-body">
                                    <h5 class="card-title" id="itemTitle"></h5>
                                    <p class="card-text" id="itemContent"></p>
                                    <div id="mediaContainer" class="media-container"></div>
                                </div>
                            </div>
                            <button class="btn btn-primary mt-3" onclick="continueGame()">🎮 继续游戏</button>
                            <button class="btn btn-outline-primary mt-3" onclick="resetGame()">🔄 重新设置</button>
                            <a href="/admin" class="btn btn-outline-secondary mt-3">⚙️ 后台管理</a>
                        </div>
                    </div>

                    <!-- 房间游戏界面 -->
                    <div id="roomGameSetup" style="display: none;">
                        <div class="row mb-3">
                            <div class="col-md-12">
                                <div class="btn-group w-100" role="group">
                                    <button type="button" class="btn btn-outline-primary" onclick="showCreateRoom()">创建房间</button>
                                    <button type="button" class="btn btn-outline-success" onclick="showJoinRoom()">加入房间</button>
                                </div>
                            </div>
                        </div>

                        <!-- 创建房间 -->
                        <div id="createRoomPanel" style="display: none;">
                            <h5>创建房间</h5>
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label for="roomName" class="form-label">房间名字</label>
                                    <input type="text" class="form-control" id="roomName" placeholder="请输入房间名字（可选）">
                                </div>
                                <div class="col-md-6">
                                    <label for="roomCreatorName" class="form-label">您的姓名</label>
                                    <input type="text" class="form-control" id="roomCreatorName" placeholder="请输入您的姓名">
                                </div>
                            </div>
                            <div class="row mb-3">
                                <div class="col-md-12">
                                    <label for="roomGameMode" class="form-label">游戏模式</label>
                                    <select class="form-select" id="roomGameMode">
                                        <option value="punishment_reward">奖惩游戏</option>
                                        <option value="slot_machine">爱情老虎机</option>
                                        <option value="truth_dare">真心话大冒险</option>
                                        <option value="dice_challenge">浪漫骰子</option>
                                        <option value="love_quiz">💕 爱情问答</option>
                                        <option value="photo_challenge">📸 主题摄影</option>
                                    </select>
                                </div>
                            </div>
                            <div class="row mb-3" id="roomPunishmentSettings">
                                <div class="col-md-6">
                                    <label for="roomGameType" class="form-label">游戏类型</label>
                                    <select class="form-select" id="roomGameType">
                                        <option value="0">随机类型</option>
                                        <option value="1">只有奖励</option>
                                        <option value="2">只有惩罚</option>
                                    </select>
                                </div>
                                <div class="col-md-6">
                                    <label for="roomGameLevel" class="form-label">游戏级别</label>
                                    <select class="form-select" id="roomGameLevel">
                                        <option value="0">随机级别</option>
                                        <option value="1">轻微</option>
                                        <option value="2">一般</option>
                                        <option value="3">严重</option>
                                    </select>
                                </div>
                            </div>

                            <button class="btn btn-primary" onclick="createRoom()">创建房间</button>
                        </div>

                        <!-- 加入房间 -->
                        <div id="joinRoomPanel" style="display: none;">
                            <h5>加入房间</h5>
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label for="joinPlayerName" class="form-label">您的姓名</label>
                                    <input type="text" class="form-control" id="joinPlayerName" placeholder="请输入您的姓名">
                                </div>
                                <div class="col-md-6">
                                    <label for="joinRoomId" class="form-label">房间ID</label>
                                    <input type="text" class="form-control" id="joinRoomId" placeholder="请输入房间ID">
                                </div>
                            </div>
                            <button class="btn btn-success" onclick="joinRoom()">加入房间</button>
                        </div>

                        <!-- 房间状态 -->
                        <div id="roomStatus" style="display: none;">
                            <div class="alert alert-info">
                                <h5 id="roomTitle">房间信息</h5>
                                <p id="roomInfo"></p>
                                <div id="roomPlayers"></div>
                                <div id="roomActions" class="mt-3"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 预设列表模态框 -->
    <div class="modal fade" id="presetsModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">📋 选择预设玩法</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="row" id="allPresetsList">
                        <!-- 预设列表将通过JavaScript动态加载 -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 保存预设模态框 -->
    <div class="modal fade" id="savePresetModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">💾 保存当前设置为预设</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="presetName" class="form-label">预设名称</label>
                        <input type="text" class="form-control" id="presetName" placeholder="例如：我们的专属玩法">
                    </div>
                    <div class="mb-3">
                        <label for="presetDescription" class="form-label">预设描述</label>
                        <textarea class="form-control" id="presetDescription" rows="3" placeholder="描述这个预设的特点和适用场景"></textarea>
                    </div>
                    <div class="mb-3">
                        <label for="createdBy" class="form-label">创建者（可选）</label>
                        <input type="text" class="form-control" id="createdBy" placeholder="你的昵称">
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="saveCurrentPreset()">保存预设</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 全局变量
        let currentGameType = 'punishment_reward';
        let currentGameSettings = null;
        let currentRoom = null;
        let roomCheckInterval = null;

        function selectGameType(gameType, event) {
            currentGameType = gameType;

            // 更新按钮状态
            document.querySelectorAll('#gameTypeSelection .btn').forEach(btn => btn.classList.remove('active'));
            if (event && event.target) {
                event.target.classList.add('active');
            } else {
                // 如果没有event，根据gameType找到对应的按钮并激活
                const buttons = document.querySelectorAll('#gameTypeSelection .btn');
                buttons.forEach(btn => {
                    if ((gameType === 'punishment_reward' && btn.textContent.includes('奖惩游戏')) ||
                        (gameType === 'slot_machine' && btn.textContent.includes('爱情老虎机')) ||
                        (gameType === 'truth_dare' && btn.textContent.includes('真心话大冒险')) ||
                        (gameType === 'dice_challenge' && btn.textContent.includes('浪漫骰子')) ||
                        (gameType === 'love_quiz' && btn.textContent.includes('爱情问答')) ||
                        (gameType === 'photo_challenge' && btn.textContent.includes('主题摄影'))) {
                        btn.classList.add('active');
                    }
                });
            }

            // 显示对应的游戏设置
            document.getElementById('punishmentRewardSettings').style.display = gameType === 'punishment_reward' ? 'block' : 'none';
            document.getElementById('slotMachineSettings').style.display = gameType === 'slot_machine' ? 'block' : 'none';
            document.getElementById('loveQuizSettings').style.display = gameType === 'love_quiz' ? 'block' : 'none';
            document.getElementById('photoChallengeSettings').style.display = gameType === 'photo_challenge' ? 'block' : 'none';
            document.getElementById('otherGameSettings').style.display = (gameType !== 'punishment_reward' && gameType !== 'slot_machine' && gameType !== 'love_quiz' && gameType !== 'photo_challenge') ? 'block' : 'none';

            // 清除之前的游戏结果和进度显示
            document.getElementById('gameResult').style.display = 'none';
            document.getElementById('gameProgress').style.display = 'none';

            // 重置游戏界面到初始状态
            document.getElementById('gameSetup').style.display = 'block';

            console.log('Selected game type:', gameType); // 添加调试日志
        }

        // 随机玩家名字列表
        const randomNames = [
            '小甜心', '小宝贝', '亲爱的', '小可爱', '小天使', '小公主', '小王子',
            '蜜糖', '糖果', '巧克力', '奶茶', '咖啡', '蛋糕', '布丁',
            '星星', '月亮', '太阳', '彩虹', '花朵', '蝴蝶', '小鸟',
            '阿萌', '阿呆', '阿乖', '阿甜', '阿美', '阿帅', '阿酷'
        ];

        function getRandomName() {
            return randomNames[Math.floor(Math.random() * randomNames.length)];
        }

        function startGame() {
            let player1Name = document.getElementById('player1Name').value.trim();
            let player2Name = document.getElementById('player2Name').value.trim();

            // 如果没有输入玩家名字，使用随机名字
            if (!player1Name) {
                player1Name = getRandomName();
                document.getElementById('player1Name').value = player1Name;
            }
            if (!player2Name) {
                do {
                    player2Name = getRandomName();
                } while (player2Name === player1Name); // 确保两个名字不同
                document.getElementById('player2Name').value = player2Name;
            }

            if (player1Name === player2Name) {
                alert('两个玩家姓名不能相同！');
                return;
            }

            // 根据游戏类型构建请求
            let gameSettings = {
                player1Name: player1Name,
                player2Name: player2Name,
                gameMode: currentGameType
            };

            // 只有奖惩游戏需要类型和级别设置
            if (currentGameType === 'punishment_reward') {
                const gameType = parseInt(document.getElementById('gameType').value);
                const gameLevel = parseInt(document.getElementById('gameLevel').value);
                gameSettings.type = gameType;
                gameSettings.level = gameLevel;
            }

            console.log('Game settings:', gameSettings); // 添加调试日志

            // 所有游戏类型都已支持
            // 无需检查游戏类型

            // 保存游戏设置供连续游戏使用
            currentGameSettings = gameSettings;

            // 开始游戏
            startGameWithSettings(currentGameSettings);
        }
        
        function showResult(result) {
            document.getElementById('gameProgress').style.display = 'none';
            document.getElementById('gameResult').style.display = 'block';

            // 显示玩家信息
            updatePlayerInfo(result);

            if (result.gameMode === 'slot_machine') {
                showSlotMachineResult(result);
            } else if (result.gameMode === 'truth_dare') {
                showTruthDareResult(result);
            } else if (result.gameMode === 'dice_challenge') {
                showDiceChallengeResult(result);
            } else if (result.gameMode === 'love_quiz') {
                showLoveQuizResult(result);
            } else if (result.gameMode === 'photo_challenge') {
                showPhotoChallengeResult(result);
            } else {
                showPunishmentRewardResult(result);
            }
        }

        function updatePlayerInfo(result) {
            // 更新玩家名字显示
            document.getElementById('displayPlayer1Name').textContent = result.player1Name || '玩家1';
            document.getElementById('displayPlayer2Name').textContent = result.player2Name || '玩家2';

            // 重置玩家卡片状态
            document.getElementById('player1Card').classList.remove('active', 'winner');
            document.getElementById('player2Card').classList.remove('active', 'winner');
            document.getElementById('player1Status').textContent = '';
            document.getElementById('player2Status').textContent = '';

            // 根据游戏类型设置玩家状态
            if (result.gameMode === 'punishment_reward' && result.selectedPlayer) {
                // 奖惩游戏：显示被选中的玩家
                if (result.selectedPlayer === 1) {
                    document.getElementById('player1Card').classList.add('active');
                    document.getElementById('player1Status').textContent = '🎯 被选中';
                    document.getElementById('player2Status').textContent = '👀 观看';
                } else {
                    document.getElementById('player2Card').classList.add('active');
                    document.getElementById('player2Status').textContent = '🎯 被选中';
                    document.getElementById('player1Status').textContent = '👀 观看';
                }
            } else if (result.gameMode === 'slot_machine') {
                // 老虎机：两人共同参与
                document.getElementById('player1Status').textContent = '🎰 共同转动';
                document.getElementById('player2Status').textContent = '🎰 共同转动';
                if (result.slotResult && result.slotResult.isWin) {
                    document.getElementById('player1Card').classList.add('winner');
                    document.getElementById('player2Card').classList.add('winner');
                }
            } else if (result.gameMode === 'truth_dare') {
                // 真心话大冒险：轮流参与
                const currentPlayer = Math.floor(Math.random() * 2) + 1;
                if (currentPlayer === 1) {
                    document.getElementById('player1Card').classList.add('active');
                    document.getElementById('player1Status').textContent = '🎭 轮到你了';
                    document.getElementById('player2Status').textContent = '⏳ 等待中';
                } else {
                    document.getElementById('player2Card').classList.add('active');
                    document.getElementById('player2Status').textContent = '🎭 轮到你了';
                    document.getElementById('player1Status').textContent = '⏳ 等待中';
                }
            } else if (result.gameMode === 'dice_challenge') {
                // 骰子挑战：两人共同完成
                document.getElementById('player1Status').textContent = '🎲 共同挑战';
                document.getElementById('player2Status').textContent = '🎲 共同挑战';
                document.getElementById('player1Card').classList.add('active');
                document.getElementById('player2Card').classList.add('active');
            }
        }

        function showPunishmentRewardResult(result) {
            const selectedPlayerName = result.selectedPlayer === 1 ? result.player1Name : result.player2Name;
            const otherPlayerName = result.selectedPlayer === 1 ? result.player2Name : result.player1Name;
            const item = result.selectedItem;
            const typeText = item.type === 1 ? '🎁 恭喜！获得奖励' : '😅 很遗憾！接受惩罚';
            const levelText = ['', '轻微', '一般', '严重'][item.level];

            document.getElementById('resultTitle').textContent = typeText;
            document.getElementById('selectedPlayer').innerHTML = `
                <div class="row">
                    <div class="col-12">
                        <strong>🎯 ${selectedPlayerName}</strong> 被选中了！级别：${levelText}
                    </div>
                    <div class="col-12 mt-2">
                        <small class="text-muted">${otherPlayerName} 负责监督完成哦~ 😊</small>
                    </div>
                </div>
            `;
            document.getElementById('itemTitle').textContent = item.title;
            document.getElementById('itemContent').textContent = item.content;

            // 显示媒体内容
            showMediaContent(item);
        }

        function showLoveQuizResult(result) {
            const quizResult = result.loveQuizResult;
            const question = quizResult.question;

            document.getElementById('resultTitle').textContent = '💕 爱情问答挑战';

            // 显示当前问题和游戏状态
            const currentPlayerName = quizResult.currentTurn === 1 ? result.player1Name : result.player2Name;
            const otherPlayerName = quizResult.currentTurn === 1 ? result.player2Name : result.player1Name;

            if (quizResult.isGameOver) {
                // 游戏结束，显示最终结果
                const winner = quizResult.player1Score > quizResult.player2Score ? result.player1Name :
                              quizResult.player2Score > quizResult.player1Score ? result.player2Name : '平局';

                document.getElementById('selectedPlayer').innerHTML = `
                    <div class="alert alert-success">
                        <h5>🎉 游戏结束！</h5>
                        <div class="row text-center">
                            <div class="col-6">
                                <h6>${result.player1Name}</h6>
                                <span class="badge bg-primary fs-5">${quizResult.player1Score}分</span>
                            </div>
                            <div class="col-6">
                                <h6>${result.player2Name}</h6>
                                <span class="badge bg-primary fs-5">${quizResult.player2Score}分</span>
                            </div>
                        </div>
                        <div class="mt-3">
                            ${winner === '平局' ?
                                '<h6>🤝 平局！你们对彼此的了解程度相当！</h6>' :
                                `<h6>🏆 ${winner} 获胜！更了解对方哦~</h6>`
                            }
                        </div>
                    </div>
                `;

                document.getElementById('itemTitle').textContent = '游戏总结';
                document.getElementById('itemContent').textContent = '通过这次问答游戏，希望你们更加了解彼此，感情更加深厚！';
            } else {
                // 游戏进行中，显示当前问题
                const typeText = ['', '了解对方', '共同回忆', '未来计划'][question.questionType];
                const difficultyText = ['', '简单', '中等', '困难'][question.difficulty];

                document.getElementById('selectedPlayer').innerHTML = `
                    <div class="alert alert-info">
                        <div class="row">
                            <div class="col-12 text-center mb-3">
                                <h6>第 ${quizResult.currentRound} / ${quizResult.totalRounds} 轮</h6>
                                <div class="progress mb-2">
                                    <div class="progress-bar" style="width: ${(quizResult.currentRound / quizResult.totalRounds) * 100}%"></div>
                                </div>
                            </div>
                            <div class="col-6 text-center">
                                <h6>${result.player1Name}</h6>
                                <span class="badge bg-primary">${quizResult.player1Score}分</span>
                            </div>
                            <div class="col-6 text-center">
                                <h6>${result.player2Name}</h6>
                                <span class="badge bg-primary">${quizResult.player2Score}分</span>
                            </div>
                            <div class="col-12 text-center mt-3">
                                <h6>💭 轮到 <strong>${currentPlayerName}</strong> 回答问题</h6>
                                <small class="text-muted">${otherPlayerName} 可以一起讨论哦~</small>
                            </div>
                        </div>
                    </div>
                `;

                document.getElementById('itemTitle').innerHTML = `
                    <span class="badge bg-info me-2">${typeText}</span>
                    <span class="badge bg-secondary me-2">${difficultyText}</span>
                    <span class="badge bg-success">${question.points}分</span>
                `;
                document.getElementById('itemContent').textContent = question.question;

                // 如果是选择题，显示选项
                if (question.answerType === 1 && question.options) {
                    try {
                        const options = JSON.parse(question.options);
                        const optionsHtml = options.map((option, index) =>
                            `<button class="btn btn-outline-primary me-2 mb-2" onclick="selectAnswer('${option}')">${String.fromCharCode(65 + index)}. ${option}</button>`
                        ).join('');

                        document.getElementById('itemContent').innerHTML = `
                            <p>${question.question}</p>
                            <div class="mt-3">
                                <h6>请选择答案：</h6>
                                ${optionsHtml}
                            </div>
                        `;
                    } catch (e) {
                        console.error('解析选项失败:', e);
                    }
                }
            }

            // 显示媒体内容（如果有）
            if (question && question.mediaUrl) {
                showMediaContent(question);
            } else {
                document.getElementById('mediaContainer').innerHTML = '';
            }
        }

        function selectAnswer(answer) {
            // 这里可以添加答案选择的逻辑
            alert(`你选择了：${answer}`);
            // 实际应用中，这里应该发送答案到服务器并获取下一题
        }

        function showPhotoChallengeResult(result) {
            const challengeResult = result.photoChallengeResult;
            const challenge = challengeResult.challenge;
            const theme = challengeResult.theme;

            document.getElementById('resultTitle').textContent = '📸 主题摄影挑战';

            // 显示主题信息
            document.getElementById('itemTitle').innerHTML = `
                <span class="badge bg-primary me-2">${theme.themeName}</span>
                <span class="badge bg-info me-2">${theme.category}</span>
                <span class="badge bg-secondary">难度${theme.difficulty}</span>
            `;

            document.getElementById('itemContent').innerHTML = `
                <div class="alert alert-info">
                    <h6>📸 拍摄主题：${theme.themeName}</h6>
                    <p class="mb-2">${theme.description}</p>
                    ${theme.tips ? `<small class="text-muted">💡 小贴士：${theme.tips}</small>` : ''}
                </div>
            `;

            // 显示玩家状态和照片
            let playerStatusHtml = '';

            if (challengeResult.isCompleted) {
                // 游戏结束，显示结果
                playerStatusHtml = `
                    <div class="alert alert-success">
                        <h5>🎉 摄影挑战完成！</h5>
                        <div class="row">
                            <div class="col-6 text-center">
                                <h6>${challenge.player1Name}</h6>
                                <span class="badge bg-primary fs-6">${challenge.player1Score}分</span>
                                ${challenge.player1Photo ? `<div class="mt-2"><img src="${challenge.player1Photo}" class="img-thumbnail" style="max-width: 150px;"></div>` : ''}
                            </div>
                            <div class="col-6 text-center">
                                <h6>${challenge.player2Name}</h6>
                                <span class="badge bg-primary fs-6">${challenge.player2Score}分</span>
                                ${challenge.player2Photo ? `<div class="mt-2"><img src="${challenge.player2Photo}" class="img-thumbnail" style="max-width: 150px;"></div>` : ''}
                            </div>
                        </div>
                        <div class="mt-3 text-center">
                            ${challenge.winner === '平局' ?
                                '<h6>🤝 平局！你们都拍得很棒！</h6>' :
                                `<h6>🏆 ${challenge.winner} 获胜！</h6>`
                            }
                        </div>
                    </div>
                `;
            } else if (challengeResult.canVote) {
                // 可以投票
                playerStatusHtml = `
                    <div class="alert alert-warning">
                        <h6>📸 照片已提交完成，请投票选出最佳照片！</h6>
                        <div class="row mt-3">
                            <div class="col-6 text-center">
                                <h6>${challenge.player1Name}</h6>
                                <img src="${challenge.player1Photo}" class="img-thumbnail mb-2" style="max-width: 200px;">
                                <br>
                                <button class="btn btn-success btn-sm" onclick="voteForPhoto('${challenge.player1Name}')">
                                    👍 投票给${challenge.player1Name}
                                </button>
                            </div>
                            <div class="col-6 text-center">
                                <h6>${challenge.player2Name}</h6>
                                <img src="${challenge.player2Photo}" class="img-thumbnail mb-2" style="max-width: 200px;">
                                <br>
                                <button class="btn btn-success btn-sm" onclick="voteForPhoto('${challenge.player2Name}')">
                                    👍 投票给${challenge.player2Name}
                                </button>
                            </div>
                        </div>
                        <div class="text-center mt-3">
                            <button class="btn btn-secondary" onclick="voteForPhoto('平局')">
                                🤝 平局（都很棒）
                            </button>
                        </div>
                    </div>
                `;
            } else {
                // 等待提交照片
                const player1Submitted = challenge.player1Photo ? '✅' : '⏳';
                const player2Submitted = challenge.player2Photo ? '✅' : '⏳';

                playerStatusHtml = `
                    <div class="alert alert-info">
                        <h6>📸 等待照片提交</h6>
                        <div class="row text-center">
                            <div class="col-6">
                                <h6>${player1Submitted} ${challenge.player1Name}</h6>
                                ${challenge.player1Photo ?
                                    `<img src="${challenge.player1Photo}" class="img-thumbnail" style="max-width: 150px;">` :
                                    `<button class="btn btn-primary" onclick="takePhotoForChallenge('${challenge.player1Name}')">📸 拍照</button>`
                                }
                            </div>
                            <div class="col-6">
                                <h6>${player2Submitted} ${challenge.player2Name}</h6>
                                ${challenge.player2Photo ?
                                    `<img src="${challenge.player2Photo}" class="img-thumbnail" style="max-width: 150px;">` :
                                    `<button class="btn btn-primary" onclick="takePhotoForChallenge('${challenge.player2Name}')">📸 拍照</button>`
                                }
                            </div>
                        </div>
                    </div>
                `;
            }

            document.getElementById('selectedPlayer').innerHTML = playerStatusHtml;

            // 清空媒体容器
            document.getElementById('mediaContainer').innerHTML = '';
        }

        // 显示摄像头模态框
        function showCameraModal(title, onPhotoTaken) {
            // 创建模态框HTML
            const modalHtml = `
                <div class="modal fade" id="cameraModal" tabindex="-1" aria-hidden="true">
                    <div class="modal-dialog modal-lg">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title">${title}</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body text-center">
                                <div id="cameraContainer">
                                    <video id="cameraVideo" width="400" height="300" autoplay style="border: 2px solid #ddd; border-radius: 8px;"></video>
                                    <canvas id="cameraCanvas" width="400" height="300" style="display: none;"></canvas>
                                </div>
                                <div class="mt-3">
                                    <button class="btn btn-primary" id="captureBtn">📸 拍照</button>
                                    <button class="btn btn-secondary" id="retakeBtn" style="display: none;">🔄 重拍</button>
                                </div>
                                <div id="photoPreview" class="mt-3" style="display: none;">
                                    <img id="previewImage" style="max-width: 100%; border-radius: 8px;">
                                </div>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                                <button type="button" class="btn btn-primary" id="confirmPhotoBtn" style="display: none;">确认使用</button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // 移除已存在的模态框
            const existingModal = document.getElementById('cameraModal');
            if (existingModal) {
                existingModal.remove();
            }

            // 添加新模态框
            document.body.insertAdjacentHTML('beforeend', modalHtml);

            // 获取元素
            const modal = new bootstrap.Modal(document.getElementById('cameraModal'));
            const video = document.getElementById('cameraVideo');
            const canvas = document.getElementById('cameraCanvas');
            const captureBtn = document.getElementById('captureBtn');
            const retakeBtn = document.getElementById('retakeBtn');
            const confirmBtn = document.getElementById('confirmPhotoBtn');
            const photoPreview = document.getElementById('photoPreview');
            const previewImage = document.getElementById('previewImage');

            let stream = null;
            let photoData = null;

            // 启动摄像头
            navigator.mediaDevices.getUserMedia({ video: true })
                .then(function(mediaStream) {
                    stream = mediaStream;
                    video.srcObject = stream;
                })
                .catch(function(error) {
                    console.error('摄像头访问失败:', error);
                    alert('无法访问摄像头，请检查权限设置或使用文件上传功能');
                });

            // 拍照按钮
            captureBtn.addEventListener('click', function() {
                const context = canvas.getContext('2d');
                context.drawImage(video, 0, 0, 400, 300);
                photoData = canvas.toDataURL('image/jpeg', 0.8);

                previewImage.src = photoData;
                photoPreview.style.display = 'block';
                video.style.display = 'none';
                captureBtn.style.display = 'none';
                retakeBtn.style.display = 'inline-block';
                confirmBtn.style.display = 'inline-block';
            });

            // 重拍按钮
            retakeBtn.addEventListener('click', function() {
                video.style.display = 'block';
                photoPreview.style.display = 'none';
                captureBtn.style.display = 'inline-block';
                retakeBtn.style.display = 'none';
                confirmBtn.style.display = 'none';
                photoData = null;
            });

            // 确认按钮
            confirmBtn.addEventListener('click', function() {
                if (photoData && onPhotoTaken) {
                    onPhotoTaken(photoData);
                }
                modal.hide();
            });

            // 模态框关闭时停止摄像头
            document.getElementById('cameraModal').addEventListener('hidden.bs.modal', function() {
                if (stream) {
                    stream.getTracks().forEach(track => track.stop());
                }
                document.getElementById('cameraModal').remove();
            });

            // 显示模态框
            modal.show();
        }

        // 上传照片
        function uploadPhoto(photoData, filename) {
            return new Promise((resolve, reject) => {
                // 将base64转换为blob
                const byteCharacters = atob(photoData.split(',')[1]);
                const byteNumbers = new Array(byteCharacters.length);
                for (let i = 0; i < byteCharacters.length; i++) {
                    byteNumbers[i] = byteCharacters.charCodeAt(i);
                }
                const byteArray = new Uint8Array(byteNumbers);
                const blob = new Blob([byteArray], { type: 'image/jpeg' });

                // 创建FormData
                const formData = new FormData();
                formData.append('file', blob, filename);

                // 上传到服务器
                fetch('/admin/upload', {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.code === 0) {
                        resolve(data.data.url);
                    } else {
                        reject(new Error(data.msg));
                    }
                })
                .catch(error => {
                    reject(error);
                });
            });
        }

        // 为摄影挑战拍照
        function takePhotoForChallenge(playerName) {
            showCameraModal(`${playerName} 的摄影作品`, function(photoData) {
                // 上传照片并提交到游戏
                uploadPhoto(photoData, `${playerName}_photo_${Date.now()}.jpg`).then(photoUrl => {
                    if (photoUrl && currentGameSettings && currentGameSettings.gameSession) {
                        // 调用API提交照片到游戏
                        console.log(`${playerName} 提交照片: ${photoUrl}`);

                        fetch('/game/submit-photo', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                            },
                            body: JSON.stringify({
                                gameSession: currentGameSettings.gameSession,
                                playerName: playerName,
                                photoUrl: photoUrl,
                                gameMode: currentGameSettings.gameMode
                            })
                        })
                        .then(response => response.json())
                        .then(data => {
                            console.log('📸 Submit photo response:', data);
                            if (data.code === 0) {
                                alert('照片提交成功！');
                                // 更新游戏状态显示
                                showPhotoChallengeResult(data.data);
                            } else {
                                alert('照片提交失败: ' + data.msg);
                            }
                        })
                        .catch(error => {
                            console.error('💥 Submit photo error:', error);
                            alert('照片提交失败: ' + error.message);
                        });
                    } else {
                        alert('照片上传成功，但无法提交到游戏（缺少游戏会话信息）');
                    }
                }).catch(error => {
                    console.error('照片上传失败:', error);
                    alert('照片上传失败: ' + error.message);
                });
            });
        }

        // 为照片投票
        function voteForPhoto(winnerName) {
            if (confirm(`确定投票给 ${winnerName} 吗？`)) {
                // 这里应该调用API提交投票
                console.log(`投票给: ${winnerName}`);
                alert('投票成功！');
                // 实际应用中应该刷新游戏状态
            }
        }

        function showSlotMachineResult(result) {
            const slotResult = result.slotResult;
            const combination = slotResult.combination;

            document.getElementById('resultTitle').textContent = '🎰 爱情老虎机结果';

            // 显示符号组合，添加动画延迟
            const symbolsHtml = slotResult.symbols.map((symbol, index) =>
                `<div class="symbol" style="--delay: ${index}">${symbol.emoji}</div>`
            ).join('');

            if (slotResult.isWin) {
                document.getElementById('selectedPlayer').innerHTML = `
                    <div class="slot-combination winning">${symbolsHtml}</div>
                    <div class="alert alert-success mt-2">
                        <div>🎊 恭喜 <strong>${result.player1Name}</strong> 和 <strong>${result.player2Name}</strong>！</div>
                        <div class="mt-2"><small>你们一起获得了特殊组合，共同完成这个浪漫挑战吧！💕</small></div>
                    </div>
                `;
            } else {
                document.getElementById('selectedPlayer').innerHTML = `
                    <div class="slot-combination">${symbolsHtml}</div>
                    <div class="alert alert-info mt-2">
                        <div>💫 <strong>${result.player1Name}</strong> 和 <strong>${result.player2Name}</strong></div>
                        <div class="mt-2"><small>这次没有特殊组合，但爱情的轮盘还在转动！再试一次吧~ 🎰</small></div>
                    </div>
                `;
            }

            document.getElementById('itemTitle').textContent = combination.activityTitle;
            document.getElementById('itemContent').textContent = combination.activityContent;

            // 为获胜结果添加特殊样式
            const gameResult = document.getElementById('gameResult');
            if (slotResult.isWin) {
                gameResult.classList.add('winning');
            } else {
                gameResult.classList.remove('winning');
            }

            // 显示媒体内容（如果有）
            if (combination.mediaUrl) {
                showMediaContent(combination);
            } else {
                document.getElementById('mediaContainer').innerHTML = '';
            }
        }

        function showTruthDareResult(result) {
            const truthDareResult = result.truthDareResult;
            const question = truthDareResult.question;

            document.getElementById('resultTitle').textContent = '💭 真心话大冒险';

            // 显示问题类型和内容
            const typeText = question.type === 1 ? '真心话' : '大冒险';
            const typeIcon = question.type === 1 ? '💭' : '🎯';

            // 随机选择一个玩家来回答/执行
            const currentPlayer = Math.floor(Math.random() * 2) + 1;
            const currentPlayerName = currentPlayer === 1 ? result.player1Name : result.player2Name;
            const otherPlayerName = currentPlayer === 1 ? result.player2Name : result.player1Name;

            document.getElementById('selectedPlayer').innerHTML = `
                <div class="truth-dare-container">
                    <div class="truth-dare-type">${typeIcon}</div>
                    <h4>${typeText}</h4>
                    <div class="alert alert-primary mt-2">
                        <div>🎭 轮到 <strong>${currentPlayerName}</strong> 了！</div>
                        <div class="mt-2"><small class="text-muted">${otherPlayerName} 负责提问和监督哦~ 😊</small></div>
                    </div>
                    <div class="truth-dare-question">
                        <h5>${question.title}</h5>
                        <p>${question.content}</p>
                    </div>
                </div>
            `;

            document.getElementById('itemTitle').textContent = `${typeText}：${question.title}`;
            document.getElementById('itemContent').textContent = question.content;

            // 显示媒体内容（如果有）
            if (question.mediaUrl) {
                showMediaContent(question);
            } else {
                document.getElementById('mediaContainer').innerHTML = '';
            }
        }

        function showDiceChallengeResult(result) {
            const diceResult = result.diceResult;
            const challenge = diceResult.challenge;

            document.getElementById('resultTitle').textContent = '🎲 浪漫骰子';

            // 显示骰子结果和挑战
            document.getElementById('selectedPlayer').innerHTML = `
                <div class="dice-container">
                    <h4>🎲 骰子结果</h4>
                    <div class="dice-display">
                        <div class="dice">${diceResult.dice1}</div>
                        <div class="dice">+</div>
                        <div class="dice">${diceResult.dice2}</div>
                        <div class="dice">=</div>
                        <div class="dice">${diceResult.sum}</div>
                    </div>
                    <div class="alert alert-success mt-3">
                        <div>🎲 <strong>${result.player1Name}</strong> 和 <strong>${result.player2Name}</strong></div>
                        <div class="mt-2"><small>一起完成这个浪漫挑战吧！两人协作会更有趣哦~ 💕</small></div>
                    </div>
                    <div class="dice-challenge">
                        <h5>${challenge.name}</h5>
                        <p><strong>组合：</strong>${challenge.diceCombination}</p>
                        <p><strong>描述：</strong>${challenge.description}</p>
                        ${challenge.requiresProps ? `<p><strong>需要道具：</strong>${challenge.propsNeeded}</p>` : ''}
                        <p><strong>预计时间：</strong>${challenge.durationMinutes}分钟</p>
                    </div>
                </div>
            `;

            document.getElementById('itemTitle').textContent = challenge.activityTitle;
            document.getElementById('itemContent').textContent = challenge.activityContent;

            // 显示媒体内容（如果有）
            if (challenge.mediaUrl) {
                showMediaContent(challenge);
            } else {
                document.getElementById('mediaContainer').innerHTML = '';
            }
        }

        function showMediaContent(item) {
            const mediaContainer = document.getElementById('mediaContainer');
            mediaContainer.innerHTML = '';

            if (item.mediaUrl && item.mediaType > 0) {
                let mediaElement = '';
                switch (item.mediaType) {
                    case 1: // 图片
                        mediaElement = `<div class="text-center mt-3">
                                         <img src="${item.mediaUrl}" alt="${item.title || item.activityTitle}" class="img-fluid rounded" style="max-width: 300px; max-height: 300px;">
                                       </div>`;
                        break;
                    case 2: // 音频
                        mediaElement = `<div class="text-center mt-3">
                                         <audio controls class="w-100" style="max-width: 400px;">
                                           <source src="${item.mediaUrl}" type="audio/mpeg">
                                           <source src="${item.mediaUrl}" type="audio/wav">
                                           <source src="${item.mediaUrl}" type="audio/ogg">
                                           您的浏览器不支持音频播放。
                                         </audio>
                                       </div>`;
                        break;
                    case 3: // 视频
                        mediaElement = `<div class="text-center mt-3">
                                         <video controls class="w-100 rounded" style="max-width: 400px; max-height: 300px;">
                                           <source src="${item.mediaUrl}" type="video/mp4">
                                           <source src="${item.mediaUrl}" type="video/webm">
                                           <source src="${item.mediaUrl}" type="video/ogg">
                                           您的浏览器不支持视频播放。
                                         </video>
                                       </div>`;
                        break;
                }
                if (mediaElement) {
                    mediaContainer.innerHTML = mediaElement;
                }
            }
        }
        


        function continueGame() {
            if (currentRoom) {
                // 房间游戏：继续房间游戏
                startRoomGame();
            } else if (currentGameSettings && currentGameSettings.gameSession) {
                // 本地游戏：继续当前游戏会话
                console.log('🎮 Continuing game with session:', currentGameSettings.gameSession);

                // 显示进行中界面和动画效果
                document.getElementById('gameSetup').style.display = 'none';
                document.getElementById('gameProgress').style.display = 'block';
                document.getElementById('gameResult').style.display = 'none';

                // 根据游戏模式显示不同的进行中界面和动画
                if (currentGameSettings.gameMode === 'slot_machine') {
                    document.getElementById('punishmentRewardProgress').style.display = 'none';
                    document.getElementById('slotMachineProgress').style.display = 'block';
                    startSlotMachineAnimation();
                } else if (currentGameSettings.gameMode === 'love_quiz') {
                    // 爱情问答不需要动画，直接显示结果
                    document.getElementById('punishmentRewardProgress').style.display = 'none';
                    document.getElementById('slotMachineProgress').style.display = 'none';
                } else if (currentGameSettings.gameMode === 'photo_challenge') {
                    // 主题摄影不需要动画，直接显示结果
                    document.getElementById('punishmentRewardProgress').style.display = 'none';
                    document.getElementById('slotMachineProgress').style.display = 'none';
                } else {
                    // 其他游戏（奖惩、真心话、骰子）显示命运之轮动画
                    document.getElementById('punishmentRewardProgress').style.display = 'block';
                    document.getElementById('slotMachineProgress').style.display = 'none';
                }

                fetch('/game/continue', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        gameSession: currentGameSettings.gameSession,
                        gameMode: currentGameSettings.gameMode
                    })
                })
                .then(response => response.json())
                .then(data => {
                    console.log('📋 Continue game response:', data);
                    if (data.code === 0) {
                        // 更新游戏会话信息
                        currentGameSettings.gameSession = data.data.gameSession;

                        // 根据游戏模式处理动画和结果显示
                        if (currentGameSettings.gameMode === 'love_quiz' || currentGameSettings.gameMode === 'photo_challenge') {
                            // 爱情问答和主题摄影直接显示结果
                            showResult(data.data);
                        } else {
                            // 其他游戏延迟显示结果，增加悬念
                            setTimeout(() => {
                                // 如果是老虎机游戏，传递最终符号
                                if (data.data.gameMode === 'slot_machine' && data.data.slotResult) {
                                    const finalSymbols = data.data.slotResult.symbols.map(s => s.emoji);
                                    stopSlotMachineAnimation(finalSymbols);
                                } else {
                                    stopSlotMachineAnimation();
                                }

                                // 延迟显示结果，让动画完成
                                setTimeout(() => {
                                    showResult(data.data);
                                }, 2000);
                            }, 3000);
                        }
                    } else {
                        alert('继续游戏失败: ' + data.msg);
                        resetGame();
                    }
                })
                .catch(error => {
                    console.error('💥 Continue game error:', error);
                    alert('继续游戏失败: ' + error.message);
                    resetGame();
                });
            } else if (currentGameSettings) {
                // 如果没有游戏会话，重新开始游戏
                startGameWithSettings(currentGameSettings);
            } else {
                // 如果没有保存的设置，回到设置页面
                resetGame();
            }
        }

        function startGameWithSettings(settings) {
            // 显示进行中界面
            document.getElementById('gameSetup').style.display = 'none';
            document.getElementById('gameProgress').style.display = 'block';
            document.getElementById('gameResult').style.display = 'none';

            // 根据游戏模式显示不同的进行中界面
            if (settings.gameMode === 'slot_machine') {
                document.getElementById('punishmentRewardProgress').style.display = 'none';
                document.getElementById('slotMachineProgress').style.display = 'block';
                startSlotMachineAnimation();
            } else {
                document.getElementById('punishmentRewardProgress').style.display = 'block';
                document.getElementById('slotMachineProgress').style.display = 'none';
            }

            // 添加sessionId到设置中
            const sessionId = localStorage.getItem('gameSessionId') || 'default';
            settings.sessionId = sessionId;

            // 发送请求
            fetch('/game/play', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(settings)
            })
            .then(response => response.json())
            .then(data => {
                if (data.code === 0) {
                    // 保存游戏会话信息
                    if (data.data.gameSession) {
                        currentGameSettings.gameSession = data.data.gameSession;
                        console.log('💾 Game session saved:', data.data.gameSession);
                    }

                    // 延迟显示结果，增加悬念
                    setTimeout(() => {
                        // 如果是老虎机游戏，传递最终符号
                        if (data.data.gameMode === 'slot_machine' && data.data.slotResult) {
                            const finalSymbols = data.data.slotResult.symbols.map(s => s.emoji);
                            stopSlotMachineAnimation(finalSymbols);
                        } else {
                            stopSlotMachineAnimation();
                        }

                        // 延迟显示结果，让动画完成
                        setTimeout(() => {
                            showResult(data.data);
                        }, 2000);
                    }, 3000);
                } else {
                    alert('游戏出错：' + data.msg);
                    resetGame();
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('网络错误，请重试');
                resetGame();
            });
        }

        function startSlotMachineAnimation() {
            const reels = document.querySelectorAll('.slot-reel');
            const symbols = ['❤️', '💋', '🌹', '💍', '👫', '🍽️', '🎬', '💆', '🎁', '💃', '🎵', '⭐', '🌙', '🔥', '🍒'];

            reels.forEach((reel, index) => {
                reel.classList.add('spinning');
                reel.classList.remove('winning');

                // 在转动过程中随机更换符号，增加真实感
                const symbolElement = reel.querySelector('.slot-symbol');
                const changeSymbol = () => {
                    if (reel.classList.contains('spinning')) {
                        const randomSymbol = symbols[Math.floor(Math.random() * symbols.length)];
                        symbolElement.textContent = randomSymbol;
                        setTimeout(changeSymbol, 100 + Math.random() * 100);
                    }
                };

                // 延迟开始符号变换，让每个轮盘有不同的节奏
                setTimeout(changeSymbol, index * 200);
            });
        }

        function stopSlotMachineAnimation(finalSymbols) {
            const reels = document.querySelectorAll('.slot-reel');

            reels.forEach((reel, index) => {
                // 分别停止每个轮盘，增加真实感
                setTimeout(() => {
                    reel.classList.remove('spinning');

                    // 设置最终符号
                    if (finalSymbols && finalSymbols[index]) {
                        const symbolElement = reel.querySelector('.slot-symbol');
                        symbolElement.textContent = finalSymbols[index];
                    }

                    // 添加停止后的弹跳效果
                    setTimeout(() => {
                        reel.classList.add('winning');
                        setTimeout(() => {
                            reel.classList.remove('winning');
                        }, 1000);
                    }, 300);

                }, index * 500); // 每个轮盘延迟500ms停止
            });
        }

        function resetGame() {
            document.getElementById('gameSetup').style.display = 'block';
            document.getElementById('gameProgress').style.display = 'none';
            document.getElementById('gameResult').style.display = 'none';

            // 清空输入框
            const player1Input = document.getElementById('player1Name');
            const player2Input = document.getElementById('player2Name');
            const gameTypeSelect = document.getElementById('gameType');
            const gameLevelSelect = document.getElementById('gameLevel');

            if (player1Input) player1Input.value = '';
            if (player2Input) player2Input.value = '';
            if (gameTypeSelect) gameTypeSelect.value = '0';
            if (gameLevelSelect) gameLevelSelect.value = '0';

            // 清空保存的设置
            currentGameSettings = null;
            currentRoom = null;

            // 清理房间检查定时器
            if (roomCheckInterval) {
                clearInterval(roomCheckInterval);
                roomCheckInterval = null;
            }
        }

        // 显示本地游戏
        function showLocalGame(event) {
            document.getElementById('gameSetup').style.display = 'block';
            document.getElementById('roomGameSetup').style.display = 'none';

            // 更新按钮状态
            document.querySelectorAll('#modeSelection .btn').forEach(btn => btn.classList.remove('active'));
            if (event && event.target) {
                event.target.classList.add('active');
            } else {
                // 如果没有event，默认激活第一个按钮（本地游戏）
                const localBtn = document.querySelector('#modeSelection .btn:first-child');
                if (localBtn) localBtn.classList.add('active');
            }
        }

        // 显示房间游戏
        function showRoomGame(event) {
            document.getElementById('gameSetup').style.display = 'none';
            document.getElementById('roomGameSetup').style.display = 'block';

            // 更新按钮状态
            document.querySelectorAll('#modeSelection .btn').forEach(btn => btn.classList.remove('active'));
            if (event && event.target) {
                event.target.classList.add('active');
            } else {
                // 如果没有event，默认激活第二个按钮（房间游戏）
                const roomBtn = document.querySelector('#modeSelection .btn:last-child');
                if (roomBtn) roomBtn.classList.add('active');
            }
        }

        // 显示创建房间面板
        function showCreateRoom() {
            document.getElementById('createRoomPanel').style.display = 'block';
            document.getElementById('joinRoomPanel').style.display = 'none';
            document.getElementById('roomStatus').style.display = 'none';
        }

        // 显示加入房间面板
        function showJoinRoom() {
            document.getElementById('createRoomPanel').style.display = 'none';
            document.getElementById('joinRoomPanel').style.display = 'block';
            document.getElementById('roomStatus').style.display = 'none';
        }

        // 创建房间
        function createRoom() {
            const roomName = document.getElementById('roomName').value.trim();
            const creatorName = document.getElementById('roomCreatorName').value.trim();
            const gameMode = document.getElementById('roomGameMode').value;
            const gameType = parseInt(document.getElementById('roomGameType').value);
            const gameLevel = parseInt(document.getElementById('roomGameLevel').value);

            if (!creatorName) {
                alert('请输入您的姓名！');
                return;
            }

            // 构建请求数据
            let requestData = {
                name: roomName || '', // 房间名字，可选
                createdBy: creatorName,
                gameMode: gameMode
            };

            // 只有奖惩游戏需要类型和级别
            if (gameMode === 'punishment_reward') {
                requestData.type = gameType;
                requestData.level = gameLevel;
            }

            fetch('/room/create', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(requestData)
            })
            .then(response => response.json())
            .then(data => {
                if (data.code === 0) {
                    currentRoom = data.data;
                    showRoomStatus();
                    startRoomCheck();
                } else {
                    alert('创建房间失败：' + data.msg);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('网络错误，请重试');
            });
        }

        // 加入房间
        function joinRoom() {
            const playerName = document.getElementById('joinPlayerName').value.trim();
            const roomId = document.getElementById('joinRoomId').value.trim();

            if (!playerName || !roomId) {
                alert('请输入姓名和房间ID！');
                return;
            }

            fetch('/room/join', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    roomId: roomId,
                    playerName: playerName
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.code === 0) {
                    currentRoom = data.data;
                    showRoomStatus();
                    startRoomCheck();
                } else {
                    alert('加入房间失败：' + data.msg);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('网络错误，请重试');
            });
        }

        // 显示房间状态
        function showRoomStatus() {
            if (!currentRoom) return;

            document.getElementById('createRoomPanel').style.display = 'none';
            document.getElementById('joinRoomPanel').style.display = 'none';
            document.getElementById('roomStatus').style.display = 'block';

            const roomTitle = currentRoom.name ? `${currentRoom.name} (${currentRoom.id})` : `房间 ${currentRoom.id}`;
            document.getElementById('roomTitle').textContent = roomTitle;
            document.getElementById('roomInfo').innerHTML = `
                创建者: ${currentRoom.createdBy}<br>
                游戏类型: ${['随机', '奖励', '惩罚'][currentRoom.settings.type]}<br>
                游戏级别: ${['随机', '轻微', '一般', '严重'][currentRoom.settings.level]}
            `;

            const playersHtml = currentRoom.players.map(player =>
                `<span class="badge bg-primary me-2">${player}</span>`
            ).join('');
            document.getElementById('roomPlayers').innerHTML = `
                <strong>玩家 (${currentRoom.players.length}/2):</strong><br>
                ${playersHtml}
            `;

            const actionsDiv = document.getElementById('roomActions');
            if (currentRoom.status === 'waiting') {
                if (currentRoom.players.length === 2) {
                    actionsDiv.innerHTML = `
                        <button class="btn btn-success" onclick="startRoomGame()">开始游戏</button>
                        <button class="btn btn-outline-secondary" onclick="copyRoomId()">复制房间ID</button>
                    `;
                } else {
                    actionsDiv.innerHTML = `
                        <p class="text-muted">等待另一位玩家加入...</p>
                        <button class="btn btn-outline-secondary" onclick="copyRoomId()">复制房间ID</button>
                    `;
                }
            } else if (currentRoom.status === 'playing' && currentRoom.currentGame) {
                showResult(currentRoom.currentGame);
            }
        }

        // 复制房间ID
        function copyRoomId() {
            if (currentRoom) {
                navigator.clipboard.writeText(currentRoom.id).then(() => {
                    alert('房间ID已复制到剪贴板！');
                }).catch(() => {
                    prompt('房间ID（请手动复制）:', currentRoom.id);
                });
            }
        }

        // 开始房间游戏
        function startRoomGame() {
            if (!currentRoom) return;

            fetch(`/room/${currentRoom.id}/start`, {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                if (data.code === 0) {
                    // 显示进行中界面
                    document.getElementById('roomGameSetup').style.display = 'none';
                    document.getElementById('gameProgress').style.display = 'block';
                    document.getElementById('gameResult').style.display = 'none';

                    // 延迟显示结果
                    setTimeout(() => {
                        showResult(data.data);
                    }, 3000);
                } else {
                    alert('开始游戏失败：' + data.msg);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('网络错误，请重试');
            });
        }

        // 开始房间状态检查
        function startRoomCheck() {
            if (roomCheckInterval) {
                clearInterval(roomCheckInterval);
            }

            roomCheckInterval = setInterval(() => {
                if (currentRoom) {
                    fetch(`/room/${currentRoom.id}`)
                        .then(response => response.json())
                        .then(data => {
                            if (data.code === 0) {
                                currentRoom = data.data;
                                showRoomStatus();
                            }
                        })
                        .catch(error => {
                            console.error('Room check error:', error);
                        });
                }
            }, 2000); // 每2秒检查一次
        }

        // 房间游戏模式改变时的处理
        function handleRoomGameModeChange() {
            const gameMode = document.getElementById('roomGameMode').value;
            const punishmentSettings = document.getElementById('roomPunishmentSettings');

            if (gameMode === 'punishment_reward') {
                punishmentSettings.style.display = 'block';
            } else {
                punishmentSettings.style.display = 'none';
            }
        }

        // 页面加载时默认显示本地游戏和奖惩游戏模式
        document.addEventListener('DOMContentLoaded', function() {
            selectGameType('punishment_reward');
            showLocalGame();

            // 绑定房间游戏模式改变事件
            const roomGameModeSelect = document.getElementById('roomGameMode');
            if (roomGameModeSelect) {
                roomGameModeSelect.addEventListener('change', handleRoomGameModeChange);
                handleRoomGameModeChange(); // 初始化显示
            }

            // 摄像头和拍照相关功能
            let currentStream = null;
            let photoCanvas = null;

            async function initCamera() {
                try {
                    const constraints = {
                        video: {
                            width: { ideal: 640 },
                            height: { ideal: 480 },
                            facingMode: 'user' // 前置摄像头
                        }
                    };

                    currentStream = await navigator.mediaDevices.getUserMedia(constraints);
                    return currentStream;
                } catch (error) {
                    console.error('摄像头初始化失败:', error);
                    alert('无法访问摄像头，请检查权限设置');
                    return null;
                }
            }

            function stopCamera() {
                if (currentStream) {
                    currentStream.getTracks().forEach(track => track.stop());
                    currentStream = null;
                }
            }

            async function takePhoto(videoElement) {
                if (!photoCanvas) {
                    photoCanvas = document.createElement('canvas');
                }

                const video = videoElement;
                photoCanvas.width = video.videoWidth;
                photoCanvas.height = video.videoHeight;

                const ctx = photoCanvas.getContext('2d');
                ctx.drawImage(video, 0, 0);

                return photoCanvas.toDataURL('image/jpeg', 0.8);
            }

            function showCameraModal(title, onPhotoTaken) {
                const modalHtml = `
                    <div class="modal fade" id="cameraModal" tabindex="-1">
                        <div class="modal-dialog modal-lg">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h5 class="modal-title">${title}</h5>
                                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                </div>
                                <div class="modal-body text-center">
                                    <video id="cameraVideo" autoplay muted style="max-width: 100%; border-radius: 10px;"></video>
                                    <div class="mt-3">
                                        <button class="btn btn-primary btn-lg" onclick="capturePhoto()">
                                            📸 拍照
                                        </button>
                                        <button class="btn btn-secondary ms-2" onclick="switchCamera()">
                                            🔄 切换摄像头
                                        </button>
                                    </div>
                                    <div id="photoPreview" class="mt-3" style="display: none;">
                                        <img id="capturedPhoto" style="max-width: 100%; border-radius: 10px;">
                                        <div class="mt-2">
                                            <button class="btn btn-success" onclick="confirmPhoto()">✅ 确认使用</button>
                                            <button class="btn btn-warning" onclick="retakePhoto()">🔄 重新拍摄</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                `;

                // 移除已存在的模态框
                const existingModal = document.getElementById('cameraModal');
                if (existingModal) {
                    existingModal.remove();
                }

                // 添加新模态框
                document.body.insertAdjacentHTML('beforeend', modalHtml);

                // 显示模态框并初始化摄像头
                const modal = new bootstrap.Modal(document.getElementById('cameraModal'));
                modal.show();

                // 存储回调函数
                window.currentPhotoCallback = onPhotoTaken;

                // 初始化摄像头
                initCamera().then(stream => {
                    if (stream) {
                        const video = document.getElementById('cameraVideo');
                        video.srcObject = stream;
                    }
                });

                // 模态框关闭时停止摄像头
                document.getElementById('cameraModal').addEventListener('hidden.bs.modal', function() {
                    stopCamera();
                    this.remove();
                });
            }

            async function capturePhoto() {
                const video = document.getElementById('cameraVideo');
                const photoData = await takePhoto(video);

                // 显示预览
                document.getElementById('capturedPhoto').src = photoData;
                document.getElementById('photoPreview').style.display = 'block';

                // 存储照片数据
                window.currentPhotoData = photoData;
            }

            function confirmPhoto() {
                if (window.currentPhotoCallback && window.currentPhotoData) {
                    window.currentPhotoCallback(window.currentPhotoData);
                }
                bootstrap.Modal.getInstance(document.getElementById('cameraModal')).hide();
            }

            function retakePhoto() {
                document.getElementById('photoPreview').style.display = 'none';
                window.currentPhotoData = null;
            }

            let currentFacingMode = 'user';
            async function switchCamera() {
                stopCamera();
                currentFacingMode = currentFacingMode === 'user' ? 'environment' : 'user';

                const constraints = {
                    video: {
                        width: { ideal: 640 },
                        height: { ideal: 480 },
                        facingMode: currentFacingMode
                    }
                };

                try {
                    currentStream = await navigator.mediaDevices.getUserMedia(constraints);
                    const video = document.getElementById('cameraVideo');
                    video.srcObject = currentStream;
                } catch (error) {
                    console.error('切换摄像头失败:', error);
                    // 如果切换失败，回到原来的摄像头
                    currentFacingMode = currentFacingMode === 'user' ? 'environment' : 'user';
                    initCamera().then(stream => {
                        if (stream) {
                            const video = document.getElementById('cameraVideo');
                            video.srcObject = stream;
                        }
                    });
                }
            }

            // 上传照片到服务器
            async function uploadPhoto(photoData, filename = 'photo.jpg') {
                try {
                    // 将base64转换为blob
                    const response = await fetch(photoData);
                    const blob = await response.blob();

                    const formData = new FormData();
                    formData.append('file', blob, filename);

                    const uploadResponse = await fetch('/admin/upload', {
                        method: 'POST',
                        body: formData
                    });

                    const result = await uploadResponse.json();
                    if (result.code === 0) {
                        return result.data.url;
                    } else {
                        throw new Error(result.msg);
                    }
                } catch (error) {
                    console.error('照片上传失败:', error);
                    alert('照片上传失败: ' + error.message);
                    return null;
                }
            }

            // 录音相关功能
            let mediaRecorder = null;
            let recordedChunks = [];
            let isRecording = false;

            async function initMicrophone() {
                try {
                    const stream = await navigator.mediaDevices.getUserMedia({
                        audio: {
                            echoCancellation: true,
                            noiseSuppression: true,
                            sampleRate: 44100
                        }
                    });
                    return stream;
                } catch (error) {
                    console.error('麦克风初始化失败:', error);
                    alert('无法访问麦克风，请检查权限设置');
                    return null;
                }
            }

            async function startRecording() {
                if (isRecording) return;

                const stream = await initMicrophone();
                if (!stream) return;

                recordedChunks = [];
                mediaRecorder = new MediaRecorder(stream, {
                    mimeType: 'audio/webm;codecs=opus'
                });

                mediaRecorder.ondataavailable = function(event) {
                    if (event.data.size > 0) {
                        recordedChunks.push(event.data);
                    }
                };

                mediaRecorder.onstop = function() {
                    const blob = new Blob(recordedChunks, { type: 'audio/webm' });
                    const audioUrl = URL.createObjectURL(blob);

                    // 停止所有音轨
                    stream.getTracks().forEach(track => track.stop());

                    // 触发录音完成回调
                    if (window.recordingCallback) {
                        window.recordingCallback(blob, audioUrl);
                    }
                };

                mediaRecorder.start();
                isRecording = true;

                return mediaRecorder;
            }

            function stopRecording() {
                if (mediaRecorder && isRecording) {
                    mediaRecorder.stop();
                    isRecording = false;
                }
            }

            function showRecordingModal(title, maxDuration = 30, onRecordingComplete) {
                const modalHtml = `
                    <div class="modal fade" id="recordingModal" tabindex="-1">
                        <div class="modal-dialog">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h5 class="modal-title">${title}</h5>
                                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                </div>
                                <div class="modal-body text-center">
                                    <div id="recordingStatus" class="mb-3">
                                        <p>准备录音...</p>
                                    </div>
                                    <div id="recordingControls">
                                        <button id="startRecordBtn" class="btn btn-danger btn-lg" onclick="startMusicRecording()">
                                            🎤 开始录音
                                        </button>
                                        <button id="stopRecordBtn" class="btn btn-success btn-lg" onclick="stopMusicRecording()" style="display: none;">
                                            ⏹️ 停止录音
                                        </button>
                                    </div>
                                    <div id="recordingTimer" class="mt-3" style="display: none;">
                                        <h4 id="timerDisplay">00:00</h4>
                                        <div class="progress">
                                            <div id="recordingProgress" class="progress-bar bg-danger" style="width: 0%"></div>
                                        </div>
                                    </div>
                                    <div id="playbackControls" class="mt-3" style="display: none;">
                                        <audio id="recordedAudio" controls style="width: 100%;"></audio>
                                        <div class="mt-2">
                                            <button class="btn btn-primary" onclick="confirmRecording()">✅ 确认使用</button>
                                            <button class="btn btn-warning" onclick="retakeRecording()">🔄 重新录音</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                `;

                // 移除已存在的模态框
                const existingModal = document.getElementById('recordingModal');
                if (existingModal) {
                    existingModal.remove();
                }

                // 添加新模态框
                document.body.insertAdjacentHTML('beforeend', modalHtml);

                // 显示模态框
                const modal = new bootstrap.Modal(document.getElementById('recordingModal'));
                modal.show();

                // 存储参数
                window.maxRecordingDuration = maxDuration;
                window.recordingCallback = onRecordingComplete;
                window.recordingTimer = null;
                window.recordingStartTime = null;

                // 模态框关闭时清理
                document.getElementById('recordingModal').addEventListener('hidden.bs.modal', function() {
                    stopRecording();
                    if (window.recordingTimer) {
                        clearInterval(window.recordingTimer);
                    }
                    this.remove();
                });
            }

            async function startMusicRecording() {
                const recorder = await startRecording();
                if (!recorder) return;

                // 更新UI
                document.getElementById('startRecordBtn').style.display = 'none';
                document.getElementById('stopRecordBtn').style.display = 'inline-block';
                document.getElementById('recordingTimer').style.display = 'block';
                document.getElementById('recordingStatus').innerHTML = '<p class="text-danger">🎤 正在录音...</p>';

                // 开始计时
                window.recordingStartTime = Date.now();
                window.recordingTimer = setInterval(updateRecordingTimer, 100);

                // 自动停止录音
                setTimeout(() => {
                    if (isRecording) {
                        stopMusicRecording();
                    }
                }, window.maxRecordingDuration * 1000);
            }

            function stopMusicRecording() {
                stopRecording();

                // 停止计时
                if (window.recordingTimer) {
                    clearInterval(window.recordingTimer);
                    window.recordingTimer = null;
                }

                // 更新UI
                document.getElementById('stopRecordBtn').style.display = 'none';
                document.getElementById('recordingTimer').style.display = 'none';
                document.getElementById('recordingStatus').innerHTML = '<p class="text-success">录音完成！</p>';
            }

            function updateRecordingTimer() {
                if (!window.recordingStartTime) return;

                const elapsed = (Date.now() - window.recordingStartTime) / 1000;
                const minutes = Math.floor(elapsed / 60);
                const seconds = Math.floor(elapsed % 60);
                const progress = (elapsed / window.maxRecordingDuration) * 100;

                document.getElementById('timerDisplay').textContent =
                    `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
                document.getElementById('recordingProgress').style.width = `${Math.min(progress, 100)}%`;
            }

            // 录音完成回调
            window.recordingCallback = function(blob, audioUrl) {
                // 显示播放控件
                document.getElementById('playbackControls').style.display = 'block';
                document.getElementById('recordedAudio').src = audioUrl;

                // 存储录音数据
                window.currentRecordingBlob = blob;
                window.currentRecordingUrl = audioUrl;
            };

            function confirmRecording() {
                if (window.currentRecordingCallback && window.currentRecordingBlob) {
                    window.currentRecordingCallback(window.currentRecordingBlob, window.currentRecordingUrl);
                }
                bootstrap.Modal.getInstance(document.getElementById('recordingModal')).hide();
            }

            function retakeRecording() {
                // 重置UI
                document.getElementById('startRecordBtn').style.display = 'inline-block';
                document.getElementById('playbackControls').style.display = 'none';
                document.getElementById('recordingStatus').innerHTML = '<p>准备录音...</p>';

                // 清理数据
                window.currentRecordingBlob = null;
                window.currentRecordingUrl = null;
            }

            // 上传录音到服务器
            async function uploadRecording(blob, filename = 'recording.webm') {
                try {
                    const formData = new FormData();
                    formData.append('file', blob, filename);

                    const response = await fetch('/admin/upload', {
                        method: 'POST',
                        body: formData
                    });

                    const result = await response.json();
                    if (result.code === 0) {
                        return result.data.url;
                    } else {
                        throw new Error(result.msg);
                    }
                } catch (error) {
                    console.error('录音上传失败:', error);
                    alert('录音上传失败: ' + error.message);
                    return null;
                }
            }
        });
        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 默认显示本地游戏
            showLocalGame();
            // 检查预设功能是否启用
            checkPresetFeatureEnabled();
            // 检查并显示当前预设
            checkCurrentPreset();
        });

        // 检查并显示当前预设
        function checkCurrentPreset() {
            const currentPreset = localStorage.getItem('currentPreset');
            if (currentPreset) {
                try {
                    const preset = JSON.parse(currentPreset);
                    updateCurrentPresetDisplay(preset);
                } catch (e) {
                    console.error('解析当前预设失败:', e);
                }
            }
        }

        // 检查预设功能是否启用
        function checkPresetFeatureEnabled() {
            fetch('/admin/preset-feature-status')
                .then(response => response.json())
                .then(data => {
                    if (data.code === 0 && data.data.enabled) {
                        // 启用预设功能
                        document.getElementById('presetSection').style.display = 'block';
                        loadPresetButtons();
                    }
                })
                .catch(error => {
                    console.log('预设功能检查失败，默认禁用:', error);
                    // 默认禁用预设功能
                });
        }

        // 加载预设按钮
        function loadPresetButtons() {
            fetch('/game/presets')
                .then(response => response.json())
                .then(data => {
                    if (data.code === 0) {
                        displayPresetButtons(data.data);
                    }
                })
                .catch(error => {
                    console.error('加载预设失败:', error);
                });
        }

        // 显示预设按钮
        function displayPresetButtons(presets) {
            const container = document.getElementById('presetButtons');
            container.innerHTML = '';

            // 只显示前6个最常用的预设
            const popularPresets = presets.slice(0, 6);

            popularPresets.forEach(preset => {
                const col = document.createElement('div');
                col.className = 'col-md-4 col-sm-6 mb-2';
                col.innerHTML = `
                    <button class="btn btn-outline-primary btn-sm w-100" onclick="applyPreset(${preset.id})">
                        ${preset.name}
                    </button>
                `;
                container.appendChild(col);
            });
        }

        // 应用预设
        function applyPreset(presetId) {
            // 生成或获取会话ID
            let sessionId = localStorage.getItem('gameSessionId');
            if (!sessionId) {
                sessionId = 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
                localStorage.setItem('gameSessionId', sessionId);
            }

            fetch('/game/apply-preset', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    presetId: presetId,
                    sessionId: sessionId
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.code === 0) {
                    // 保存当前应用的预设信息
                    localStorage.setItem('currentPreset', JSON.stringify(data.data.preset));

                    // 更新UI显示当前预设
                    updateCurrentPresetDisplay(data.data.preset);

                    alert('预设应用成功！现在游戏内容将根据预设进行筛选。');
                } else {
                    alert('应用预设失败: ' + data.msg);
                }
            })
            .catch(error => {
                console.error('应用预设失败:', error);
                alert('应用预设失败: ' + error.message);
            });
        }

        // 更新当前预设显示
        function updateCurrentPresetDisplay(preset) {
            // 在页面上显示当前应用的预设
            const presetInfo = document.getElementById('currentPresetInfo');
            if (presetInfo) {
                presetInfo.innerHTML = `
                    <div class="alert alert-success">
                        <strong>🎯 当前预设:</strong> ${preset.name}<br>
                        <small>${preset.description}</small>
                    </div>
                `;
            }
        }

        // 将设置应用到界面
        function applySettingsToUI(settings) {
            // 应用奖惩游戏设置
            if (settings.punishment_reward) {
                if (settings.punishment_reward.enabled) {
                    document.getElementById('gameType').value = settings.punishment_reward.type || 0;
                    document.getElementById('gameLevel').value = settings.punishment_reward.level || 1;
                }
            }

            // 这里可以添加其他游戏模式的设置应用逻辑
            console.log('应用设置:', settings);
        }

        // 显示所有预设
        function showAllPresets() {
            fetch('/game/presets')
                .then(response => response.json())
                .then(data => {
                    if (data.code === 0) {
                        displayAllPresets(data.data);
                        const modal = new bootstrap.Modal(document.getElementById('presetsModal'));
                        modal.show();
                    }
                })
                .catch(error => {
                    console.error('加载预设失败:', error);
                });
        }

        // 显示所有预设列表
        function displayAllPresets(presets) {
            const container = document.getElementById('allPresetsList');
            container.innerHTML = '';

            // 按分类分组
            const categories = {
                'friendship': '👫 朋友关系',
                'ambiguous': '😏 暧昧关系',
                'couple': '💕 情侣关系',
                'special': '🎪 特殊玩法',
                'age_group': '🎓 年龄分组',
                'custom': '🛠️ 自定义'
            };

            Object.keys(categories).forEach(category => {
                const categoryPresets = presets.filter(p => p.category === category);
                if (categoryPresets.length > 0) {
                    const categoryDiv = document.createElement('div');
                    categoryDiv.className = 'col-12 mb-3';
                    categoryDiv.innerHTML = `
                        <h6>${categories[category]}</h6>
                        <div class="row">
                            ${categoryPresets.map(preset => `
                                <div class="col-md-6 mb-2">
                                    <div class="card">
                                        <div class="card-body p-2">
                                            <h6 class="card-title mb-1">${preset.name}</h6>
                                            <p class="card-text small text-muted mb-2">${preset.description}</p>
                                            <button class="btn btn-primary btn-sm" onclick="applyPreset(${preset.id}); bootstrap.Modal.getInstance(document.getElementById('presetsModal')).hide();">
                                                应用此预设
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            `).join('')}
                        </div>
                    `;
                    container.appendChild(categoryDiv);
                }
            });
        }

        // 显示保存预设模态框
        function showSavePresetModal() {
            const modal = new bootstrap.Modal(document.getElementById('savePresetModal'));
            modal.show();
        }

        // 保存当前设置为预设
        function saveCurrentPreset() {
            const name = document.getElementById('presetName').value.trim();
            const description = document.getElementById('presetDescription').value.trim();
            const createdBy = document.getElementById('createdBy').value.trim();

            if (!name) {
                alert('请输入预设名称');
                return;
            }

            if (!description) {
                alert('请输入预设描述');
                return;
            }

            // 收集当前设置
            const settings = getCurrentSettings();

            fetch('/game/save-preset', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    name: name,
                    description: description,
                    settings: settings,
                    createdBy: createdBy
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.code === 0) {
                    alert('预设保存成功！');
                    bootstrap.Modal.getInstance(document.getElementById('savePresetModal')).hide();
                    // 清空表单
                    document.getElementById('presetName').value = '';
                    document.getElementById('presetDescription').value = '';
                    document.getElementById('createdBy').value = '';
                    // 重新加载预设按钮
                    loadPresetButtons();
                } else {
                    alert('保存预设失败: ' + data.msg);
                }
            })
            .catch(error => {
                console.error('保存预设失败:', error);
                alert('保存预设失败: ' + error.message);
            });
        }

        // 获取当前设置
        function getCurrentSettings() {
            return {
                punishment_reward: {
                    enabled: true,
                    type: parseInt(document.getElementById('gameType').value) || 0,
                    level: parseInt(document.getElementById('gameLevel').value) || 1
                },
                slot_machine: {
                    enabled: true
                },
                truth_dare: {
                    enabled: true,
                    level: 2,
                    type: 0
                },
                dice_challenge: {
                    enabled: true,
                    level: 2
                },
                love_quiz: {
                    enabled: true,
                    level: 2
                },
                photo_challenge: {
                    enabled: true,
                    level: 2
                }
            };
        }
    </script>
</body>
</html>