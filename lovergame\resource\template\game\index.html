<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{.title}}</title>
    <link rel="icon" href="data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 viewBox=%220 0 100 100%22><text y=%22.9em%22 font-size=%2290%22>🎮</text></svg>">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Arial', sans-serif;
        }
        .game-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            padding: 40px;
            margin-top: 50px;
        }
        .player-card {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 20px;
            margin: 10px;
            border: 3px solid transparent;
            transition: all 0.3s ease;
        }
        .player-card.selected {
            border-color: #dc3545;
            background: #fff5f5;
            transform: scale(1.05);
        }
        .start-btn {
            background: linear-gradient(45deg, #FF6B6B, #4ECDC4);
            border: none;
            border-radius: 50px;
            padding: 15px 40px;
            font-size: 18px;
            font-weight: bold;
            color: white;
            transition: all 0.3s ease;
        }
        .start-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.2);
        }
        .result-card {
            background: #fff;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            margin-top: 20px;
            display: none;
        }
        .media-container {
            text-align: center;
            margin: 20px 0;
        }
        .media-container img, .media-container video {
            max-width: 100%;
            max-height: 300px;
            border-radius: 10px;
        }
        .roulette {
            width: 200px;
            height: 200px;
            border: 10px solid #ddd;
            border-radius: 50%;
            margin: 20px auto;
            position: relative;
            animation: spin 2s linear infinite;
        }
        .roulette.stopped {
            animation: none;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .roulette::before {
            content: '';
            position: absolute;
            top: -20px;
            left: 50%;
            transform: translateX(-50%);
            width: 0;
            height: 0;
            border-left: 15px solid transparent;
            border-right: 15px solid transparent;
            border-bottom: 20px solid #dc3545;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="game-container">
                    <h1 class="text-center mb-4">🎮 双人互动奖惩游戏</h1>
                    <p class="text-center text-muted mb-4">让命运决定谁来接受挑战吧！</p>

                    <!-- 游戏模式选择 -->
                    <div id="modeSelection" class="text-center mb-4">
                        <h5>选择游戏模式</h5>
                        <div class="btn-group" role="group">
                            <button type="button" class="btn btn-outline-primary active" onclick="showLocalGame(event)">本地游戏</button>
                            <button type="button" class="btn btn-outline-success" onclick="showRoomGame(event)">房间游戏</button>
                        </div>
                    </div>

                    <!-- 本地游戏设置 -->
                    <div id="gameSetup">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="player-card">
                                    <h5>🎯 玩家1</h5>
                                    <input type="text" class="form-control" id="player1Name" placeholder="请输入玩家1姓名">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="player-card">
                                    <h5>🎯 玩家2</h5>
                                    <input type="text" class="form-control" id="player2Name" placeholder="请输入玩家2姓名">
                                </div>
                            </div>
                        </div>
                        
                        <div class="row mt-4">
                            <div class="col-md-6">
                                <label for="gameType" class="form-label">游戏类型</label>
                                <select class="form-select" id="gameType">
                                    <option value="0">随机（奖励+惩罚）</option>
                                    <option value="1">只有奖励</option>
                                    <option value="2">只有惩罚</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label for="gameLevel" class="form-label">游戏级别</label>
                                <select class="form-select" id="gameLevel">
                                    <option value="0">随机级别</option>
                                    <option value="1">轻微</option>
                                    <option value="2">一般</option>
                                    <option value="3">严重</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="text-center mt-4">
                            <button class="btn start-btn" onclick="startGame()">🎲 开始游戏</button>
                        </div>
                    </div>
                    
                    <!-- 游戏进行中 -->
                    <div id="gameProgress" style="display: none;">
                        <div class="text-center">
                            <h3>🎰 正在选择...</h3>
                            <div class="roulette" id="roulette"></div>
                            <p class="text-muted">命运之轮正在转动...</p>
                        </div>
                    </div>
                    
                    <!-- 游戏结果 -->
                    <div id="gameResult" class="result-card">
                        <div class="text-center">
                            <h3 id="resultTitle"></h3>
                            <div class="alert alert-info" id="selectedPlayer"></div>
                            <div class="card mt-3">
                                <div class="card-body">
                                    <h5 class="card-title" id="itemTitle"></h5>
                                    <p class="card-text" id="itemContent"></p>
                                    <div id="mediaContainer" class="media-container"></div>
                                </div>
                            </div>
                            <button class="btn btn-primary mt-3" onclick="continueGame()">🎮 继续游戏</button>
                            <button class="btn btn-outline-primary mt-3" onclick="resetGame()">🔄 重新设置</button>
                            <a href="/admin" class="btn btn-outline-secondary mt-3">⚙️ 后台管理</a>
                        </div>
                    </div>

                    <!-- 房间游戏界面 -->
                    <div id="roomGameSetup" style="display: none;">
                        <div class="row mb-3">
                            <div class="col-md-12">
                                <div class="btn-group w-100" role="group">
                                    <button type="button" class="btn btn-outline-primary" onclick="showCreateRoom()">创建房间</button>
                                    <button type="button" class="btn btn-outline-success" onclick="showJoinRoom()">加入房间</button>
                                </div>
                            </div>
                        </div>

                        <!-- 创建房间 -->
                        <div id="createRoomPanel" style="display: none;">
                            <h5>创建房间</h5>
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label for="roomName" class="form-label">房间名字</label>
                                    <input type="text" class="form-control" id="roomName" placeholder="请输入房间名字（可选）">
                                </div>
                                <div class="col-md-6">
                                    <label for="roomCreatorName" class="form-label">您的姓名</label>
                                    <input type="text" class="form-control" id="roomCreatorName" placeholder="请输入您的姓名">
                                </div>
                            </div>
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label for="roomGameType" class="form-label">游戏类型</label>
                                    <select class="form-select" id="roomGameType">
                                        <option value="0">随机类型</option>
                                        <option value="1">只有奖励</option>
                                        <option value="2">只有惩罚</option>
                                    </select>
                                </div>
                                <div class="col-md-6">
                                    <label for="roomGameLevel" class="form-label">游戏级别</label>
                                    <select class="form-select" id="roomGameLevel">
                                        <option value="0">随机级别</option>
                                        <option value="1">轻微</option>
                                        <option value="2">一般</option>
                                        <option value="3">严重</option>
                                    </select>
                                </div>
                            </div>

                            <button class="btn btn-primary" onclick="createRoom()">创建房间</button>
                        </div>

                        <!-- 加入房间 -->
                        <div id="joinRoomPanel" style="display: none;">
                            <h5>加入房间</h5>
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label for="joinPlayerName" class="form-label">您的姓名</label>
                                    <input type="text" class="form-control" id="joinPlayerName" placeholder="请输入您的姓名">
                                </div>
                                <div class="col-md-6">
                                    <label for="joinRoomId" class="form-label">房间ID</label>
                                    <input type="text" class="form-control" id="joinRoomId" placeholder="请输入房间ID">
                                </div>
                            </div>
                            <button class="btn btn-success" onclick="joinRoom()">加入房间</button>
                        </div>

                        <!-- 房间状态 -->
                        <div id="roomStatus" style="display: none;">
                            <div class="alert alert-info">
                                <h5 id="roomTitle">房间信息</h5>
                                <p id="roomInfo"></p>
                                <div id="roomPlayers"></div>
                                <div id="roomActions" class="mt-3"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function startGame() {
            const player1Name = document.getElementById('player1Name').value.trim();
            const player2Name = document.getElementById('player2Name').value.trim();
            const gameType = parseInt(document.getElementById('gameType').value);
            const gameLevel = parseInt(document.getElementById('gameLevel').value);

            if (!player1Name || !player2Name) {
                alert('请输入两个玩家的姓名！');
                return;
            }

            if (player1Name === player2Name) {
                alert('两个玩家姓名不能相同！');
                return;
            }

            // 保存游戏设置供连续游戏使用
            currentGameSettings = {
                player1Name: player1Name,
                player2Name: player2Name,
                type: gameType,
                level: gameLevel
            };

            // 开始游戏
            startGameWithSettings(currentGameSettings);
        }
        
        function showResult(result) {
            document.getElementById('gameProgress').style.display = 'none';
            document.getElementById('gameResult').style.display = 'block';

            const selectedPlayerName = result.selectedPlayer === 1 ? result.player1Name : result.player2Name;
            const item = result.selectedItem;
            const typeText = item.type === 1 ? '🎁 恭喜！获得奖励' : '😅 很遗憾！接受惩罚';
            const levelText = ['', '轻微', '一般', '严重'][item.level];

            document.getElementById('resultTitle').textContent = typeText;
            document.getElementById('selectedPlayer').innerHTML = `<strong>${selectedPlayerName}</strong> 被选中了！级别：${levelText}`;
            document.getElementById('itemTitle').textContent = item.title;
            document.getElementById('itemContent').textContent = item.content;

            // 显示媒体内容
            const mediaContainer = document.getElementById('mediaContainer');
            mediaContainer.innerHTML = '';

            console.log('Media info:', {
                mediaUrl: item.mediaUrl,
                mediaType: item.mediaType
            });

            if (item.mediaUrl && item.mediaType > 0) {
                let mediaElement = '';
                switch (item.mediaType) {
                    case 1: // 图片
                        mediaElement = `<div class="text-center mt-3">
                                         <img src="${item.mediaUrl}" alt="${item.title}" class="img-fluid rounded" style="max-width: 300px; max-height: 300px;">
                                       </div>`;
                        break;
                    case 2: // 音频
                        mediaElement = `<div class="text-center mt-3">
                                         <audio controls class="w-100" style="max-width: 400px;">
                                           <source src="${item.mediaUrl}" type="audio/mpeg">
                                           <source src="${item.mediaUrl}" type="audio/wav">
                                           <source src="${item.mediaUrl}" type="audio/ogg">
                                           您的浏览器不支持音频播放。
                                         </audio>
                                       </div>`;
                        break;
                    case 3: // 视频
                        mediaElement = `<div class="text-center mt-3">
                                         <video controls class="w-100 rounded" style="max-width: 400px; max-height: 300px;">
                                           <source src="${item.mediaUrl}" type="video/mp4">
                                           <source src="${item.mediaUrl}" type="video/webm">
                                           <source src="${item.mediaUrl}" type="video/ogg">
                                           您的浏览器不支持视频播放。
                                         </video>
                                       </div>`;
                        break;
                }
                if (mediaElement) {
                    mediaContainer.innerHTML = mediaElement;
                }
            }
        }
        
        // 全局变量保存当前游戏设置
        let currentGameSettings = null;
        let currentRoom = null;
        let roomCheckInterval = null;

        function continueGame() {
            if (currentRoom) {
                // 房间游戏：继续房间游戏
                startRoomGame();
            } else if (currentGameSettings) {
                // 本地游戏：使用保存的设置继续游戏
                startGameWithSettings(currentGameSettings);
            } else {
                // 如果没有保存的设置，回到设置页面
                resetGame();
            }
        }

        function startGameWithSettings(settings) {
            // 显示进行中界面
            document.getElementById('gameSetup').style.display = 'none';
            document.getElementById('gameProgress').style.display = 'block';
            document.getElementById('gameResult').style.display = 'none';

            // 发送请求
            fetch('/game/play', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(settings)
            })
            .then(response => response.json())
            .then(data => {
                if (data.code === 0) {
                    // 延迟显示结果，增加悬念
                    setTimeout(() => {
                        showResult(data.data);
                    }, 3000);
                } else {
                    alert('游戏出错：' + data.msg);
                    resetGame();
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('网络错误，请重试');
                resetGame();
            });
        }

        function resetGame() {
            document.getElementById('gameSetup').style.display = 'block';
            document.getElementById('gameProgress').style.display = 'none';
            document.getElementById('gameResult').style.display = 'none';

            // 清空输入框
            document.getElementById('player1Name').value = '';
            document.getElementById('player2Name').value = '';
            document.getElementById('gameType').value = '0';
            document.getElementById('gameLevel').value = '0';

            // 清空保存的设置
            currentGameSettings = null;
            currentRoom = null;

            // 清理房间检查定时器
            if (roomCheckInterval) {
                clearInterval(roomCheckInterval);
                roomCheckInterval = null;
            }
        }

        // 显示本地游戏
        function showLocalGame(event) {
            document.getElementById('gameSetup').style.display = 'block';
            document.getElementById('roomGameSetup').style.display = 'none';

            // 更新按钮状态
            document.querySelectorAll('#modeSelection .btn').forEach(btn => btn.classList.remove('active'));
            if (event && event.target) {
                event.target.classList.add('active');
            } else {
                // 如果没有event，默认激活第一个按钮（本地游戏）
                const localBtn = document.querySelector('#modeSelection .btn:first-child');
                if (localBtn) localBtn.classList.add('active');
            }
        }

        // 显示房间游戏
        function showRoomGame(event) {
            document.getElementById('gameSetup').style.display = 'none';
            document.getElementById('roomGameSetup').style.display = 'block';

            // 更新按钮状态
            document.querySelectorAll('#modeSelection .btn').forEach(btn => btn.classList.remove('active'));
            if (event && event.target) {
                event.target.classList.add('active');
            } else {
                // 如果没有event，默认激活第二个按钮（房间游戏）
                const roomBtn = document.querySelector('#modeSelection .btn:last-child');
                if (roomBtn) roomBtn.classList.add('active');
            }
        }

        // 显示创建房间面板
        function showCreateRoom() {
            document.getElementById('createRoomPanel').style.display = 'block';
            document.getElementById('joinRoomPanel').style.display = 'none';
            document.getElementById('roomStatus').style.display = 'none';
        }

        // 显示加入房间面板
        function showJoinRoom() {
            document.getElementById('createRoomPanel').style.display = 'none';
            document.getElementById('joinRoomPanel').style.display = 'block';
            document.getElementById('roomStatus').style.display = 'none';
        }

        // 创建房间
        function createRoom() {
            const roomName = document.getElementById('roomName').value.trim();
            const creatorName = document.getElementById('roomCreatorName').value.trim();
            const gameType = parseInt(document.getElementById('roomGameType').value);
            const gameLevel = parseInt(document.getElementById('roomGameLevel').value);

            if (!creatorName) {
                alert('请输入您的姓名！');
                return;
            }

            fetch('/room/create', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    name: roomName || '', // 房间名字，可选
                    createdBy: creatorName,
                    type: gameType,
                    level: gameLevel
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.code === 0) {
                    currentRoom = data.data;
                    showRoomStatus();
                    startRoomCheck();
                } else {
                    alert('创建房间失败：' + data.msg);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('网络错误，请重试');
            });
        }

        // 加入房间
        function joinRoom() {
            const playerName = document.getElementById('joinPlayerName').value.trim();
            const roomId = document.getElementById('joinRoomId').value.trim();

            if (!playerName || !roomId) {
                alert('请输入姓名和房间ID！');
                return;
            }

            fetch('/room/join', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    roomId: roomId,
                    playerName: playerName
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.code === 0) {
                    currentRoom = data.data;
                    showRoomStatus();
                    startRoomCheck();
                } else {
                    alert('加入房间失败：' + data.msg);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('网络错误，请重试');
            });
        }

        // 显示房间状态
        function showRoomStatus() {
            if (!currentRoom) return;

            document.getElementById('createRoomPanel').style.display = 'none';
            document.getElementById('joinRoomPanel').style.display = 'none';
            document.getElementById('roomStatus').style.display = 'block';

            const roomTitle = currentRoom.name ? `${currentRoom.name} (${currentRoom.id})` : `房间 ${currentRoom.id}`;
            document.getElementById('roomTitle').textContent = roomTitle;
            document.getElementById('roomInfo').innerHTML = `
                创建者: ${currentRoom.createdBy}<br>
                游戏类型: ${['随机', '奖励', '惩罚'][currentRoom.settings.type]}<br>
                游戏级别: ${['随机', '轻微', '一般', '严重'][currentRoom.settings.level]}
            `;

            const playersHtml = currentRoom.players.map(player =>
                `<span class="badge bg-primary me-2">${player}</span>`
            ).join('');
            document.getElementById('roomPlayers').innerHTML = `
                <strong>玩家 (${currentRoom.players.length}/2):</strong><br>
                ${playersHtml}
            `;

            const actionsDiv = document.getElementById('roomActions');
            if (currentRoom.status === 'waiting') {
                if (currentRoom.players.length === 2) {
                    actionsDiv.innerHTML = `
                        <button class="btn btn-success" onclick="startRoomGame()">开始游戏</button>
                        <button class="btn btn-outline-secondary" onclick="copyRoomId()">复制房间ID</button>
                    `;
                } else {
                    actionsDiv.innerHTML = `
                        <p class="text-muted">等待另一位玩家加入...</p>
                        <button class="btn btn-outline-secondary" onclick="copyRoomId()">复制房间ID</button>
                    `;
                }
            } else if (currentRoom.status === 'playing' && currentRoom.currentGame) {
                showResult(currentRoom.currentGame);
            }
        }

        // 复制房间ID
        function copyRoomId() {
            if (currentRoom) {
                navigator.clipboard.writeText(currentRoom.id).then(() => {
                    alert('房间ID已复制到剪贴板！');
                }).catch(() => {
                    prompt('房间ID（请手动复制）:', currentRoom.id);
                });
            }
        }

        // 开始房间游戏
        function startRoomGame() {
            if (!currentRoom) return;

            fetch(`/room/${currentRoom.id}/start`, {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                if (data.code === 0) {
                    // 显示进行中界面
                    document.getElementById('roomGameSetup').style.display = 'none';
                    document.getElementById('gameProgress').style.display = 'block';
                    document.getElementById('gameResult').style.display = 'none';

                    // 延迟显示结果
                    setTimeout(() => {
                        showResult(data.data);
                    }, 3000);
                } else {
                    alert('开始游戏失败：' + data.msg);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('网络错误，请重试');
            });
        }

        // 开始房间状态检查
        function startRoomCheck() {
            if (roomCheckInterval) {
                clearInterval(roomCheckInterval);
            }

            roomCheckInterval = setInterval(() => {
                if (currentRoom) {
                    fetch(`/room/${currentRoom.id}`)
                        .then(response => response.json())
                        .then(data => {
                            if (data.code === 0) {
                                currentRoom = data.data;
                                showRoomStatus();
                            }
                        })
                        .catch(error => {
                            console.error('Room check error:', error);
                        });
                }
            }, 2000); // 每2秒检查一次
        }

        // 页面加载时默认显示本地游戏
        document.addEventListener('DOMContentLoaded', function() {
            showLocalGame();
        });
    </script>
</body>
</html>