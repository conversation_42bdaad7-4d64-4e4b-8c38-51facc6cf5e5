<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{.title}}</title>
    <link rel="icon" href="data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 viewBox=%220 0 100 100%22><text y=%22.9em%22 font-size=%2290%22>🎮</text></svg>">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Arial', sans-serif;
        }
        .game-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            padding: 40px;
            margin-top: 50px;
        }
        .player-card {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 20px;
            margin: 10px;
            border: 3px solid transparent;
            transition: all 0.3s ease;
        }
        .player-card.selected {
            border-color: #dc3545;
            background: #fff5f5;
            transform: scale(1.05);
        }
        .start-btn {
            background: linear-gradient(45deg, #FF6B6B, #4ECDC4);
            border: none;
            border-radius: 50px;
            padding: 15px 40px;
            font-size: 18px;
            font-weight: bold;
            color: white;
            transition: all 0.3s ease;
        }
        .start-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.2);
        }
        .result-card {
            background: #fff;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            margin-top: 20px;
            display: none;
        }
        .media-container {
            text-align: center;
            margin: 20px 0;
        }
        .media-container img, .media-container video {
            max-width: 100%;
            max-height: 300px;
            border-radius: 10px;
        }
        .roulette {
            width: 200px;
            height: 200px;
            border: 10px solid #ddd;
            border-radius: 50%;
            margin: 20px auto;
            position: relative;
            animation: spin 2s linear infinite;
        }
        .roulette.stopped {
            animation: none;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .roulette::before {
            content: '';
            position: absolute;
            top: -20px;
            left: 50%;
            transform: translateX(-50%);
            width: 0;
            height: 0;
            border-left: 15px solid transparent;
            border-right: 15px solid transparent;
            border-bottom: 20px solid #dc3545;
        }

        /* 老虎机样式 */
        .slot-machine-container {
            background: linear-gradient(145deg, #FFD700, #FFA500);
            border-radius: 20px;
            padding: 30px;
            margin: 20px auto;
            max-width: 400px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            border: 3px solid #B8860B;
        }
        .slot-reels {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin: 20px 0;
        }
        .slot-reel {
            width: 90px;
            height: 120px;
            background: linear-gradient(180deg, #f8f9fa, #e9ecef);
            border: 4px solid #333;
            border-radius: 15px;
            position: relative;
            overflow: hidden;
            box-shadow: inset 0 2px 10px rgba(0,0,0,0.2);
        }
        .slot-reel::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(180deg,
                rgba(255,255,255,0.3) 0%,
                transparent 20%,
                transparent 80%,
                rgba(0,0,0,0.1) 100%);
            pointer-events: none;
            z-index: 2;
        }
        .slot-symbol-container {
            position: absolute;
            width: 100%;
            height: 100%;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }
        .slot-reel.spinning .slot-symbol-container {
            animation: slotSpin 0.15s ease-in-out infinite;
        }
        .slot-symbol {
            font-size: 45px;
            line-height: 1;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
            transition: transform 0.3s ease;
        }
        .slot-reel:not(.spinning) .slot-symbol {
            animation: symbolBounce 0.6s ease-out;
        }

        /* 改进的转动动画 */
        @keyframes slotSpin {
            0% { transform: translateY(0px); }
            25% { transform: translateY(-30px); }
            50% { transform: translateY(-60px); }
            75% { transform: translateY(-30px); }
            100% { transform: translateY(0px); }
        }

        /* 停止时的弹跳效果 */
        @keyframes symbolBounce {
            0% { transform: scale(0.8) rotateY(90deg); }
            50% { transform: scale(1.1) rotateY(0deg); }
            100% { transform: scale(1) rotateY(0deg); }
        }

        /* 发光效果 */
        .slot-reel.winning {
            animation: slotGlow 1s ease-in-out infinite alternate;
            border-color: #FFD700;
        }

        @keyframes slotGlow {
            0% {
                box-shadow: inset 0 2px 10px rgba(0,0,0,0.2),
                           0 0 20px rgba(255, 215, 0, 0.5);
            }
            100% {
                box-shadow: inset 0 2px 10px rgba(0,0,0,0.2),
                           0 0 30px rgba(255, 215, 0, 0.8);
            }
        }

        /* 老虎机结果样式 */
        .slot-result {
            background: linear-gradient(145deg, #FF6B6B, #4ECDC4);
            color: white;
            border-radius: 15px;
            padding: 20px;
            margin: 20px 0;
            animation: resultAppear 0.8s ease-out;
        }
        .slot-combination {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin: 15px 0;
        }
        .slot-combination .symbol {
            font-size: 35px;
            background: rgba(255,255,255,0.3);
            border-radius: 50%;
            width: 60px;
            height: 60px;
            display: flex;
            align-items: center;
            justify-content: center;
            border: 2px solid rgba(255,255,255,0.5);
            animation: symbolPop 0.6s ease-out;
            animation-delay: calc(var(--delay) * 0.2s);
            transform: scale(0);
            animation-fill-mode: forwards;
        }

        @keyframes resultAppear {
            0% {
                opacity: 0;
                transform: translateY(30px) scale(0.8);
            }
            100% {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        @keyframes symbolPop {
            0% {
                transform: scale(0) rotate(180deg);
                opacity: 0;
            }
            70% {
                transform: scale(1.2) rotate(0deg);
                opacity: 1;
            }
            100% {
                transform: scale(1) rotate(0deg);
                opacity: 1;
            }
        }

        /* 获胜时的特殊效果 */
        .slot-result.winning {
            background: linear-gradient(145deg, #FFD700, #FFA500);
            animation: resultAppear 0.8s ease-out, winningPulse 2s ease-in-out infinite;
        }

        @keyframes winningPulse {
            0%, 100% {
                box-shadow: 0 0 20px rgba(255, 215, 0, 0.5);
                transform: scale(1);
            }
            50% {
                box-shadow: 0 0 40px rgba(255, 215, 0, 0.8);
                transform: scale(1.02);
            }
        }

        /* 真心话大冒险样式 */
        .truth-dare-container {
            background: linear-gradient(145deg, #FF6B6B, #4ECDC4);
            border-radius: 20px;
            padding: 30px;
            margin: 20px auto;
            max-width: 500px;
            color: white;
            text-align: center;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        }

        .truth-dare-type {
            font-size: 60px;
            margin-bottom: 20px;
            animation: typeReveal 1s ease-out;
        }

        .truth-dare-question {
            background: rgba(255,255,255,0.2);
            border-radius: 15px;
            padding: 20px;
            margin: 20px 0;
            animation: questionSlide 1.2s ease-out;
        }

        @keyframes typeReveal {
            0% {
                opacity: 0;
                transform: rotateY(90deg) scale(0.5);
            }
            100% {
                opacity: 1;
                transform: rotateY(0deg) scale(1);
            }
        }

        @keyframes questionSlide {
            0% {
                opacity: 0;
                transform: translateY(30px);
            }
            100% {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* 玩家信息样式 */
        .player-info {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .player-card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            padding: 15px;
            text-align: center;
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }

        .player-card.active {
            background: rgba(255, 255, 255, 0.2);
            border-color: #ffd700;
            transform: scale(1.05);
            box-shadow: 0 5px 15px rgba(255, 215, 0, 0.3);
        }

        .player-card.winner {
            background: rgba(76, 175, 80, 0.2);
            border-color: #4caf50;
            animation: winnerPulse 2s infinite;
        }

        @keyframes winnerPulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .player-avatar {
            font-size: 2.5rem;
            margin-bottom: 10px;
            filter: drop-shadow(0 2px 4px rgba(0,0,0,0.3));
        }

        .player-name {
            font-size: 1.1rem;
            font-weight: bold;
            color: white;
            margin-bottom: 5px;
        }

        .player-status {
            font-size: 0.9rem;
            color: rgba(255, 255, 255, 0.8);
            min-height: 20px;
        }

        /* 骰子游戏样式 */
        .dice-container {
            background: linear-gradient(145deg, #667eea, #764ba2);
            border-radius: 20px;
            padding: 30px;
            margin: 20px auto;
            max-width: 500px;
            color: white;
            text-align: center;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        }

        .dice-display {
            display: flex;
            justify-content: center;
            gap: 30px;
            margin: 30px 0;
        }

        .dice {
            width: 80px;
            height: 80px;
            background: white;
            color: #333;
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 40px;
            font-weight: bold;
            box-shadow: 0 5px 15px rgba(0,0,0,0.3);
            animation: diceRoll 2s ease-out;
        }

        .dice-challenge {
            background: rgba(255,255,255,0.2);
            border-radius: 15px;
            padding: 20px;
            margin: 20px 0;
            animation: challengeAppear 1.5s ease-out;
        }

        @keyframes diceRoll {
            0% {
                transform: rotateX(0deg) rotateY(0deg);
            }
            25% {
                transform: rotateX(180deg) rotateY(90deg);
            }
            50% {
                transform: rotateX(360deg) rotateY(180deg);
            }
            75% {
                transform: rotateX(540deg) rotateY(270deg);
            }
            100% {
                transform: rotateX(720deg) rotateY(360deg);
            }
        }

        @keyframes challengeAppear {
            0% {
                opacity: 0;
                transform: scale(0.8) translateY(20px);
            }
            100% {
                opacity: 1;
                transform: scale(1) translateY(0);
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="game-container">
                    <h1 class="text-center mb-4">🎮 双人互动奖惩游戏</h1>
                    <p class="text-center text-muted mb-4">让命运决定谁来接受挑战吧！</p>

                    <!-- 游戏类型选择 -->
                    <div id="gameTypeSelection" class="text-center mb-4">
                        <h5>选择游戏类型</h5>
                        <div class="btn-group" role="group">
                            <button type="button" class="btn btn-outline-primary active" onclick="selectGameType('punishment_reward', event)">奖惩游戏</button>
                            <button type="button" class="btn btn-outline-danger" onclick="selectGameType('slot_machine', event)">爱情老虎机</button>
                            <button type="button" class="btn btn-outline-info" onclick="selectGameType('truth_dare', event)">真心话大冒险</button>
                            <button type="button" class="btn btn-outline-warning" onclick="selectGameType('dice_challenge', event)">浪漫骰子</button>
                        </div>
                    </div>

                    <!-- 游戏模式选择 -->
                    <div id="modeSelection" class="text-center mb-4">
                        <h5>选择游戏模式</h5>
                        <div class="btn-group" role="group">
                            <button type="button" class="btn btn-outline-primary active" onclick="showLocalGame(event)">本地游戏</button>
                            <button type="button" class="btn btn-outline-success" onclick="showRoomGame(event)">房间游戏</button>
                        </div>
                    </div>

                    <!-- 本地游戏设置 -->
                    <div id="gameSetup">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="player-card">
                                    <h5>🎯 玩家1</h5>
                                    <input type="text" class="form-control" id="player1Name" placeholder="请输入玩家1姓名">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="player-card">
                                    <h5>🎯 玩家2</h5>
                                    <input type="text" class="form-control" id="player2Name" placeholder="请输入玩家2姓名">
                                </div>
                            </div>
                        </div>
                        
                        <!-- 奖惩游戏设置 -->
                        <div id="punishmentRewardSettings" class="row mt-4">
                            <div class="col-md-6">
                                <label for="gameType" class="form-label">游戏类型</label>
                                <select class="form-select" id="gameType">
                                    <option value="0">随机（奖励+惩罚）</option>
                                    <option value="1">只有奖励</option>
                                    <option value="2">只有惩罚</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label for="gameLevel" class="form-label">游戏级别</label>
                                <select class="form-select" id="gameLevel">
                                    <option value="0">随机级别</option>
                                    <option value="1">轻微</option>
                                    <option value="2">一般</option>
                                    <option value="3">严重</option>
                                </select>
                            </div>
                        </div>

                        <!-- 老虎机游戏设置 -->
                        <div id="slotMachineSettings" class="row mt-4" style="display: none;">
                            <div class="col-md-12">
                                <div class="alert alert-info">
                                    <h6>🎰 爱情老虎机</h6>
                                    <p class="mb-0">转动爱情转盘，获得浪漫活动组合！三个符号的不同组合将决定你们的浪漫活动内容。</p>
                                </div>
                            </div>
                        </div>

                        <!-- 其他游戏设置 -->
                        <div id="otherGameSettings" class="row mt-4" style="display: none;">
                            <div class="col-md-12">
                                <div class="alert alert-info">
                                    <h6>🎮 游戏说明</h6>
                                    <p class="mb-0">真心话大冒险和浪漫骰子游戏无需额外设置，直接开始游戏即可！</p>
                                </div>
                            </div>
                        </div>
                        
                        <div class="text-center mt-4">
                            <button class="btn start-btn" onclick="startGame()">🎲 开始游戏</button>
                        </div>
                    </div>
                    
                    <!-- 游戏进行中 -->
                    <div id="gameProgress" style="display: none;">
                        <!-- 奖惩游戏进行中 -->
                        <div id="punishmentRewardProgress" class="text-center">
                            <h3>🎰 正在选择...</h3>
                            <div class="roulette" id="roulette"></div>
                            <p class="text-muted">命运之轮正在转动...</p>
                        </div>

                        <!-- 老虎机游戏进行中 -->
                        <div id="slotMachineProgress" class="text-center" style="display: none;">
                            <h3>🎰 爱情老虎机转动中...</h3>
                            <div class="slot-machine-container">
                                <div class="slot-reels">
                                    <div class="slot-reel" id="reel1">
                                        <div class="slot-symbol-container">
                                            <div class="slot-symbol">❤️</div>
                                        </div>
                                    </div>
                                    <div class="slot-reel" id="reel2">
                                        <div class="slot-symbol-container">
                                            <div class="slot-symbol">💋</div>
                                        </div>
                                    </div>
                                    <div class="slot-reel" id="reel3">
                                        <div class="slot-symbol-container">
                                            <div class="slot-symbol">🌹</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <p class="text-muted">爱情转盘正在为你们选择浪漫活动...</p>
                        </div>
                    </div>
                    
                    <!-- 游戏结果 -->
                    <div id="gameResult" class="result-card">
                        <!-- 玩家信息显示 -->
                        <div id="playerInfo" class="player-info mb-4">
                            <div class="row">
                                <div class="col-6">
                                    <div class="player-card" id="player1Card">
                                        <div class="player-avatar">👤</div>
                                        <div class="player-name" id="displayPlayer1Name">玩家1</div>
                                        <div class="player-status" id="player1Status"></div>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="player-card" id="player2Card">
                                        <div class="player-avatar">👤</div>
                                        <div class="player-name" id="displayPlayer2Name">玩家2</div>
                                        <div class="player-status" id="player2Status"></div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="text-center">
                            <h3 id="resultTitle"></h3>
                            <div class="alert alert-info" id="selectedPlayer"></div>
                            <div class="card mt-3">
                                <div class="card-body">
                                    <h5 class="card-title" id="itemTitle"></h5>
                                    <p class="card-text" id="itemContent"></p>
                                    <div id="mediaContainer" class="media-container"></div>
                                </div>
                            </div>
                            <button class="btn btn-primary mt-3" onclick="continueGame()">🎮 继续游戏</button>
                            <button class="btn btn-outline-primary mt-3" onclick="resetGame()">🔄 重新设置</button>
                            <a href="/admin" class="btn btn-outline-secondary mt-3">⚙️ 后台管理</a>
                        </div>
                    </div>

                    <!-- 房间游戏界面 -->
                    <div id="roomGameSetup" style="display: none;">
                        <div class="row mb-3">
                            <div class="col-md-12">
                                <div class="btn-group w-100" role="group">
                                    <button type="button" class="btn btn-outline-primary" onclick="showCreateRoom()">创建房间</button>
                                    <button type="button" class="btn btn-outline-success" onclick="showJoinRoom()">加入房间</button>
                                </div>
                            </div>
                        </div>

                        <!-- 创建房间 -->
                        <div id="createRoomPanel" style="display: none;">
                            <h5>创建房间</h5>
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label for="roomName" class="form-label">房间名字</label>
                                    <input type="text" class="form-control" id="roomName" placeholder="请输入房间名字（可选）">
                                </div>
                                <div class="col-md-6">
                                    <label for="roomCreatorName" class="form-label">您的姓名</label>
                                    <input type="text" class="form-control" id="roomCreatorName" placeholder="请输入您的姓名">
                                </div>
                            </div>
                            <div class="row mb-3">
                                <div class="col-md-12">
                                    <label for="roomGameMode" class="form-label">游戏模式</label>
                                    <select class="form-select" id="roomGameMode">
                                        <option value="punishment_reward">奖惩游戏</option>
                                        <option value="slot_machine">爱情老虎机</option>
                                        <option value="truth_dare">真心话大冒险</option>
                                        <option value="dice_challenge">浪漫骰子</option>
                                    </select>
                                </div>
                            </div>
                            <div class="row mb-3" id="roomPunishmentSettings">
                                <div class="col-md-6">
                                    <label for="roomGameType" class="form-label">游戏类型</label>
                                    <select class="form-select" id="roomGameType">
                                        <option value="0">随机类型</option>
                                        <option value="1">只有奖励</option>
                                        <option value="2">只有惩罚</option>
                                    </select>
                                </div>
                                <div class="col-md-6">
                                    <label for="roomGameLevel" class="form-label">游戏级别</label>
                                    <select class="form-select" id="roomGameLevel">
                                        <option value="0">随机级别</option>
                                        <option value="1">轻微</option>
                                        <option value="2">一般</option>
                                        <option value="3">严重</option>
                                    </select>
                                </div>
                            </div>

                            <button class="btn btn-primary" onclick="createRoom()">创建房间</button>
                        </div>

                        <!-- 加入房间 -->
                        <div id="joinRoomPanel" style="display: none;">
                            <h5>加入房间</h5>
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label for="joinPlayerName" class="form-label">您的姓名</label>
                                    <input type="text" class="form-control" id="joinPlayerName" placeholder="请输入您的姓名">
                                </div>
                                <div class="col-md-6">
                                    <label for="joinRoomId" class="form-label">房间ID</label>
                                    <input type="text" class="form-control" id="joinRoomId" placeholder="请输入房间ID">
                                </div>
                            </div>
                            <button class="btn btn-success" onclick="joinRoom()">加入房间</button>
                        </div>

                        <!-- 房间状态 -->
                        <div id="roomStatus" style="display: none;">
                            <div class="alert alert-info">
                                <h5 id="roomTitle">房间信息</h5>
                                <p id="roomInfo"></p>
                                <div id="roomPlayers"></div>
                                <div id="roomActions" class="mt-3"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 全局变量
        let currentGameType = 'punishment_reward';
        let currentGameSettings = null;
        let currentRoom = null;
        let roomCheckInterval = null;

        function selectGameType(gameType, event) {
            currentGameType = gameType;

            // 更新按钮状态
            document.querySelectorAll('#gameTypeSelection .btn').forEach(btn => btn.classList.remove('active'));
            if (event && event.target) {
                event.target.classList.add('active');
            } else {
                // 如果没有event，根据gameType找到对应的按钮并激活
                const buttons = document.querySelectorAll('#gameTypeSelection .btn');
                buttons.forEach(btn => {
                    if ((gameType === 'punishment_reward' && btn.textContent.includes('奖惩游戏')) ||
                        (gameType === 'slot_machine' && btn.textContent.includes('爱情老虎机')) ||
                        (gameType === 'truth_dare' && btn.textContent.includes('真心话大冒险')) ||
                        (gameType === 'dice_challenge' && btn.textContent.includes('浪漫骰子'))) {
                        btn.classList.add('active');
                    }
                });
            }

            // 显示对应的游戏设置
            document.getElementById('punishmentRewardSettings').style.display = gameType === 'punishment_reward' ? 'block' : 'none';
            document.getElementById('slotMachineSettings').style.display = gameType === 'slot_machine' ? 'block' : 'none';
            document.getElementById('otherGameSettings').style.display = (gameType !== 'punishment_reward' && gameType !== 'slot_machine') ? 'block' : 'none';

            // 清除之前的游戏结果和进度显示
            document.getElementById('gameResult').style.display = 'none';
            document.getElementById('gameProgress').style.display = 'none';

            // 重置游戏界面到初始状态
            document.getElementById('gameSetup').style.display = 'block';

            console.log('Selected game type:', gameType); // 添加调试日志
        }

        // 随机玩家名字列表
        const randomNames = [
            '小甜心', '小宝贝', '亲爱的', '小可爱', '小天使', '小公主', '小王子',
            '蜜糖', '糖果', '巧克力', '奶茶', '咖啡', '蛋糕', '布丁',
            '星星', '月亮', '太阳', '彩虹', '花朵', '蝴蝶', '小鸟',
            '阿萌', '阿呆', '阿乖', '阿甜', '阿美', '阿帅', '阿酷'
        ];

        function getRandomName() {
            return randomNames[Math.floor(Math.random() * randomNames.length)];
        }

        function startGame() {
            let player1Name = document.getElementById('player1Name').value.trim();
            let player2Name = document.getElementById('player2Name').value.trim();

            // 如果没有输入玩家名字，使用随机名字
            if (!player1Name) {
                player1Name = getRandomName();
                document.getElementById('player1Name').value = player1Name;
            }
            if (!player2Name) {
                do {
                    player2Name = getRandomName();
                } while (player2Name === player1Name); // 确保两个名字不同
                document.getElementById('player2Name').value = player2Name;
            }

            if (player1Name === player2Name) {
                alert('两个玩家姓名不能相同！');
                return;
            }

            // 根据游戏类型构建请求
            let gameSettings = {
                player1Name: player1Name,
                player2Name: player2Name,
                gameMode: currentGameType
            };

            // 只有奖惩游戏需要类型和级别设置
            if (currentGameType === 'punishment_reward') {
                const gameType = parseInt(document.getElementById('gameType').value);
                const gameLevel = parseInt(document.getElementById('gameLevel').value);
                gameSettings.type = gameType;
                gameSettings.level = gameLevel;
            }

            console.log('Game settings:', gameSettings); // 添加调试日志

            // 所有游戏类型都已支持
            // 无需检查游戏类型

            // 保存游戏设置供连续游戏使用
            currentGameSettings = gameSettings;

            // 开始游戏
            startGameWithSettings(currentGameSettings);
        }
        
        function showResult(result) {
            document.getElementById('gameProgress').style.display = 'none';
            document.getElementById('gameResult').style.display = 'block';

            // 显示玩家信息
            updatePlayerInfo(result);

            if (result.gameMode === 'slot_machine') {
                showSlotMachineResult(result);
            } else if (result.gameMode === 'truth_dare') {
                showTruthDareResult(result);
            } else if (result.gameMode === 'dice_challenge') {
                showDiceChallengeResult(result);
            } else {
                showPunishmentRewardResult(result);
            }
        }

        function updatePlayerInfo(result) {
            // 更新玩家名字显示
            document.getElementById('displayPlayer1Name').textContent = result.player1Name || '玩家1';
            document.getElementById('displayPlayer2Name').textContent = result.player2Name || '玩家2';

            // 重置玩家卡片状态
            document.getElementById('player1Card').classList.remove('active', 'winner');
            document.getElementById('player2Card').classList.remove('active', 'winner');
            document.getElementById('player1Status').textContent = '';
            document.getElementById('player2Status').textContent = '';

            // 根据游戏类型设置玩家状态
            if (result.gameMode === 'punishment_reward' && result.selectedPlayer) {
                // 奖惩游戏：显示被选中的玩家
                if (result.selectedPlayer === 1) {
                    document.getElementById('player1Card').classList.add('active');
                    document.getElementById('player1Status').textContent = '🎯 被选中';
                    document.getElementById('player2Status').textContent = '👀 观看';
                } else {
                    document.getElementById('player2Card').classList.add('active');
                    document.getElementById('player2Status').textContent = '🎯 被选中';
                    document.getElementById('player1Status').textContent = '👀 观看';
                }
            } else if (result.gameMode === 'slot_machine') {
                // 老虎机：两人共同参与
                document.getElementById('player1Status').textContent = '🎰 共同转动';
                document.getElementById('player2Status').textContent = '🎰 共同转动';
                if (result.slotResult && result.slotResult.isWin) {
                    document.getElementById('player1Card').classList.add('winner');
                    document.getElementById('player2Card').classList.add('winner');
                }
            } else if (result.gameMode === 'truth_dare') {
                // 真心话大冒险：轮流参与
                const currentPlayer = Math.floor(Math.random() * 2) + 1;
                if (currentPlayer === 1) {
                    document.getElementById('player1Card').classList.add('active');
                    document.getElementById('player1Status').textContent = '🎭 轮到你了';
                    document.getElementById('player2Status').textContent = '⏳ 等待中';
                } else {
                    document.getElementById('player2Card').classList.add('active');
                    document.getElementById('player2Status').textContent = '🎭 轮到你了';
                    document.getElementById('player1Status').textContent = '⏳ 等待中';
                }
            } else if (result.gameMode === 'dice_challenge') {
                // 骰子挑战：两人共同完成
                document.getElementById('player1Status').textContent = '🎲 共同挑战';
                document.getElementById('player2Status').textContent = '🎲 共同挑战';
                document.getElementById('player1Card').classList.add('active');
                document.getElementById('player2Card').classList.add('active');
            }
        }

        function showPunishmentRewardResult(result) {
            const selectedPlayerName = result.selectedPlayer === 1 ? result.player1Name : result.player2Name;
            const otherPlayerName = result.selectedPlayer === 1 ? result.player2Name : result.player1Name;
            const item = result.selectedItem;
            const typeText = item.type === 1 ? '🎁 恭喜！获得奖励' : '😅 很遗憾！接受惩罚';
            const levelText = ['', '轻微', '一般', '严重'][item.level];

            document.getElementById('resultTitle').textContent = typeText;
            document.getElementById('selectedPlayer').innerHTML = `
                <div class="row">
                    <div class="col-12">
                        <strong>🎯 ${selectedPlayerName}</strong> 被选中了！级别：${levelText}
                    </div>
                    <div class="col-12 mt-2">
                        <small class="text-muted">${otherPlayerName} 负责监督完成哦~ 😊</small>
                    </div>
                </div>
            `;
            document.getElementById('itemTitle').textContent = item.title;
            document.getElementById('itemContent').textContent = item.content;

            // 显示媒体内容
            showMediaContent(item);
        }

        function showSlotMachineResult(result) {
            const slotResult = result.slotResult;
            const combination = slotResult.combination;

            document.getElementById('resultTitle').textContent = '🎰 爱情老虎机结果';

            // 显示符号组合，添加动画延迟
            const symbolsHtml = slotResult.symbols.map((symbol, index) =>
                `<div class="symbol" style="--delay: ${index}">${symbol.emoji}</div>`
            ).join('');

            if (slotResult.isWin) {
                document.getElementById('selectedPlayer').innerHTML = `
                    <div class="slot-combination winning">${symbolsHtml}</div>
                    <div class="alert alert-success mt-2">
                        <div>🎊 恭喜 <strong>${result.player1Name}</strong> 和 <strong>${result.player2Name}</strong>！</div>
                        <div class="mt-2"><small>你们一起获得了特殊组合，共同完成这个浪漫挑战吧！💕</small></div>
                    </div>
                `;
            } else {
                document.getElementById('selectedPlayer').innerHTML = `
                    <div class="slot-combination">${symbolsHtml}</div>
                    <div class="alert alert-info mt-2">
                        <div>💫 <strong>${result.player1Name}</strong> 和 <strong>${result.player2Name}</strong></div>
                        <div class="mt-2"><small>这次没有特殊组合，但爱情的轮盘还在转动！再试一次吧~ 🎰</small></div>
                    </div>
                `;
            }

            document.getElementById('itemTitle').textContent = combination.activityTitle;
            document.getElementById('itemContent').textContent = combination.activityContent;

            // 为获胜结果添加特殊样式
            const gameResult = document.getElementById('gameResult');
            if (slotResult.isWin) {
                gameResult.classList.add('winning');
            } else {
                gameResult.classList.remove('winning');
            }

            // 显示媒体内容（如果有）
            if (combination.mediaUrl) {
                showMediaContent(combination);
            } else {
                document.getElementById('mediaContainer').innerHTML = '';
            }
        }

        function showTruthDareResult(result) {
            const truthDareResult = result.truthDareResult;
            const question = truthDareResult.question;

            document.getElementById('resultTitle').textContent = '💭 真心话大冒险';

            // 显示问题类型和内容
            const typeText = question.type === 1 ? '真心话' : '大冒险';
            const typeIcon = question.type === 1 ? '💭' : '🎯';

            // 随机选择一个玩家来回答/执行
            const currentPlayer = Math.floor(Math.random() * 2) + 1;
            const currentPlayerName = currentPlayer === 1 ? result.player1Name : result.player2Name;
            const otherPlayerName = currentPlayer === 1 ? result.player2Name : result.player1Name;

            document.getElementById('selectedPlayer').innerHTML = `
                <div class="truth-dare-container">
                    <div class="truth-dare-type">${typeIcon}</div>
                    <h4>${typeText}</h4>
                    <div class="alert alert-primary mt-2">
                        <div>🎭 轮到 <strong>${currentPlayerName}</strong> 了！</div>
                        <div class="mt-2"><small class="text-muted">${otherPlayerName} 负责提问和监督哦~ 😊</small></div>
                    </div>
                    <div class="truth-dare-question">
                        <h5>${question.title}</h5>
                        <p>${question.content}</p>
                    </div>
                </div>
            `;

            document.getElementById('itemTitle').textContent = `${typeText}：${question.title}`;
            document.getElementById('itemContent').textContent = question.content;

            // 显示媒体内容（如果有）
            if (question.mediaUrl) {
                showMediaContent(question);
            } else {
                document.getElementById('mediaContainer').innerHTML = '';
            }
        }

        function showDiceChallengeResult(result) {
            const diceResult = result.diceResult;
            const challenge = diceResult.challenge;

            document.getElementById('resultTitle').textContent = '🎲 浪漫骰子';

            // 显示骰子结果和挑战
            document.getElementById('selectedPlayer').innerHTML = `
                <div class="dice-container">
                    <h4>🎲 骰子结果</h4>
                    <div class="dice-display">
                        <div class="dice">${diceResult.dice1}</div>
                        <div class="dice">+</div>
                        <div class="dice">${diceResult.dice2}</div>
                        <div class="dice">=</div>
                        <div class="dice">${diceResult.sum}</div>
                    </div>
                    <div class="alert alert-success mt-3">
                        <div>🎲 <strong>${result.player1Name}</strong> 和 <strong>${result.player2Name}</strong></div>
                        <div class="mt-2"><small>一起完成这个浪漫挑战吧！两人协作会更有趣哦~ 💕</small></div>
                    </div>
                    <div class="dice-challenge">
                        <h5>${challenge.name}</h5>
                        <p><strong>组合：</strong>${challenge.diceCombination}</p>
                        <p><strong>描述：</strong>${challenge.description}</p>
                        ${challenge.requiresProps ? `<p><strong>需要道具：</strong>${challenge.propsNeeded}</p>` : ''}
                        <p><strong>预计时间：</strong>${challenge.durationMinutes}分钟</p>
                    </div>
                </div>
            `;

            document.getElementById('itemTitle').textContent = challenge.activityTitle;
            document.getElementById('itemContent').textContent = challenge.activityContent;

            // 显示媒体内容（如果有）
            if (challenge.mediaUrl) {
                showMediaContent(challenge);
            } else {
                document.getElementById('mediaContainer').innerHTML = '';
            }
        }

        function showMediaContent(item) {
            const mediaContainer = document.getElementById('mediaContainer');
            mediaContainer.innerHTML = '';

            if (item.mediaUrl && item.mediaType > 0) {
                let mediaElement = '';
                switch (item.mediaType) {
                    case 1: // 图片
                        mediaElement = `<div class="text-center mt-3">
                                         <img src="${item.mediaUrl}" alt="${item.title || item.activityTitle}" class="img-fluid rounded" style="max-width: 300px; max-height: 300px;">
                                       </div>`;
                        break;
                    case 2: // 音频
                        mediaElement = `<div class="text-center mt-3">
                                         <audio controls class="w-100" style="max-width: 400px;">
                                           <source src="${item.mediaUrl}" type="audio/mpeg">
                                           <source src="${item.mediaUrl}" type="audio/wav">
                                           <source src="${item.mediaUrl}" type="audio/ogg">
                                           您的浏览器不支持音频播放。
                                         </audio>
                                       </div>`;
                        break;
                    case 3: // 视频
                        mediaElement = `<div class="text-center mt-3">
                                         <video controls class="w-100 rounded" style="max-width: 400px; max-height: 300px;">
                                           <source src="${item.mediaUrl}" type="video/mp4">
                                           <source src="${item.mediaUrl}" type="video/webm">
                                           <source src="${item.mediaUrl}" type="video/ogg">
                                           您的浏览器不支持视频播放。
                                         </video>
                                       </div>`;
                        break;
                }
                if (mediaElement) {
                    mediaContainer.innerHTML = mediaElement;
                }
            }
        }
        


        function continueGame() {
            if (currentRoom) {
                // 房间游戏：继续房间游戏
                startRoomGame();
            } else if (currentGameSettings) {
                // 本地游戏：使用保存的设置继续游戏
                startGameWithSettings(currentGameSettings);
            } else {
                // 如果没有保存的设置，回到设置页面
                resetGame();
            }
        }

        function startGameWithSettings(settings) {
            // 显示进行中界面
            document.getElementById('gameSetup').style.display = 'none';
            document.getElementById('gameProgress').style.display = 'block';
            document.getElementById('gameResult').style.display = 'none';

            // 根据游戏模式显示不同的进行中界面
            if (settings.gameMode === 'slot_machine') {
                document.getElementById('punishmentRewardProgress').style.display = 'none';
                document.getElementById('slotMachineProgress').style.display = 'block';
                startSlotMachineAnimation();
            } else {
                document.getElementById('punishmentRewardProgress').style.display = 'block';
                document.getElementById('slotMachineProgress').style.display = 'none';
            }

            // 发送请求
            fetch('/game/play', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(settings)
            })
            .then(response => response.json())
            .then(data => {
                if (data.code === 0) {
                    // 延迟显示结果，增加悬念
                    setTimeout(() => {
                        // 如果是老虎机游戏，传递最终符号
                        if (data.data.gameMode === 'slot_machine' && data.data.slotResult) {
                            const finalSymbols = data.data.slotResult.symbols.map(s => s.emoji);
                            stopSlotMachineAnimation(finalSymbols);
                        } else {
                            stopSlotMachineAnimation();
                        }

                        // 延迟显示结果，让动画完成
                        setTimeout(() => {
                            showResult(data.data);
                        }, 2000);
                    }, 3000);
                } else {
                    alert('游戏出错：' + data.msg);
                    resetGame();
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('网络错误，请重试');
                resetGame();
            });
        }

        function startSlotMachineAnimation() {
            const reels = document.querySelectorAll('.slot-reel');
            const symbols = ['❤️', '💋', '🌹', '💍', '👫', '🍽️', '🎬', '💆', '🎁', '💃', '🎵', '⭐', '🌙', '🔥', '🍒'];

            reels.forEach((reel, index) => {
                reel.classList.add('spinning');
                reel.classList.remove('winning');

                // 在转动过程中随机更换符号，增加真实感
                const symbolElement = reel.querySelector('.slot-symbol');
                const changeSymbol = () => {
                    if (reel.classList.contains('spinning')) {
                        const randomSymbol = symbols[Math.floor(Math.random() * symbols.length)];
                        symbolElement.textContent = randomSymbol;
                        setTimeout(changeSymbol, 100 + Math.random() * 100);
                    }
                };

                // 延迟开始符号变换，让每个轮盘有不同的节奏
                setTimeout(changeSymbol, index * 200);
            });
        }

        function stopSlotMachineAnimation(finalSymbols) {
            const reels = document.querySelectorAll('.slot-reel');

            reels.forEach((reel, index) => {
                // 分别停止每个轮盘，增加真实感
                setTimeout(() => {
                    reel.classList.remove('spinning');

                    // 设置最终符号
                    if (finalSymbols && finalSymbols[index]) {
                        const symbolElement = reel.querySelector('.slot-symbol');
                        symbolElement.textContent = finalSymbols[index];
                    }

                    // 添加停止后的弹跳效果
                    setTimeout(() => {
                        reel.classList.add('winning');
                        setTimeout(() => {
                            reel.classList.remove('winning');
                        }, 1000);
                    }, 300);

                }, index * 500); // 每个轮盘延迟500ms停止
            });
        }

        function resetGame() {
            document.getElementById('gameSetup').style.display = 'block';
            document.getElementById('gameProgress').style.display = 'none';
            document.getElementById('gameResult').style.display = 'none';

            // 清空输入框
            const player1Input = document.getElementById('player1Name');
            const player2Input = document.getElementById('player2Name');
            const gameTypeSelect = document.getElementById('gameType');
            const gameLevelSelect = document.getElementById('gameLevel');

            if (player1Input) player1Input.value = '';
            if (player2Input) player2Input.value = '';
            if (gameTypeSelect) gameTypeSelect.value = '0';
            if (gameLevelSelect) gameLevelSelect.value = '0';

            // 清空保存的设置
            currentGameSettings = null;
            currentRoom = null;

            // 清理房间检查定时器
            if (roomCheckInterval) {
                clearInterval(roomCheckInterval);
                roomCheckInterval = null;
            }
        }

        // 显示本地游戏
        function showLocalGame(event) {
            document.getElementById('gameSetup').style.display = 'block';
            document.getElementById('roomGameSetup').style.display = 'none';

            // 更新按钮状态
            document.querySelectorAll('#modeSelection .btn').forEach(btn => btn.classList.remove('active'));
            if (event && event.target) {
                event.target.classList.add('active');
            } else {
                // 如果没有event，默认激活第一个按钮（本地游戏）
                const localBtn = document.querySelector('#modeSelection .btn:first-child');
                if (localBtn) localBtn.classList.add('active');
            }
        }

        // 显示房间游戏
        function showRoomGame(event) {
            document.getElementById('gameSetup').style.display = 'none';
            document.getElementById('roomGameSetup').style.display = 'block';

            // 更新按钮状态
            document.querySelectorAll('#modeSelection .btn').forEach(btn => btn.classList.remove('active'));
            if (event && event.target) {
                event.target.classList.add('active');
            } else {
                // 如果没有event，默认激活第二个按钮（房间游戏）
                const roomBtn = document.querySelector('#modeSelection .btn:last-child');
                if (roomBtn) roomBtn.classList.add('active');
            }
        }

        // 显示创建房间面板
        function showCreateRoom() {
            document.getElementById('createRoomPanel').style.display = 'block';
            document.getElementById('joinRoomPanel').style.display = 'none';
            document.getElementById('roomStatus').style.display = 'none';
        }

        // 显示加入房间面板
        function showJoinRoom() {
            document.getElementById('createRoomPanel').style.display = 'none';
            document.getElementById('joinRoomPanel').style.display = 'block';
            document.getElementById('roomStatus').style.display = 'none';
        }

        // 创建房间
        function createRoom() {
            const roomName = document.getElementById('roomName').value.trim();
            const creatorName = document.getElementById('roomCreatorName').value.trim();
            const gameMode = document.getElementById('roomGameMode').value;
            const gameType = parseInt(document.getElementById('roomGameType').value);
            const gameLevel = parseInt(document.getElementById('roomGameLevel').value);

            if (!creatorName) {
                alert('请输入您的姓名！');
                return;
            }

            // 构建请求数据
            let requestData = {
                name: roomName || '', // 房间名字，可选
                createdBy: creatorName,
                gameMode: gameMode
            };

            // 只有奖惩游戏需要类型和级别
            if (gameMode === 'punishment_reward') {
                requestData.type = gameType;
                requestData.level = gameLevel;
            }

            fetch('/room/create', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(requestData)
            })
            .then(response => response.json())
            .then(data => {
                if (data.code === 0) {
                    currentRoom = data.data;
                    showRoomStatus();
                    startRoomCheck();
                } else {
                    alert('创建房间失败：' + data.msg);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('网络错误，请重试');
            });
        }

        // 加入房间
        function joinRoom() {
            const playerName = document.getElementById('joinPlayerName').value.trim();
            const roomId = document.getElementById('joinRoomId').value.trim();

            if (!playerName || !roomId) {
                alert('请输入姓名和房间ID！');
                return;
            }

            fetch('/room/join', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    roomId: roomId,
                    playerName: playerName
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.code === 0) {
                    currentRoom = data.data;
                    showRoomStatus();
                    startRoomCheck();
                } else {
                    alert('加入房间失败：' + data.msg);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('网络错误，请重试');
            });
        }

        // 显示房间状态
        function showRoomStatus() {
            if (!currentRoom) return;

            document.getElementById('createRoomPanel').style.display = 'none';
            document.getElementById('joinRoomPanel').style.display = 'none';
            document.getElementById('roomStatus').style.display = 'block';

            const roomTitle = currentRoom.name ? `${currentRoom.name} (${currentRoom.id})` : `房间 ${currentRoom.id}`;
            document.getElementById('roomTitle').textContent = roomTitle;
            document.getElementById('roomInfo').innerHTML = `
                创建者: ${currentRoom.createdBy}<br>
                游戏类型: ${['随机', '奖励', '惩罚'][currentRoom.settings.type]}<br>
                游戏级别: ${['随机', '轻微', '一般', '严重'][currentRoom.settings.level]}
            `;

            const playersHtml = currentRoom.players.map(player =>
                `<span class="badge bg-primary me-2">${player}</span>`
            ).join('');
            document.getElementById('roomPlayers').innerHTML = `
                <strong>玩家 (${currentRoom.players.length}/2):</strong><br>
                ${playersHtml}
            `;

            const actionsDiv = document.getElementById('roomActions');
            if (currentRoom.status === 'waiting') {
                if (currentRoom.players.length === 2) {
                    actionsDiv.innerHTML = `
                        <button class="btn btn-success" onclick="startRoomGame()">开始游戏</button>
                        <button class="btn btn-outline-secondary" onclick="copyRoomId()">复制房间ID</button>
                    `;
                } else {
                    actionsDiv.innerHTML = `
                        <p class="text-muted">等待另一位玩家加入...</p>
                        <button class="btn btn-outline-secondary" onclick="copyRoomId()">复制房间ID</button>
                    `;
                }
            } else if (currentRoom.status === 'playing' && currentRoom.currentGame) {
                showResult(currentRoom.currentGame);
            }
        }

        // 复制房间ID
        function copyRoomId() {
            if (currentRoom) {
                navigator.clipboard.writeText(currentRoom.id).then(() => {
                    alert('房间ID已复制到剪贴板！');
                }).catch(() => {
                    prompt('房间ID（请手动复制）:', currentRoom.id);
                });
            }
        }

        // 开始房间游戏
        function startRoomGame() {
            if (!currentRoom) return;

            fetch(`/room/${currentRoom.id}/start`, {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                if (data.code === 0) {
                    // 显示进行中界面
                    document.getElementById('roomGameSetup').style.display = 'none';
                    document.getElementById('gameProgress').style.display = 'block';
                    document.getElementById('gameResult').style.display = 'none';

                    // 延迟显示结果
                    setTimeout(() => {
                        showResult(data.data);
                    }, 3000);
                } else {
                    alert('开始游戏失败：' + data.msg);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('网络错误，请重试');
            });
        }

        // 开始房间状态检查
        function startRoomCheck() {
            if (roomCheckInterval) {
                clearInterval(roomCheckInterval);
            }

            roomCheckInterval = setInterval(() => {
                if (currentRoom) {
                    fetch(`/room/${currentRoom.id}`)
                        .then(response => response.json())
                        .then(data => {
                            if (data.code === 0) {
                                currentRoom = data.data;
                                showRoomStatus();
                            }
                        })
                        .catch(error => {
                            console.error('Room check error:', error);
                        });
                }
            }, 2000); // 每2秒检查一次
        }

        // 房间游戏模式改变时的处理
        function handleRoomGameModeChange() {
            const gameMode = document.getElementById('roomGameMode').value;
            const punishmentSettings = document.getElementById('roomPunishmentSettings');

            if (gameMode === 'punishment_reward') {
                punishmentSettings.style.display = 'block';
            } else {
                punishmentSettings.style.display = 'none';
            }
        }

        // 页面加载时默认显示本地游戏和奖惩游戏模式
        document.addEventListener('DOMContentLoaded', function() {
            selectGameType('punishment_reward');
            showLocalGame();

            // 绑定房间游戏模式改变事件
            const roomGameModeSelect = document.getElementById('roomGameMode');
            if (roomGameModeSelect) {
                roomGameModeSelect.addEventListener('change', handleRoomGameModeChange);
                handleRoomGameModeChange(); // 初始化显示
            }
        });
    </script>
</body>
</html>