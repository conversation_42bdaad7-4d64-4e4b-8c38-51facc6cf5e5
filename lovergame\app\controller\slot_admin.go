package controller

import (
	"fmt"
	"lovergame/app/model"
	"lovergame/app/service"
	"strconv"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/ghttp"
	"github.com/gogf/gf/v2/util/gconv"
)

// SlotSymbolList 老虎机符号列表
func (c *AdminController) SlotSymbolList(r *ghttp.Request) {
	if !c.checkAuth(r) {
		if r.Method == "GET" {
			r.Response.RedirectTo("/admin/login")
		} else {
			r.Response.WriteJsonExit(g.Map{
				"code": 401,
				"msg":  "未授权访问",
			})
		}
		return
	}

	if r.Method == "GET" {
		// 暂时直接返回JSON数据，不使用模板
		page := gconv.Int(r.Get("page", 1))
		limit := gconv.Int(r.Get("limit", 100))

		list, total, err := service.SlotMachine().GetSymbolList(r.Context(), page, limit)
		if err != nil {
			r.Response.WriteJsonExit(g.Map{
				"code": 500,
				"msg":  err.Error(),
			})
		}

		r.Response.WriteJsonExit(g.Map{
			"code":  0,
			"msg":   "success",
			"count": total,
			"data":  list,
		})
		return
	}

	// AJAX请求获取数据
	page := gconv.Int(r.Get("page", 1))
	limit := gconv.Int(r.Get("limit", 10))

	list, total, err := service.SlotMachine().GetSymbolList(r.Context(), page, limit)
	if err != nil {
		r.Response.WriteJsonExit(g.Map{
			"code": 500,
			"msg":  err.Error(),
		})
	}

	r.Response.WriteJsonExit(g.Map{
		"code":  0,
		"msg":   "success",
		"count": total,
		"data":  list,
	})
}

// SlotSymbolAdd 添加老虎机符号
func (c *AdminController) SlotSymbolAdd(r *ghttp.Request) {
	if !c.checkAuth(r) {
		if r.Method == "GET" {
			r.Response.RedirectTo("/admin/login")
		} else {
			r.Response.WriteJsonExit(g.Map{
				"code": 401,
				"msg":  "未授权访问",
			})
		}
		return
	}

	if r.Method == "GET" {
		// 返回空的符号数据结构供前端使用
		r.Response.WriteJsonExit(g.Map{
			"code": 0,
			"msg":  "success",
			"data": g.Map{
				"id":          0,
				"name":        "",
				"displayName": "",
				"emoji":       "",
				"color":       "#FF6B6B",
				"rarity":      1,
				"isActive":    1,
			},
		})
		return
	}

	var req *model.SlotSymbol
	if err := r.Parse(&req); err != nil {
		r.Response.WriteJsonExit(g.Map{
			"code": 400,
			"msg":  err.Error(),
		})
	}

	if err := service.SlotMachine().CreateSymbol(r.Context(), req); err != nil {
		r.Response.WriteJsonExit(g.Map{
			"code": 500,
			"msg":  err.Error(),
		})
	}

	r.Response.WriteJsonExit(g.Map{
		"code": 0,
		"msg":  "添加成功",
	})
}

// SlotSymbolEdit 编辑老虎机符号
func (c *AdminController) SlotSymbolEdit(r *ghttp.Request) {
	if !c.checkAuth(r) {
		if r.Method == "GET" {
			r.Response.RedirectTo("/admin/login")
		} else {
			r.Response.WriteJsonExit(g.Map{
				"code": 401,
				"msg":  "未授权访问",
			})
		}
		return
	}

	id := gconv.Int(r.Get("id"))
	if id <= 0 {
		r.Response.WriteJsonExit(g.Map{
			"code": 400,
			"msg":  "参数错误",
		})
	}

	if r.Method == "GET" {
		symbol, err := service.SlotMachine().GetSymbolById(r.Context(), id)
		if err != nil {
			r.Response.WriteJsonExit(g.Map{
				"code": 500,
				"msg":  "获取符号失败: " + err.Error(),
			})
			return
		}

		r.Response.WriteJsonExit(g.Map{
			"code": 0,
			"msg":  "success",
			"data": symbol,
		})
		return
	}

	var req *model.SlotSymbol
	if err := r.Parse(&req); err != nil {
		r.Response.WriteJsonExit(g.Map{
			"code": 400,
			"msg":  err.Error(),
		})
	}

	if err := service.SlotMachine().UpdateSymbol(r.Context(), id, req); err != nil {
		r.Response.WriteJsonExit(g.Map{
			"code": 500,
			"msg":  err.Error(),
		})
	}

	r.Response.WriteJsonExit(g.Map{
		"code": 0,
		"msg":  "更新成功",
	})
}

// SlotSymbolDelete 删除老虎机符号
func (c *AdminController) SlotSymbolDelete(r *ghttp.Request) {
	if !c.checkAuth(r) {
		r.Response.WriteJsonExit(g.Map{
			"code": 401,
			"msg":  "未授权访问",
		})
		return
	}

	id := gconv.Int(r.Get("id"))
	if id <= 0 {
		r.Response.WriteJsonExit(g.Map{
			"code": 400,
			"msg":  "参数错误",
		})
	}

	if err := service.SlotMachine().DeleteSymbol(r.Context(), id); err != nil {
		r.Response.WriteJsonExit(g.Map{
			"code": 500,
			"msg":  err.Error(),
		})
	}

	r.Response.WriteJsonExit(g.Map{
		"code": 0,
		"msg":  "删除成功",
	})
}

// SlotCombinationList 老虎机组合列表
func (c *AdminController) SlotCombinationList(r *ghttp.Request) {
	if !c.checkAuth(r) {
		if r.Method == "GET" {
			r.Response.RedirectTo("/admin/login")
		} else {
			r.Response.WriteJsonExit(g.Map{
				"code": 401,
				"msg":  "未授权访问",
			})
		}
		return
	}

	if r.Method == "GET" {
		// 暂时直接返回JSON数据，不使用模板
		page := gconv.Int(r.Get("page", 1))
		limit := gconv.Int(r.Get("limit", 100))

		list, total, err := service.SlotMachine().GetCombinationList(r.Context(), page, limit)
		if err != nil {
			r.Response.WriteJsonExit(g.Map{
				"code": 500,
				"msg":  err.Error(),
			})
		}

		r.Response.WriteJsonExit(g.Map{
			"code":  0,
			"msg":   "success",
			"count": total,
			"data":  list,
		})
		return
	}

	// AJAX请求获取数据
	page := gconv.Int(r.Get("page", 1))
	limit := gconv.Int(r.Get("limit", 10))

	list, total, err := service.SlotMachine().GetCombinationList(r.Context(), page, limit)
	if err != nil {
		r.Response.WriteJsonExit(g.Map{
			"code": 500,
			"msg":  err.Error(),
		})
	}

	r.Response.WriteJsonExit(g.Map{
		"code":  0,
		"msg":   "success",
		"count": total,
		"data":  list,
	})
}

// SlotCombinationAdd 添加老虎机组合
func (c *AdminController) SlotCombinationAdd(r *ghttp.Request) {
	if !c.checkAuth(r) {
		if r.Method == "GET" {
			r.Response.RedirectTo("/admin/login")
		} else {
			r.Response.WriteJsonExit(g.Map{
				"code": 401,
				"msg":  "未授权访问",
			})
		}
		return
	}

	if r.Method == "GET" {
		// 获取所有符号供选择
		symbols, _, err := service.SlotMachine().GetSymbolList(r.Context(), 1, 100)
		if err != nil {
			r.Response.WriteJsonExit(g.Map{
				"code": 500,
				"msg":  "获取符号失败: " + err.Error(),
			})
			return
		}

		r.Response.WriteJsonExit(g.Map{
			"code": 0,
			"msg":  "success",
			"data": g.Map{
				"symbols": symbols,
				"combination": g.Map{
					"id":              0,
					"name":            "",
					"symbol1Id":       1,
					"symbol2Id":       1,
					"symbol3Id":       1,
					"activityTitle":   "",
					"activityContent": "",
					"activityType":    1,
					"intensityLevel":  1,
					"mediaType":       0,
					"mediaUrl":        "",
					"isActive":        1,
				},
			},
		})
		return
	}

	var req *model.SlotCombination
	if err := r.Parse(&req); err != nil {
		r.Response.WriteJsonExit(g.Map{
			"code": 400,
			"msg":  err.Error(),
		})
	}

	if err := service.SlotMachine().CreateCombination(r.Context(), req); err != nil {
		r.Response.WriteJsonExit(g.Map{
			"code": 500,
			"msg":  err.Error(),
		})
	}

	r.Response.WriteJsonExit(g.Map{
		"code": 0,
		"msg":  "添加成功",
	})
}

// SlotCombinationEdit 编辑老虎机组合
func (c *AdminController) SlotCombinationEdit(r *ghttp.Request) {
	if !c.checkAuth(r) {
		if r.Method == "GET" {
			r.Response.RedirectTo("/admin/login")
		} else {
			r.Response.WriteJsonExit(g.Map{
				"code": 401,
				"msg":  "未授权访问",
			})
		}
		return
	}

	id := gconv.Int(r.Get("id"))
	if id <= 0 {
		r.Response.WriteJsonExit(g.Map{
			"code": 400,
			"msg":  "参数错误",
		})
	}

	if r.Method == "GET" {
		combination, err := service.SlotMachine().GetCombinationById(r.Context(), id)
		if err != nil {
			r.Response.WriteJsonExit(g.Map{
				"code": 500,
				"msg":  "获取组合失败: " + err.Error(),
			})
			return
		}

		// 获取所有符号供选择
		symbols, _, err := service.SlotMachine().GetSymbolList(r.Context(), 1, 100)
		if err != nil {
			r.Response.WriteJsonExit(g.Map{
				"code": 500,
				"msg":  "获取符号失败: " + err.Error(),
			})
			return
		}

		r.Response.WriteJsonExit(g.Map{
			"code": 0,
			"msg":  "success",
			"data": g.Map{
				"combination": combination,
				"symbols":     symbols,
			},
		})
		return
	}

	var req *model.SlotCombination
	if err := r.Parse(&req); err != nil {
		r.Response.WriteJsonExit(g.Map{
			"code": 400,
			"msg":  err.Error(),
		})
	}

	if err := service.SlotMachine().UpdateCombination(r.Context(), id, req); err != nil {
		r.Response.WriteJsonExit(g.Map{
			"code": 500,
			"msg":  err.Error(),
		})
	}

	r.Response.WriteJsonExit(g.Map{
		"code": 0,
		"msg":  "更新成功",
	})
}

// SlotCombinationDelete 删除老虎机组合
func (c *AdminController) SlotCombinationDelete(r *ghttp.Request) {
	if !c.checkAuth(r) {
		r.Response.WriteJsonExit(g.Map{
			"code": 401,
			"msg":  "未授权访问",
		})
		return
	}

	idStr := r.Get("id").String()
	id, err := strconv.Atoi(idStr)
	if err != nil || id <= 0 {
		r.Response.WriteJsonExit(g.Map{
			"code": 400,
			"msg":  "参数错误",
		})
		return
	}

	if err := service.SlotMachine().DeleteCombination(r.Context(), id); err != nil {
		r.Response.WriteJsonExit(g.Map{
			"code": 500,
			"msg":  err.Error(),
		})
		return
	}

	r.Response.WriteJsonExit(g.Map{
		"code": 0,
		"msg":  "删除成功",
	})
}

// TruthDareQuestionList 真心话大冒险问题列表
func (c *AdminController) TruthDareQuestionList(r *ghttp.Request) {
	if !c.checkAuth(r) {
		if r.Method == "GET" {
			r.Response.RedirectTo("/admin/login")
		} else {
			r.Response.WriteJsonExit(g.Map{
				"code": 401,
				"msg":  "未授权访问",
			})
		}
		return
	}

	page := gconv.Int(r.Get("page", 1))
	limit := gconv.Int(r.Get("limit", 10))

	list, total, err := service.TruthDare().GetQuestionList(r.Context(), page, limit)
	if err != nil {
		r.Response.WriteJsonExit(g.Map{
			"code": 500,
			"msg":  err.Error(),
		})
		return
	}

	r.Response.WriteJsonExit(g.Map{
		"code": 0,
		"msg":  "success",
		"data": g.Map{
			"list":  list,
			"total": total,
			"page":  page,
			"limit": limit,
		},
	})
}

// TruthDareQuestionAdd 添加真心话大冒险问题
func (c *AdminController) TruthDareQuestionAdd(r *ghttp.Request) {
	if !c.checkAuth(r) {
		if r.Method == "GET" {
			r.Response.RedirectTo("/admin/login")
		} else {
			r.Response.WriteJsonExit(g.Map{
				"code": 401,
				"msg":  "未授权访问",
			})
		}
		return
	}

	if r.Method == "GET" {
		r.Response.WriteJsonExit(g.Map{
			"code": 0,
			"msg":  "success",
			"data": g.Map{
				"question": g.Map{
					"id":         0,
					"type":       1,
					"title":      "",
					"content":    "",
					"difficulty": 1,
					"category":   "general",
					"mediaType":  0,
					"mediaUrl":   "",
					"isActive":   1,
				},
			},
		})
		return
	}

	var req *model.TruthDareQuestion
	if err := r.Parse(&req); err != nil {
		r.Response.WriteJsonExit(g.Map{
			"code": 400,
			"msg":  err.Error(),
		})
		return
	}

	if err := service.TruthDare().AddQuestion(r.Context(), req); err != nil {
		r.Response.WriteJsonExit(g.Map{
			"code": 500,
			"msg":  err.Error(),
		})
		return
	}

	r.Response.WriteJsonExit(g.Map{
		"code": 0,
		"msg":  "添加成功",
	})
}

// TruthDareQuestionEdit 编辑真心话大冒险问题
func (c *AdminController) TruthDareQuestionEdit(r *ghttp.Request) {
	if !c.checkAuth(r) {
		if r.Method == "GET" {
			r.Response.RedirectTo("/admin/login")
		} else {
			r.Response.WriteJsonExit(g.Map{
				"code": 401,
				"msg":  "未授权访问",
			})
		}
		return
	}

	id := gconv.Int(r.Get("id"))
	if id <= 0 {
		r.Response.WriteJsonExit(g.Map{
			"code": 400,
			"msg":  "参数错误",
		})
		return
	}

	if r.Method == "GET" {
		question, err := service.TruthDare().GetQuestionById(r.Context(), id)
		if err != nil {
			r.Response.WriteJsonExit(g.Map{
				"code": 500,
				"msg":  "获取问题失败: " + err.Error(),
			})
			return
		}

		r.Response.WriteJsonExit(g.Map{
			"code": 0,
			"msg":  "success",
			"data": g.Map{
				"question": question,
			},
		})
		return
	}

	var req *model.TruthDareQuestion
	if err := r.Parse(&req); err != nil {
		r.Response.WriteJsonExit(g.Map{
			"code": 400,
			"msg":  err.Error(),
		})
		return
	}

	if err := service.TruthDare().UpdateQuestion(r.Context(), id, req); err != nil {
		r.Response.WriteJsonExit(g.Map{
			"code": 500,
			"msg":  err.Error(),
		})
		return
	}

	r.Response.WriteJsonExit(g.Map{
		"code": 0,
		"msg":  "更新成功",
	})
}

// TruthDareQuestionDelete 删除真心话大冒险问题
func (c *AdminController) TruthDareQuestionDelete(r *ghttp.Request) {
	if !c.checkAuth(r) {
		r.Response.WriteJsonExit(g.Map{
			"code": 401,
			"msg":  "未授权访问",
		})
		return
	}

	idStr := r.Get("id").String()
	id, err := strconv.Atoi(idStr)
	if err != nil || id <= 0 {
		r.Response.WriteJsonExit(g.Map{
			"code": 400,
			"msg":  "参数错误",
		})
		return
	}

	if err := service.TruthDare().DeleteQuestion(r.Context(), id); err != nil {
		r.Response.WriteJsonExit(g.Map{
			"code": 500,
			"msg":  err.Error(),
		})
		return
	}

	r.Response.WriteJsonExit(g.Map{
		"code": 0,
		"msg":  "删除成功",
	})
}

// DiceChallengeList 骰子挑战列表
func (c *AdminController) DiceChallengeList(r *ghttp.Request) {
	if !c.checkAuth(r) {
		if r.Method == "GET" {
			r.Response.RedirectTo("/admin/login")
		} else {
			r.Response.WriteJsonExit(g.Map{
				"code": 401,
				"msg":  "未授权访问",
			})
		}
		return
	}

	page := gconv.Int(r.Get("page", 1))
	limit := gconv.Int(r.Get("limit", 10))

	list, total, err := service.DiceChallenge().GetChallengeList(r.Context(), page, limit)
	if err != nil {
		r.Response.WriteJsonExit(g.Map{
			"code": 500,
			"msg":  err.Error(),
		})
		return
	}

	r.Response.WriteJsonExit(g.Map{
		"code": 0,
		"msg":  "success",
		"data": g.Map{
			"list":  list,
			"total": total,
			"page":  page,
			"limit": limit,
		},
	})
}

// DiceChallengeAdd 添加骰子挑战
func (c *AdminController) DiceChallengeAdd(r *ghttp.Request) {
	if !c.checkAuth(r) {
		if r.Method == "GET" {
			r.Response.RedirectTo("/admin/login")
		} else {
			r.Response.WriteJsonExit(g.Map{
				"code": 401,
				"msg":  "未授权访问",
			})
		}
		return
	}

	if r.Method == "GET" {
		r.Response.WriteJsonExit(g.Map{
			"code": 0,
			"msg":  "success",
			"data": g.Map{
				"challenge": g.Map{
					"id":              0,
					"name":            "",
					"description":     "",
					"diceCombination": "",
					"activityTitle":   "",
					"activityContent": "",
					"difficulty":      1,
					"durationMinutes": 10,
					"requiresProps":   0,
					"propsNeeded":     "",
					"mediaType":       0,
					"mediaUrl":        "",
					"isActive":        1,
				},
			},
		})
		return
	}

	var req *model.DiceChallenge
	if err := r.Parse(&req); err != nil {
		r.Response.WriteJsonExit(g.Map{
			"code": 400,
			"msg":  err.Error(),
		})
		return
	}

	if err := service.DiceChallenge().AddChallenge(r.Context(), req); err != nil {
		r.Response.WriteJsonExit(g.Map{
			"code": 500,
			"msg":  err.Error(),
		})
		return
	}

	r.Response.WriteJsonExit(g.Map{
		"code": 0,
		"msg":  "添加成功",
	})
}

// DiceChallengeEdit 编辑骰子挑战
func (c *AdminController) DiceChallengeEdit(r *ghttp.Request) {
	if !c.checkAuth(r) {
		if r.Method == "GET" {
			r.Response.RedirectTo("/admin/login")
		} else {
			r.Response.WriteJsonExit(g.Map{
				"code": 401,
				"msg":  "未授权访问",
			})
		}
		return
	}

	id := gconv.Int(r.Get("id"))
	if id <= 0 {
		r.Response.WriteJsonExit(g.Map{
			"code": 400,
			"msg":  "参数错误",
		})
		return
	}

	if r.Method == "GET" {
		challenge, err := service.DiceChallenge().GetChallengeById(r.Context(), id)
		if err != nil {
			r.Response.WriteJsonExit(g.Map{
				"code": 500,
				"msg":  "获取挑战失败: " + err.Error(),
			})
			return
		}

		r.Response.WriteJsonExit(g.Map{
			"code": 0,
			"msg":  "success",
			"data": g.Map{
				"challenge": challenge,
			},
		})
		return
	}

	var req *model.DiceChallenge
	if err := r.Parse(&req); err != nil {
		r.Response.WriteJsonExit(g.Map{
			"code": 400,
			"msg":  err.Error(),
		})
		return
	}

	if err := service.DiceChallenge().UpdateChallenge(r.Context(), id, req); err != nil {
		r.Response.WriteJsonExit(g.Map{
			"code": 500,
			"msg":  err.Error(),
		})
		return
	}

	r.Response.WriteJsonExit(g.Map{
		"code": 0,
		"msg":  "更新成功",
	})
}

// DiceChallengeDelete 删除骰子挑战
func (c *AdminController) DiceChallengeDelete(r *ghttp.Request) {
	if !c.checkAuth(r) {
		r.Response.WriteJsonExit(g.Map{
			"code": 401,
			"msg":  "未授权访问",
		})
		return
	}

	idStr := r.Get("id").String()
	id, err := strconv.Atoi(idStr)
	if err != nil || id <= 0 {
		r.Response.WriteJsonExit(g.Map{
			"code": 400,
			"msg":  "参数错误",
		})
		return
	}

	if err := service.DiceChallenge().DeleteChallenge(r.Context(), id); err != nil {
		r.Response.WriteJsonExit(g.Map{
			"code": 500,
			"msg":  err.Error(),
		})
		return
	}

	r.Response.WriteJsonExit(g.Map{
		"code": 0,
		"msg":  "删除成功",
	})
}

// GameSettingsList 游戏设置列表
func (c *AdminController) GameSettingsList(r *ghttp.Request) {
	if !c.checkAuth(r) {
		if r.Method == "GET" {
			r.Response.RedirectTo("/admin/login")
		} else {
			r.Response.WriteJsonExit(g.Map{
				"code": 401,
				"msg":  "未授权访问",
			})
		}
		return
	}

	settings, err := service.GameSettings().GetAllSettings(r.Context())
	if err != nil {
		r.Response.WriteJsonExit(g.Map{
			"code": 500,
			"msg":  err.Error(),
		})
		return
	}

	r.Response.WriteJsonExit(g.Map{
		"code": 0,
		"msg":  "success",
		"data": g.Map{
			"settings": settings,
		},
	})
}

// GameSettingsUpdate 更新游戏设置
func (c *AdminController) GameSettingsUpdate(r *ghttp.Request) {
	if !c.checkAuth(r) {
		r.Response.WriteJsonExit(g.Map{
			"code": 401,
			"msg":  "未授权访问",
		})
		return
	}

	var req map[string]string
	if err := r.Parse(&req); err != nil {
		r.Response.WriteJsonExit(g.Map{
			"code": 400,
			"msg":  err.Error(),
		})
		return
	}

	// 批量更新设置
	for key, value := range req {
		if err := service.GameSettings().UpdateSetting(r.Context(), key, value); err != nil {
			r.Response.WriteJsonExit(g.Map{
				"code": 500,
				"msg":  fmt.Sprintf("更新设置 %s 失败: %s", key, err.Error()),
			})
			return
		}
	}

	r.Response.WriteJsonExit(g.Map{
		"code": 0,
		"msg":  "设置更新成功",
	})
}
