package controller

import (
	"lovergame/app/model"
	"lovergame/app/service"
	"strconv"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/ghttp"
	"github.com/gogf/gf/v2/util/gconv"
)

// SlotSymbolList 老虎机符号列表
func (c *AdminController) SlotSymbolList(r *ghttp.Request) {
	if !c.checkAuth(r) {
		if r.Method == "GET" {
			r.Response.RedirectTo("/admin/login")
		} else {
			r.Response.WriteJsonExit(g.Map{
				"code": 401,
				"msg":  "未授权访问",
			})
		}
		return
	}

	if r.Method == "GET" {
		// 暂时直接返回JSON数据，不使用模板
		page := gconv.Int(r.Get("page", 1))
		limit := gconv.Int(r.Get("limit", 100))

		list, total, err := service.SlotMachine().GetSymbolList(r.Context(), page, limit)
		if err != nil {
			r.Response.WriteJsonExit(g.Map{
				"code": 500,
				"msg":  err.Error(),
			})
		}

		r.Response.WriteJsonExit(g.Map{
			"code":  0,
			"msg":   "success",
			"count": total,
			"data":  list,
		})
		return
	}

	// AJAX请求获取数据
	page := gconv.Int(r.Get("page", 1))
	limit := gconv.Int(r.Get("limit", 10))

	list, total, err := service.SlotMachine().GetSymbolList(r.Context(), page, limit)
	if err != nil {
		r.Response.WriteJsonExit(g.Map{
			"code": 500,
			"msg":  err.Error(),
		})
	}

	r.Response.WriteJsonExit(g.Map{
		"code":  0,
		"msg":   "success",
		"count": total,
		"data":  list,
	})
}

// SlotSymbolAdd 添加老虎机符号
func (c *AdminController) SlotSymbolAdd(r *ghttp.Request) {
	if !c.checkAuth(r) {
		if r.Method == "GET" {
			r.Response.RedirectTo("/admin/login")
		} else {
			r.Response.WriteJsonExit(g.Map{
				"code": 401,
				"msg":  "未授权访问",
			})
		}
		return
	}

	if r.Method == "GET" {
		r.Response.WriteTpl("admin/slot_symbol_add.html", nil)
		return
	}

	var req *model.SlotSymbol
	if err := r.Parse(&req); err != nil {
		r.Response.WriteJsonExit(g.Map{
			"code": 400,
			"msg":  err.Error(),
		})
	}

	if err := service.SlotMachine().CreateSymbol(r.Context(), req); err != nil {
		r.Response.WriteJsonExit(g.Map{
			"code": 500,
			"msg":  err.Error(),
		})
	}

	r.Response.WriteJsonExit(g.Map{
		"code": 0,
		"msg":  "添加成功",
	})
}

// SlotSymbolEdit 编辑老虎机符号
func (c *AdminController) SlotSymbolEdit(r *ghttp.Request) {
	if !c.checkAuth(r) {
		if r.Method == "GET" {
			r.Response.RedirectTo("/admin/login")
		} else {
			r.Response.WriteJsonExit(g.Map{
				"code": 401,
				"msg":  "未授权访问",
			})
		}
		return
	}

	id := gconv.Int(r.Get("id"))
	if id <= 0 {
		r.Response.WriteJsonExit(g.Map{
			"code": 400,
			"msg":  "参数错误",
		})
	}

	if r.Method == "GET" {
		symbol, err := service.SlotMachine().GetSymbolById(r.Context(), id)
		if err != nil {
			r.Response.WriteTpl("admin/error.html", g.Map{
				"error": err.Error(),
			})
			return
		}

		r.Response.WriteTpl("admin/slot_symbol_edit.html", g.Map{
			"symbol": symbol,
		})
		return
	}

	var req *model.SlotSymbol
	if err := r.Parse(&req); err != nil {
		r.Response.WriteJsonExit(g.Map{
			"code": 400,
			"msg":  err.Error(),
		})
	}

	if err := service.SlotMachine().UpdateSymbol(r.Context(), id, req); err != nil {
		r.Response.WriteJsonExit(g.Map{
			"code": 500,
			"msg":  err.Error(),
		})
	}

	r.Response.WriteJsonExit(g.Map{
		"code": 0,
		"msg":  "更新成功",
	})
}

// SlotSymbolDelete 删除老虎机符号
func (c *AdminController) SlotSymbolDelete(r *ghttp.Request) {
	if !c.checkAuth(r) {
		r.Response.WriteJsonExit(g.Map{
			"code": 401,
			"msg":  "未授权访问",
		})
		return
	}

	id := gconv.Int(r.Get("id"))
	if id <= 0 {
		r.Response.WriteJsonExit(g.Map{
			"code": 400,
			"msg":  "参数错误",
		})
	}

	if err := service.SlotMachine().DeleteSymbol(r.Context(), id); err != nil {
		r.Response.WriteJsonExit(g.Map{
			"code": 500,
			"msg":  err.Error(),
		})
	}

	r.Response.WriteJsonExit(g.Map{
		"code": 0,
		"msg":  "删除成功",
	})
}

// SlotCombinationList 老虎机组合列表
func (c *AdminController) SlotCombinationList(r *ghttp.Request) {
	if !c.checkAuth(r) {
		if r.Method == "GET" {
			r.Response.RedirectTo("/admin/login")
		} else {
			r.Response.WriteJsonExit(g.Map{
				"code": 401,
				"msg":  "未授权访问",
			})
		}
		return
	}

	if r.Method == "GET" {
		// 暂时直接返回JSON数据，不使用模板
		page := gconv.Int(r.Get("page", 1))
		limit := gconv.Int(r.Get("limit", 100))

		list, total, err := service.SlotMachine().GetCombinationList(r.Context(), page, limit)
		if err != nil {
			r.Response.WriteJsonExit(g.Map{
				"code": 500,
				"msg":  err.Error(),
			})
		}

		r.Response.WriteJsonExit(g.Map{
			"code":  0,
			"msg":   "success",
			"count": total,
			"data":  list,
		})
		return
	}

	// AJAX请求获取数据
	page := gconv.Int(r.Get("page", 1))
	limit := gconv.Int(r.Get("limit", 10))

	list, total, err := service.SlotMachine().GetCombinationList(r.Context(), page, limit)
	if err != nil {
		r.Response.WriteJsonExit(g.Map{
			"code": 500,
			"msg":  err.Error(),
		})
	}

	r.Response.WriteJsonExit(g.Map{
		"code":  0,
		"msg":   "success",
		"count": total,
		"data":  list,
	})
}

// SlotCombinationAdd 添加老虎机组合
func (c *AdminController) SlotCombinationAdd(r *ghttp.Request) {
	if !c.checkAuth(r) {
		if r.Method == "GET" {
			r.Response.RedirectTo("/admin/login")
		} else {
			r.Response.WriteJsonExit(g.Map{
				"code": 401,
				"msg":  "未授权访问",
			})
		}
		return
	}

	if r.Method == "GET" {
		// 获取所有符号供选择
		symbols, _, err := service.SlotMachine().GetSymbolList(r.Context(), 1, 100)
		if err != nil {
			r.Response.WriteTpl("admin/error.html", g.Map{
				"error": err.Error(),
			})
			return
		}

		r.Response.WriteTpl("admin/slot_combination_add.html", g.Map{
			"symbols": symbols,
		})
		return
	}

	var req *model.SlotCombination
	if err := r.Parse(&req); err != nil {
		r.Response.WriteJsonExit(g.Map{
			"code": 400,
			"msg":  err.Error(),
		})
	}

	if err := service.SlotMachine().CreateCombination(r.Context(), req); err != nil {
		r.Response.WriteJsonExit(g.Map{
			"code": 500,
			"msg":  err.Error(),
		})
	}

	r.Response.WriteJsonExit(g.Map{
		"code": 0,
		"msg":  "添加成功",
	})
}

// SlotCombinationEdit 编辑老虎机组合
func (c *AdminController) SlotCombinationEdit(r *ghttp.Request) {
	if !c.checkAuth(r) {
		if r.Method == "GET" {
			r.Response.RedirectTo("/admin/login")
		} else {
			r.Response.WriteJsonExit(g.Map{
				"code": 401,
				"msg":  "未授权访问",
			})
		}
		return
	}

	id := gconv.Int(r.Get("id"))
	if id <= 0 {
		r.Response.WriteJsonExit(g.Map{
			"code": 400,
			"msg":  "参数错误",
		})
	}

	if r.Method == "GET" {
		combination, err := service.SlotMachine().GetCombinationById(r.Context(), id)
		if err != nil {
			r.Response.WriteTpl("admin/error.html", g.Map{
				"error": err.Error(),
			})
			return
		}

		// 获取所有符号供选择
		symbols, _, err := service.SlotMachine().GetSymbolList(r.Context(), 1, 100)
		if err != nil {
			r.Response.WriteTpl("admin/error.html", g.Map{
				"error": err.Error(),
			})
			return
		}

		r.Response.WriteTpl("admin/slot_combination_edit.html", g.Map{
			"combination": combination,
			"symbols":     symbols,
		})
		return
	}

	var req *model.SlotCombination
	if err := r.Parse(&req); err != nil {
		r.Response.WriteJsonExit(g.Map{
			"code": 400,
			"msg":  err.Error(),
		})
	}

	if err := service.SlotMachine().UpdateCombination(r.Context(), id, req); err != nil {
		r.Response.WriteJsonExit(g.Map{
			"code": 500,
			"msg":  err.Error(),
		})
	}

	r.Response.WriteJsonExit(g.Map{
		"code": 0,
		"msg":  "更新成功",
	})
}

// SlotCombinationDelete 删除老虎机组合
func (c *AdminController) SlotCombinationDelete(r *ghttp.Request) {
	if !c.checkAuth(r) {
		r.Response.WriteJsonExit(g.Map{
			"code": 401,
			"msg":  "未授权访问",
		})
		return
	}

	idStr := r.Get("id").String()
	id, err := strconv.Atoi(idStr)
	if err != nil || id <= 0 {
		r.Response.WriteJsonExit(g.Map{
			"code": 400,
			"msg":  "参数错误",
		})
		return
	}

	if err := service.SlotMachine().DeleteCombination(r.Context(), id); err != nil {
		r.Response.WriteJsonExit(g.Map{
			"code": 500,
			"msg":  err.Error(),
		})
		return
	}

	r.Response.WriteJsonExit(g.Map{
		"code": 0,
		"msg":  "删除成功",
	})
}
