-- 奖惩内容表
CREATE TABLE `punishment_reward` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `title` varchar(255) NOT NULL COMMENT '标题',
    `content` text NOT NULL COMMENT '内容描述',
    `type` tinyint(1) NOT NULL COMMENT '类型：1=奖励，2=惩罚',
    `media_type` tinyint(1) NOT NULL DEFAULT '0' COMMENT '媒体类型：0=无，1=图片，2=音频，3=视频',
    `media_url` varchar(500) DEFAULT NULL COMMENT '媒体文件URL',
    `level` tinyint(1) NOT NULL DEFAULT '1' COMMENT '级别：1=轻微，2=一般，3=严重',
    `is_active` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否启用：0=禁用，1=启用',
    `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `idx_type` (`type`),
    KEY `idx_level` (`level`),
    KEY `idx_is_active` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='奖惩内容表';

-- 游戏记录表
CREATE TABLE `game_record` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `player1_name` varchar(100) NOT NULL COMMENT '玩家1姓名',
    `player2_name` varchar(100) NOT NULL COMMENT '玩家2姓名',
    `selected_item_id` int(11) NOT NULL COMMENT '选中的奖惩内容ID',
    `selected_player` tinyint(1) NOT NULL COMMENT '被选中的玩家：1=玩家1，2=玩家2',
    `game_session` varchar(100) NOT NULL COMMENT '游戏会话ID',
    `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `idx_game_session` (`game_session`),
    KEY `idx_selected_item_id` (`selected_item_id`),
    FOREIGN KEY (`selected_item_id`) REFERENCES `punishment_reward` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='游戏记录表';

-- 插入示例数据
INSERT INTO `punishment_reward` (`title`, `content`, `type`, `media_type`, `level`) VALUES
('做家务', '洗碗或扫地30分钟', 2, 0, 1),
('表演才艺', '唱歌或跳舞表演一首', 2, 0, 1),
('按摩服务', '为对方按摩10分钟', 1, 0, 1),
('购买礼物', '为对方买一份小礼物', 2, 0, 2),
('浪漫晚餐', '准备一顿浪漫晚餐', 1, 0, 2),
('做运动', '做50个俯卧撑', 2, 0, 2),
('写情书', '手写一封情书', 1, 0, 1),
('禁用手机', '1小时内不能使用手机', 2, 0, 2);