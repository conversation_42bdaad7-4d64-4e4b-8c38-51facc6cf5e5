package service

import (
	"context"
	"encoding/json"
	"fmt"
	"lovergame/app/model"
	"math/rand"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/util/guid"
)

type IPunishmentRewardService interface {
	Create(ctx context.Context, data *model.PunishmentReward) error
	Update(ctx context.Context, id int, data *model.PunishmentReward) error
	Delete(ctx context.Context, id int) error
	GetList(ctx context.Context, page, limit int, searchType int) (list []*model.PunishmentReward, total int, err error)
	GetById(ctx context.Context, id int) (*model.PunishmentReward, error)
	GetRandomByCondition(ctx context.Context, prType, level int) (*model.PunishmentReward, error)
}

type punishmentRewardService struct{}

func PunishmentReward() IPunishmentRewardService {
	return &punishmentRewardService{}
}

func (s *punishmentRewardService) Create(ctx context.Context, data *model.PunishmentReward) error {
	_, err := g.DB().Model("punishment_reward").Data(data).Insert()
	return err
}

func (s *punishmentRewardService) Update(ctx context.Context, id int, data *model.PunishmentReward) error {
	// 构建更新数据，排除不应该更新的字段
	updateData := g.Map{
		"title":      data.Title,
		"content":    data.Content,
		"type":       data.Type,
		"media_type": data.MediaType,
		"media_url":  data.MediaUrl,
		"level":      data.Level,
		"is_active":  data.IsActive,
		"updated_at": "CURRENT_TIMESTAMP",
	}

	_, err := g.DB().Model("punishment_reward").Where("id", id).Data(updateData).Update()
	return err
}

func (s *punishmentRewardService) Delete(ctx context.Context, id int) error {
	_, err := g.DB().Model("punishment_reward").Where("id", id).Delete()
	return err
}

func (s *punishmentRewardService) GetList(ctx context.Context, page, limit int, searchType int) (list []*model.PunishmentReward, total int, err error) {
	m := g.DB().Model("punishment_reward")

	if searchType > 0 {
		m = m.Where("type", searchType)
	}

	// 获取总数
	totalCount, err := m.Count()
	if err != nil {
		return nil, 0, err
	}

	// 获取列表
	offset := (page - 1) * limit
	err = m.Order("created_at DESC").Limit(offset, limit).Scan(&list)

	return list, totalCount, err
}

func (s *punishmentRewardService) GetById(ctx context.Context, id int) (*model.PunishmentReward, error) {
	var item *model.PunishmentReward
	err := g.DB().Model("punishment_reward").Where("id", id).Scan(&item)
	return item, err
}

func (s *punishmentRewardService) GetRandomByCondition(ctx context.Context, prType, level int) (*model.PunishmentReward, error) {
	var items []*model.PunishmentReward
	m := g.DB().Model("punishment_reward").Where("is_active", 1)

	if prType > 0 {
		m = m.Where("type", prType)
	}
	if level > 0 {
		m = m.Where("level", level)
	}

	err := m.Scan(&items)
	if err != nil {
		return nil, err
	}

	if len(items) == 0 {
		return nil, fmt.Errorf("没有找到符合条件的奖惩内容")
	}

	// 随机选择一个
	randomIndex := rand.Intn(len(items))
	return items[randomIndex], nil
}

type ISlotMachineService interface {
	GetActiveSymbols(ctx context.Context) ([]*model.SlotSymbol, error)
	FindCombination(ctx context.Context, symbol1Id, symbol2Id, symbol3Id int) (*model.SlotCombination, error)
	GetAllCombinations(ctx context.Context) ([]*model.SlotCombination, error)
	// Symbol management
	GetSymbolList(ctx context.Context, page, limit int) (list []*model.SlotSymbol, total int, err error)
	CreateSymbol(ctx context.Context, data *model.SlotSymbol) error
	UpdateSymbol(ctx context.Context, id int, data *model.SlotSymbol) error
	DeleteSymbol(ctx context.Context, id int) error
	GetSymbolById(ctx context.Context, id int) (*model.SlotSymbol, error)
	// Combination management
	GetCombinationList(ctx context.Context, page, limit int) (list []*model.SlotCombination, total int, err error)
	CreateCombination(ctx context.Context, data *model.SlotCombination) error
	UpdateCombination(ctx context.Context, id int, data *model.SlotCombination) error
	DeleteCombination(ctx context.Context, id int) error
	GetCombinationById(ctx context.Context, id int) (*model.SlotCombination, error)
}

type slotMachineService struct{}

func SlotMachine() ISlotMachineService {
	return &slotMachineService{}
}

func (s *slotMachineService) GetActiveSymbols(ctx context.Context) ([]*model.SlotSymbol, error) {
	var symbols []*model.SlotSymbol
	err := g.DB().Model("slot_symbols").Where("is_active", 1).Scan(&symbols)
	return symbols, err
}

func (s *slotMachineService) FindCombination(ctx context.Context, symbol1Id, symbol2Id, symbol3Id int) (*model.SlotCombination, error) {
	var combination *model.SlotCombination
	err := g.DB().Model("slot_combinations").
		Where("symbol1_id", symbol1Id).
		Where("symbol2_id", symbol2Id).
		Where("symbol3_id", symbol3Id).
		Where("is_active", 1).
		Scan(&combination)

	if err != nil {
		return nil, err
	}

	if combination == nil {
		return nil, fmt.Errorf("未找到匹配的组合")
	}

	return combination, nil
}

func (s *slotMachineService) GetAllCombinations(ctx context.Context) ([]*model.SlotCombination, error) {
	var combinations []*model.SlotCombination
	err := g.DB().Model("slot_combinations").Where("is_active", 1).Scan(&combinations)
	return combinations, err
}

// Symbol management methods
func (s *slotMachineService) GetSymbolList(ctx context.Context, page, limit int) (list []*model.SlotSymbol, total int, err error) {
	m := g.DB().Model("slot_symbols")

	// 获取总数
	total, err = m.Count()
	if err != nil {
		return nil, 0, err
	}

	// 获取分页数据
	err = m.Page(page, limit).OrderDesc("created_at").Scan(&list)
	return list, total, err
}

func (s *slotMachineService) CreateSymbol(ctx context.Context, data *model.SlotSymbol) error {
	_, err := g.DB().Model("slot_symbols").Data(data).Insert()
	return err
}

func (s *slotMachineService) UpdateSymbol(ctx context.Context, id int, data *model.SlotSymbol) error {
	_, err := g.DB().Model("slot_symbols").Where("id", id).Data(data).Update()
	return err
}

func (s *slotMachineService) DeleteSymbol(ctx context.Context, id int) error {
	_, err := g.DB().Model("slot_symbols").Where("id", id).Delete()
	return err
}

func (s *slotMachineService) GetSymbolById(ctx context.Context, id int) (*model.SlotSymbol, error) {
	var symbol *model.SlotSymbol
	err := g.DB().Model("slot_symbols").Where("id", id).Scan(&symbol)
	return symbol, err
}

// Combination management methods
func (s *slotMachineService) GetCombinationList(ctx context.Context, page, limit int) (list []*model.SlotCombination, total int, err error) {
	m := g.DB().Model("slot_combinations")

	// 获取总数
	total, err = m.Count()
	if err != nil {
		return nil, 0, err
	}

	// 获取分页数据
	err = m.Page(page, limit).OrderDesc("created_at").Scan(&list)
	return list, total, err
}

func (s *slotMachineService) CreateCombination(ctx context.Context, data *model.SlotCombination) error {
	_, err := g.DB().Model("slot_combinations").Data(data).Insert()
	return err
}

func (s *slotMachineService) UpdateCombination(ctx context.Context, id int, data *model.SlotCombination) error {
	_, err := g.DB().Model("slot_combinations").Where("id", id).Data(data).Update()
	return err
}

func (s *slotMachineService) DeleteCombination(ctx context.Context, id int) error {
	_, err := g.DB().Model("slot_combinations").Where("id", id).Delete()
	return err
}

func (s *slotMachineService) GetCombinationById(ctx context.Context, id int) (*model.SlotCombination, error) {
	var combination *model.SlotCombination
	err := g.DB().Model("slot_combinations").Where("id", id).Scan(&combination)
	return combination, err
}

type IGameService interface {
	PlayGame(ctx context.Context, req *model.GameRequest) (*model.GameResult, error)
	GetGameHistory(ctx context.Context, page, limit int) (list []*model.GameRecord, total int, err error)
}

type gameService struct{}

func Game() IGameService {
	return &gameService{}
}

func (s *gameService) PlayGame(ctx context.Context, req *model.GameRequest) (*model.GameResult, error) {
	// 默认游戏模式为奖惩游戏
	gameMode := req.GameMode
	if gameMode == "" {
		gameMode = "punishment_reward"
	}

	// 生成游戏会话ID
	gameSession := guid.S()

	// 根据游戏模式处理
	switch gameMode {
	case "slot_machine":
		return s.playSlotMachine(ctx, req, gameSession)
	case "punishment_reward":
		return s.playPunishmentReward(ctx, req, gameSession)
	default:
		return nil, fmt.Errorf("不支持的游戏模式: %s", gameMode)
	}
}

func (s *gameService) playPunishmentReward(ctx context.Context, req *model.GameRequest, gameSession string) (*model.GameResult, error) {
	// 随机选择一个玩家
	selectedPlayer := rand.Intn(2) + 1 // 1 or 2

	// 随机选择奖惩内容
	item, err := PunishmentReward().GetRandomByCondition(ctx, req.Type, req.Level)
	if err != nil {
		return nil, err
	}

	// 保存游戏记录
	insertData := g.Map{
		"player1_name":     req.Player1Name,
		"player2_name":     req.Player2Name,
		"game_mode_id":     1, // 奖惩游戏模式ID
		"selected_item_id": item.Id,
		"selected_player":  selectedPlayer,
		"game_session":     gameSession,
		"created_at":       "CURRENT_TIMESTAMP",
	}

	_, err = g.DB().Model("game_record").Data(insertData).Insert()
	if err != nil {
		return nil, err
	}

	// 构建结果
	result := &model.GameResult{
		Player1Name:    req.Player1Name,
		Player2Name:    req.Player2Name,
		GameMode:       "punishment_reward",
		SelectedPlayer: &selectedPlayer,
		SelectedItem:   item,
		GameSession:    gameSession,
	}

	return result, nil
}

func (s *gameService) playSlotMachine(ctx context.Context, req *model.GameRequest, gameSession string) (*model.GameResult, error) {
	// 获取所有活跃的符号
	symbols, err := SlotMachine().GetActiveSymbols(ctx)
	if err != nil {
		return nil, err
	}

	if len(symbols) < 3 {
		return nil, fmt.Errorf("符号数量不足，无法进行老虎机游戏")
	}

	// 随机选择3个符号
	selectedSymbols := [3]*model.SlotSymbol{}
	for i := 0; i < 3; i++ {
		selectedSymbols[i] = s.getRandomSymbolByRarity(symbols)
	}

	// 检查是否有匹配的组合
	combination, err := SlotMachine().FindCombination(ctx, selectedSymbols[0].Id, selectedSymbols[1].Id, selectedSymbols[2].Id)
	if err != nil {
		// 没有找到组合，创建默认结果
		combination = &model.SlotCombination{
			ActivityTitle:   "再试一次",
			ActivityContent: "这次没有特殊组合，但爱情的轮盘还在转动！",
			ActivityType:    1,
			IntensityLevel:  1,
		}
	}

	// 创建老虎机结果
	slotResult := &model.SlotResult{
		Symbols:     selectedSymbols,
		Combination: combination,
		IsWin:       combination.Id > 0,
		Message:     combination.ActivityTitle,
	}

	// 将结果序列化为JSON
	slotResultJson, _ := json.Marshal(slotResult)

	// 保存游戏记录
	insertData := g.Map{
		"player1_name": req.Player1Name,
		"player2_name": req.Player2Name,
		"game_mode_id": 2, // 老虎机游戏模式ID
		"slot_result":  string(slotResultJson),
		"game_session": gameSession,
		"created_at":   "CURRENT_TIMESTAMP",
	}

	_, err = g.DB().Model("game_record").Data(insertData).Insert()
	if err != nil {
		return nil, err
	}

	// 返回游戏结果
	result := &model.GameResult{
		Player1Name: req.Player1Name,
		Player2Name: req.Player2Name,
		GameMode:    "slot_machine",
		SlotResult:  slotResult,
		GameSession: gameSession,
	}

	return result, nil
}

// 根据稀有度随机选择符号
func (s *gameService) getRandomSymbolByRarity(symbols []*model.SlotSymbol) *model.SlotSymbol {
	// 创建权重数组，稀有度越高权重越低
	weights := make([]int, len(symbols))
	totalWeight := 0

	for i, symbol := range symbols {
		// 稀有度1-5，权重为6-rarity，这样稀有度1的权重最高
		weight := 6 - symbol.Rarity
		weights[i] = weight
		totalWeight += weight
	}

	// 随机选择
	randomValue := rand.Intn(totalWeight)
	currentWeight := 0

	for i, weight := range weights {
		currentWeight += weight
		if randomValue < currentWeight {
			return symbols[i]
		}
	}

	// 默认返回第一个符号
	return symbols[0]
}

func (s *gameService) GetGameHistory(ctx context.Context, page, limit int) (list []*model.GameRecord, total int, err error) {
	m := g.DB().Model("game_record gr").
		LeftJoin("punishment_reward pr", "gr.selected_item_id = pr.id")

	// 获取总数
	totalCount, err := m.Count()
	if err != nil {
		return nil, 0, err
	}

	// 获取列表
	offset := (page - 1) * limit
	err = m.Fields("gr.*").Order("gr.created_at DESC").Limit(offset, limit).Scan(&list)

	return list, totalCount, err
}
