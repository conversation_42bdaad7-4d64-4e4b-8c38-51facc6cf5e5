package service

import (
	"context"
	"encoding/json"
	"fmt"
	"lovergame/app/model"
	"math/rand"
	"strings"
	"time"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/gogf/gf/v2/util/guid"
)

type IPunishmentRewardService interface {
	Create(ctx context.Context, data *model.PunishmentReward) error
	Update(ctx context.Context, id int, data *model.PunishmentReward) error
	Delete(ctx context.Context, id int) error
	GetList(ctx context.Context, page, limit int, searchType int) (list []*model.PunishmentReward, total int, err error)
	GetById(ctx context.Context, id int) (*model.PunishmentReward, error)
	GetRandomByCondition(ctx context.Context, prType, level int) (*model.PunishmentReward, error)
}

type punishmentRewardService struct{}

func PunishmentReward() IPunishmentRewardService {
	return &punishmentRewardService{}
}

func (s *punishmentRewardService) Create(ctx context.Context, data *model.PunishmentReward) error {
	_, err := g.DB().Model("punishment_reward").Data(data).Insert()
	return err
}

func (s *punishmentRewardService) Update(ctx context.Context, id int, data *model.PunishmentReward) error {
	// 构建更新数据，排除不应该更新的字段
	updateData := g.Map{
		"title":      data.Title,
		"content":    data.Content,
		"type":       data.Type,
		"media_type": data.MediaType,
		"media_url":  data.MediaUrl,
		"level":      data.Level,
		"is_active":  data.IsActive,
		"updated_at": "CURRENT_TIMESTAMP",
	}

	_, err := g.DB().Model("punishment_reward").Where("id", id).Data(updateData).Update()
	return err
}

func (s *punishmentRewardService) Delete(ctx context.Context, id int) error {
	_, err := g.DB().Model("punishment_reward").Where("id", id).Delete()
	return err
}

func (s *punishmentRewardService) GetList(ctx context.Context, page, limit int, searchType int) (list []*model.PunishmentReward, total int, err error) {
	m := g.DB().Model("punishment_reward")

	if searchType > 0 {
		m = m.Where("type", searchType)
	}

	// 获取总数
	totalCount, err := m.Count()
	if err != nil {
		return nil, 0, err
	}

	// 获取列表
	offset := (page - 1) * limit
	err = m.Order("created_at DESC").Limit(offset, limit).Scan(&list)

	return list, totalCount, err
}

func (s *punishmentRewardService) GetById(ctx context.Context, id int) (*model.PunishmentReward, error) {
	var item *model.PunishmentReward
	err := g.DB().Model("punishment_reward").Where("id", id).Scan(&item)
	return item, err
}

func (s *punishmentRewardService) GetRandomByCondition(ctx context.Context, prType, level int) (*model.PunishmentReward, error) {
	var items []*model.PunishmentReward
	m := g.DB().Model("punishment_reward").Where("is_active", 1)

	if prType > 0 {
		m = m.Where("type", prType)
	}
	if level > 0 {
		m = m.Where("level", level)
	}

	err := m.Scan(&items)
	if err != nil {
		return nil, err
	}

	if len(items) == 0 {
		return nil, fmt.Errorf("没有找到符合条件的奖惩内容")
	}

	// 随机选择一个
	randomIndex := rand.Intn(len(items))
	return items[randomIndex], nil
}

type ISlotMachineService interface {
	GetActiveSymbols(ctx context.Context) ([]*model.SlotSymbol, error)
	FindCombination(ctx context.Context, symbol1Id, symbol2Id, symbol3Id int) (*model.SlotCombination, error)
	GetAllCombinations(ctx context.Context) ([]*model.SlotCombination, error)
	// Symbol management
	GetSymbolList(ctx context.Context, page, limit int) (list []*model.SlotSymbol, total int, err error)
	CreateSymbol(ctx context.Context, data *model.SlotSymbol) error
	UpdateSymbol(ctx context.Context, id int, data *model.SlotSymbol) error
	DeleteSymbol(ctx context.Context, id int) error
	GetSymbolById(ctx context.Context, id int) (*model.SlotSymbol, error)
	// Combination management
	GetCombinationList(ctx context.Context, page, limit int) (list []*model.SlotCombination, total int, err error)
	CreateCombination(ctx context.Context, data *model.SlotCombination) error
	UpdateCombination(ctx context.Context, id int, data *model.SlotCombination) error
	DeleteCombination(ctx context.Context, id int) error
	GetCombinationById(ctx context.Context, id int) (*model.SlotCombination, error)
}

type slotMachineService struct{}

func SlotMachine() ISlotMachineService {
	return &slotMachineService{}
}

func (s *slotMachineService) GetActiveSymbols(ctx context.Context) ([]*model.SlotSymbol, error) {
	var symbols []*model.SlotSymbol
	err := g.DB().Model("slot_symbols").Where("is_active", 1).Scan(&symbols)
	return symbols, err
}

func (s *slotMachineService) FindCombination(ctx context.Context, symbol1Id, symbol2Id, symbol3Id int) (*model.SlotCombination, error) {
	var combination *model.SlotCombination
	err := g.DB().Model("slot_combinations").
		Where("symbol1_id", symbol1Id).
		Where("symbol2_id", symbol2Id).
		Where("symbol3_id", symbol3Id).
		Where("is_active", 1).
		Scan(&combination)

	if err != nil {
		return nil, err
	}

	if combination == nil {
		return nil, fmt.Errorf("未找到匹配的组合")
	}

	return combination, nil
}

func (s *slotMachineService) GetAllCombinations(ctx context.Context) ([]*model.SlotCombination, error) {
	var combinations []*model.SlotCombination
	err := g.DB().Model("slot_combinations").Where("is_active", 1).Scan(&combinations)
	return combinations, err
}

// Symbol management methods
func (s *slotMachineService) GetSymbolList(ctx context.Context, page, limit int) (list []*model.SlotSymbol, total int, err error) {
	m := g.DB().Model("slot_symbols")

	// 获取总数
	total, err = m.Count()
	if err != nil {
		return nil, 0, err
	}

	// 获取分页数据
	err = m.Page(page, limit).OrderDesc("created_at").Scan(&list)
	return list, total, err
}

func (s *slotMachineService) CreateSymbol(ctx context.Context, data *model.SlotSymbol) error {
	_, err := g.DB().Model("slot_symbols").Data(data).Insert()
	return err
}

func (s *slotMachineService) UpdateSymbol(ctx context.Context, id int, data *model.SlotSymbol) error {
	_, err := g.DB().Model("slot_symbols").Where("id", id).Data(data).Update()
	return err
}

func (s *slotMachineService) DeleteSymbol(ctx context.Context, id int) error {
	_, err := g.DB().Model("slot_symbols").Where("id", id).Delete()
	return err
}

func (s *slotMachineService) GetSymbolById(ctx context.Context, id int) (*model.SlotSymbol, error) {
	var symbol *model.SlotSymbol
	err := g.DB().Model("slot_symbols").Where("id", id).Scan(&symbol)
	return symbol, err
}

// Combination management methods
func (s *slotMachineService) GetCombinationList(ctx context.Context, page, limit int) (list []*model.SlotCombination, total int, err error) {
	m := g.DB().Model("slot_combinations")

	// 获取总数
	total, err = m.Count()
	if err != nil {
		return nil, 0, err
	}

	// 获取分页数据
	err = m.Page(page, limit).OrderDesc("created_at").Scan(&list)
	return list, total, err
}

func (s *slotMachineService) CreateCombination(ctx context.Context, data *model.SlotCombination) error {
	_, err := g.DB().Model("slot_combinations").Data(data).Insert()
	return err
}

func (s *slotMachineService) UpdateCombination(ctx context.Context, id int, data *model.SlotCombination) error {
	_, err := g.DB().Model("slot_combinations").Where("id", id).Data(data).Update()
	return err
}

func (s *slotMachineService) DeleteCombination(ctx context.Context, id int) error {
	_, err := g.DB().Model("slot_combinations").Where("id", id).Delete()
	return err
}

func (s *slotMachineService) GetCombinationById(ctx context.Context, id int) (*model.SlotCombination, error) {
	var combination *model.SlotCombination
	err := g.DB().Model("slot_combinations").Where("id", id).Scan(&combination)
	return combination, err
}

// TruthDare service interface and implementation
type ITruthDareService interface {
	GetRandomQuestion(ctx context.Context, questionType int, difficulty int) (*model.TruthDareQuestion, error)
	GetQuestionList(ctx context.Context, page, limit int) (list []*model.TruthDareQuestion, total int, err error)
	GetQuestionById(ctx context.Context, id int) (*model.TruthDareQuestion, error)
	AddQuestion(ctx context.Context, question *model.TruthDareQuestion) error
	UpdateQuestion(ctx context.Context, id int, question *model.TruthDareQuestion) error
	DeleteQuestion(ctx context.Context, id int) error
}

type truthDareService struct{}

func TruthDare() ITruthDareService {
	return &truthDareService{}
}

func (s *truthDareService) GetRandomQuestion(ctx context.Context, questionType int, difficulty int) (*model.TruthDareQuestion, error) {
	m := g.DB().Model("truth_dare_questions").Where("is_active", 1)

	if questionType > 0 {
		m = m.Where("type", questionType)
	}
	if difficulty > 0 {
		m = m.Where("difficulty", difficulty)
	}

	var questions []*model.TruthDareQuestion
	err := m.Scan(&questions)
	if err != nil {
		return nil, err
	}

	if len(questions) == 0 {
		return nil, fmt.Errorf("没有找到符合条件的问题")
	}

	// 随机选择一个问题
	randomIndex := rand.Intn(len(questions))
	return questions[randomIndex], nil
}

func (s *truthDareService) GetQuestionList(ctx context.Context, page, limit int) (list []*model.TruthDareQuestion, total int, err error) {
	m := g.DB().Model("truth_dare_questions")

	// 获取总数
	total, err = m.Count()
	if err != nil {
		return nil, 0, err
	}

	// 获取分页数据
	err = m.Page(page, limit).OrderDesc("created_at").Scan(&list)
	return list, total, err
}

func (s *truthDareService) GetQuestionById(ctx context.Context, id int) (*model.TruthDareQuestion, error) {
	var question *model.TruthDareQuestion
	err := g.DB().Model("truth_dare_questions").Where("id", id).Scan(&question)
	return question, err
}

func (s *truthDareService) AddQuestion(ctx context.Context, question *model.TruthDareQuestion) error {
	_, err := g.DB().Model("truth_dare_questions").Data(question).Insert()
	return err
}

func (s *truthDareService) UpdateQuestion(ctx context.Context, id int, question *model.TruthDareQuestion) error {
	_, err := g.DB().Model("truth_dare_questions").Where("id", id).Data(question).Update()
	return err
}

func (s *truthDareService) DeleteQuestion(ctx context.Context, id int) error {
	_, err := g.DB().Model("truth_dare_questions").Where("id", id).Delete()
	return err
}

// DiceChallenge service interface and implementation
type IDiceChallengeService interface {
	GetChallengeByDice(ctx context.Context, dice1, dice2 int) (*model.DiceChallenge, error)
	GetChallengeList(ctx context.Context, page, limit int) (list []*model.DiceChallenge, total int, err error)
	GetChallengeById(ctx context.Context, id int) (*model.DiceChallenge, error)
	AddChallenge(ctx context.Context, challenge *model.DiceChallenge) error
	UpdateChallenge(ctx context.Context, id int, challenge *model.DiceChallenge) error
	DeleteChallenge(ctx context.Context, id int) error
}

type diceChallengeService struct{}

func DiceChallenge() IDiceChallengeService {
	return &diceChallengeService{}
}

func (s *diceChallengeService) GetChallengeByDice(ctx context.Context, dice1, dice2 int) (*model.DiceChallenge, error) {
	sum := dice1 + dice2

	// 根据骰子组合查找挑战
	var challenge *model.DiceChallenge
	var err error

	// 特殊组合优先
	if dice1 == dice2 && dice1 == 6 {
		// 双6 - 最高奖励
		err = g.DB().Model("dice_challenges").Where("dice_combination", "6+6").Where("is_active", 1).Scan(&challenge)
	} else if dice1 == dice2 && dice1 == 1 {
		// 双1 - 最小组合
		err = g.DB().Model("dice_challenges").Where("dice_combination", "1+1").Where("is_active", 1).Scan(&challenge)
	} else if dice1%2 == 0 && dice2%2 == 0 {
		// 双偶数
		err = g.DB().Model("dice_challenges").Where("dice_combination", "偶数+偶数").Where("is_active", 1).Scan(&challenge)
	} else if dice1%2 == 1 && dice2%2 == 1 {
		// 双奇数
		err = g.DB().Model("dice_challenges").Where("dice_combination", "奇数+奇数").Where("is_active", 1).Scan(&challenge)
	} else if dice1%2 != dice2%2 {
		// 一奇一偶
		err = g.DB().Model("dice_challenges").Where("dice_combination", "奇数+偶数").Where("is_active", 1).Scan(&challenge)
	}

	// 如果没有找到特殊组合，按总和查找
	if challenge == nil {
		sumStr := fmt.Sprintf("总和为%d", sum)
		err = g.DB().Model("dice_challenges").Where("dice_combination LIKE", "%"+sumStr+"%").Where("is_active", 1).Scan(&challenge)
	}

	// 如果还是没有找到，使用默认挑战
	if challenge == nil {
		err = g.DB().Model("dice_challenges").Where("dice_combination", "其他").Where("is_active", 1).Scan(&challenge)
	}

	return challenge, err
}

func (s *diceChallengeService) GetChallengeList(ctx context.Context, page, limit int) (list []*model.DiceChallenge, total int, err error) {
	m := g.DB().Model("dice_challenges")

	// 获取总数
	total, err = m.Count()
	if err != nil {
		return nil, 0, err
	}

	// 获取分页数据
	err = m.Page(page, limit).OrderDesc("created_at").Scan(&list)
	return list, total, err
}

func (s *diceChallengeService) GetChallengeById(ctx context.Context, id int) (*model.DiceChallenge, error) {
	var challenge *model.DiceChallenge
	err := g.DB().Model("dice_challenges").Where("id", id).Scan(&challenge)
	return challenge, err
}

func (s *diceChallengeService) AddChallenge(ctx context.Context, challenge *model.DiceChallenge) error {
	_, err := g.DB().Model("dice_challenges").Data(challenge).Insert()
	return err
}

func (s *diceChallengeService) UpdateChallenge(ctx context.Context, id int, challenge *model.DiceChallenge) error {
	_, err := g.DB().Model("dice_challenges").Where("id", id).Data(challenge).Update()
	return err
}

func (s *diceChallengeService) DeleteChallenge(ctx context.Context, id int) error {
	_, err := g.DB().Model("dice_challenges").Where("id", id).Delete()
	return err
}

type IGameService interface {
	PlayGame(ctx context.Context, req *model.GameRequest) (*model.GameResult, error)
	GetGameHistory(ctx context.Context, page, limit int) (list []*model.GameRecord, total int, err error)
}

type gameService struct{}

func Game() IGameService {
	return &gameService{}
}

func (s *gameService) PlayGame(ctx context.Context, req *model.GameRequest) (*model.GameResult, error) {
	// 默认游戏模式为奖惩游戏
	gameMode := req.GameMode
	if gameMode == "" {
		gameMode = "punishment_reward"
	}

	// 添加调试日志
	g.Log().Infof(ctx, "🎮 PlayGame called with request: %+v", req)
	g.Log().Infof(ctx, "🎯 Game mode: %s", gameMode)
	g.Log().Infof(ctx, "👥 Players: %s vs %s", req.Player1Name, req.Player2Name)

	// 生成游戏会话ID
	gameSession := guid.S()
	g.Log().Infof(ctx, "🆔 Generated game session: %s", gameSession)

	// 根据游戏模式处理
	g.Log().Infof(ctx, "🔀 Entering switch statement for gameMode: %s", gameMode)
	switch gameMode {
	case "slot_machine":
		g.Log().Infof(ctx, "✅ Matched case: slot_machine")
		return s.playSlotMachine(ctx, req, gameSession)
	case "punishment_reward":
		g.Log().Infof(ctx, "✅ Matched case: punishment_reward")
		return s.playPunishmentReward(ctx, req, gameSession)
	case "truth_dare":
		g.Log().Infof(ctx, "✅ Matched case: truth_dare")
		return s.playTruthDare(ctx, req, gameSession)
	case "dice_challenge":
		g.Log().Infof(ctx, "✅ Matched case: dice_challenge")
		return s.playDiceChallenge(ctx, req, gameSession)
	case "love_quiz":
		g.Log().Infof(ctx, "✅ Matched case: love_quiz")
		return s.playLoveQuiz(ctx, req, gameSession)
	case "photo_challenge":
		g.Log().Infof(ctx, "✅ Matched case: photo_challenge")
		return s.playPhotoChallenge(ctx, req, gameSession)
	default:
		g.Log().Errorf(ctx, "❌ No matching case for gameMode: %s", gameMode)
		return nil, fmt.Errorf("不支持的游戏模式: %s", gameMode)
	}
}

func (s *gameService) playPunishmentReward(ctx context.Context, req *model.GameRequest, gameSession string) (*model.GameResult, error) {
	// 随机选择一个玩家
	selectedPlayer := rand.Intn(2) + 1 // 1 or 2

	// 随机选择奖惩内容
	item, err := PunishmentReward().GetRandomByCondition(ctx, req.Type, req.Level)
	if err != nil {
		return nil, err
	}

	// 保存游戏记录
	insertData := g.Map{
		"player1_name":     req.Player1Name,
		"player2_name":     req.Player2Name,
		"game_mode_id":     1, // 奖惩游戏模式ID
		"selected_item_id": item.Id,
		"selected_player":  selectedPlayer,
		"game_session":     gameSession,
		"created_at":       "CURRENT_TIMESTAMP",
	}

	_, err = g.DB().Model("game_record").Data(insertData).Insert()
	if err != nil {
		return nil, err
	}

	// 构建结果
	result := &model.GameResult{
		Player1Name:    req.Player1Name,
		Player2Name:    req.Player2Name,
		GameMode:       "punishment_reward",
		SelectedPlayer: &selectedPlayer,
		SelectedItem:   item,
		GameSession:    gameSession,
	}

	return result, nil
}

func (s *gameService) playSlotMachine(ctx context.Context, req *model.GameRequest, gameSession string) (*model.GameResult, error) {
	// 获取所有活跃的符号
	symbols, err := SlotMachine().GetActiveSymbols(ctx)
	if err != nil {
		return nil, err
	}

	if len(symbols) < 3 {
		return nil, fmt.Errorf("符号数量不足，无法进行老虎机游戏")
	}

	// 获取中奖概率设置
	winProbability := GameSettings().GetIntSetting(ctx, "slot_win_probability", 30)
	specialProbability := GameSettings().GetIntSetting(ctx, "slot_special_probability", 15)

	// 根据概率决定是否中奖
	shouldWin := rand.Intn(100) < winProbability
	shouldSpecial := rand.Intn(100) < specialProbability

	var selectedSymbols [3]*model.SlotSymbol
	var combination *model.SlotCombination

	if shouldWin {
		// 中奖逻辑：先选择一个组合，然后生成对应的符号
		combinations, err := SlotMachine().GetAllCombinations(ctx)
		if err != nil || len(combinations) == 0 {
			// 如果没有组合，使用随机符号
			selectedSymbols = s.generateRandomSymbols(symbols)
		} else {
			// 根据是否特殊中奖选择组合
			var targetCombinations []*model.SlotCombination
			if shouldSpecial {
				// 选择高强度的特殊组合
				for _, c := range combinations {
					if c.IntensityLevel >= 2 {
						targetCombinations = append(targetCombinations, c)
					}
				}
			}

			if len(targetCombinations) == 0 {
				targetCombinations = combinations
			}

			// 随机选择一个组合
			combination = targetCombinations[rand.Intn(len(targetCombinations))]

			// 根据组合生成符号
			symbol1, _ := SlotMachine().GetSymbolById(ctx, combination.Symbol1Id)
			symbol2, _ := SlotMachine().GetSymbolById(ctx, combination.Symbol2Id)
			symbol3, _ := SlotMachine().GetSymbolById(ctx, combination.Symbol3Id)

			selectedSymbols = [3]*model.SlotSymbol{symbol1, symbol2, symbol3}
		}
	} else {
		// 不中奖逻辑：生成不匹配的随机符号
		selectedSymbols = s.generateNonWinningSymbols(ctx, symbols)
	}

	// 如果还没有找到组合，尝试查找
	if combination == nil {
		combination, err = SlotMachine().FindCombination(ctx, selectedSymbols[0].Id, selectedSymbols[1].Id, selectedSymbols[2].Id)
		if err != nil {
			// 没有找到组合，创建默认结果
			combination = &model.SlotCombination{
				ActivityTitle:   "再试一次",
				ActivityContent: "这次没有特殊组合，但爱情的轮盘还在转动！",
				ActivityType:    1,
				IntensityLevel:  1,
			}
		}
	}

	// 创建老虎机结果
	slotResult := &model.SlotResult{
		Symbols:     selectedSymbols,
		Combination: combination,
		IsWin:       combination.Id > 0,
		Message:     combination.ActivityTitle,
	}

	// 将结果序列化为JSON
	slotResultJson, _ := json.Marshal(slotResult)

	// 保存游戏记录
	insertData := g.Map{
		"player1_name": req.Player1Name,
		"player2_name": req.Player2Name,
		"game_mode_id": 2, // 老虎机游戏模式ID
		"slot_result":  string(slotResultJson),
		"game_session": gameSession,
		"created_at":   "CURRENT_TIMESTAMP",
	}

	_, err = g.DB().Model("game_record").Data(insertData).Insert()
	if err != nil {
		return nil, err
	}

	// 返回游戏结果
	result := &model.GameResult{
		Player1Name: req.Player1Name,
		Player2Name: req.Player2Name,
		GameMode:    "slot_machine",
		SlotResult:  slotResult,
		GameSession: gameSession,
	}

	return result, nil
}

// 根据稀有度随机选择符号
func (s *gameService) getRandomSymbolByRarity(symbols []*model.SlotSymbol) *model.SlotSymbol {
	// 创建权重数组，稀有度越高权重越低
	weights := make([]int, len(symbols))
	totalWeight := 0

	for i, symbol := range symbols {
		// 稀有度1-5，权重为6-rarity，这样稀有度1的权重最高
		weight := 6 - symbol.Rarity
		weights[i] = weight
		totalWeight += weight
	}

	// 随机选择
	randomValue := rand.Intn(totalWeight)
	currentWeight := 0

	for i, weight := range weights {
		currentWeight += weight
		if randomValue < currentWeight {
			return symbols[i]
		}
	}

	// 默认返回第一个符号
	return symbols[0]
}

// 生成随机符号组合
func (s *gameService) generateRandomSymbols(symbols []*model.SlotSymbol) [3]*model.SlotSymbol {
	selectedSymbols := [3]*model.SlotSymbol{}
	for i := 0; i < 3; i++ {
		selectedSymbols[i] = s.getRandomSymbolByRarity(symbols)
	}
	return selectedSymbols
}

// 生成不中奖的符号组合
func (s *gameService) generateNonWinningSymbols(ctx context.Context, symbols []*model.SlotSymbol) [3]*model.SlotSymbol {
	maxAttempts := 50 // 最大尝试次数，避免无限循环

	for attempt := 0; attempt < maxAttempts; attempt++ {
		selectedSymbols := s.generateRandomSymbols(symbols)

		// 检查这个组合是否会中奖
		_, err := SlotMachine().FindCombination(ctx, selectedSymbols[0].Id, selectedSymbols[1].Id, selectedSymbols[2].Id)
		if err != nil {
			// 没有找到组合，说明不会中奖，返回这个组合
			return selectedSymbols
		}
	}

	// 如果尝试多次都找不到不中奖的组合，返回一个确保不中奖的组合
	// 选择三个不同的符号
	if len(symbols) >= 3 {
		return [3]*model.SlotSymbol{symbols[0], symbols[1], symbols[2]}
	}

	// 如果符号不够，返回随机组合
	return s.generateRandomSymbols(symbols)
}

func (s *gameService) playTruthDare(ctx context.Context, req *model.GameRequest, gameSession string) (*model.GameResult, error) {
	// 随机选择真心话或大冒险
	selectedType := rand.Intn(2) + 1 // 1=真心话, 2=大冒险

	// 获取随机问题
	question, err := TruthDare().GetRandomQuestion(ctx, selectedType, 0) // 0表示不限制难度
	if err != nil {
		return nil, err
	}

	// 创建真心话大冒险结果
	truthDareResult := &model.TruthDareResult{
		Question:     question,
		SelectedType: selectedType,
		Message:      fmt.Sprintf("%s：%s", question.Title, question.Content),
	}

	// 保存游戏记录
	resultJson, _ := json.Marshal(truthDareResult)
	insertData := g.Map{
		"player1_name": req.Player1Name,
		"player2_name": req.Player2Name,
		"game_mode_id": 3, // 真心话大冒险游戏模式ID
		"slot_result":  string(resultJson),
		"game_session": gameSession,
		"created_at":   "CURRENT_TIMESTAMP",
	}

	_, err = g.DB().Model("game_record").Data(insertData).Insert()
	if err != nil {
		return nil, err
	}

	// 返回游戏结果
	result := &model.GameResult{
		Player1Name:     req.Player1Name,
		Player2Name:     req.Player2Name,
		GameMode:        "truth_dare",
		TruthDareResult: truthDareResult,
		GameSession:     gameSession,
	}

	return result, nil
}

func (s *gameService) playDiceChallenge(ctx context.Context, req *model.GameRequest, gameSession string) (*model.GameResult, error) {
	// 随机掷两个骰子
	dice1 := rand.Intn(6) + 1
	dice2 := rand.Intn(6) + 1
	sum := dice1 + dice2

	// 根据骰子结果获取挑战
	challenge, err := DiceChallenge().GetChallengeByDice(ctx, dice1, dice2)
	if err != nil {
		return nil, err
	}

	// 创建骰子游戏结果
	diceResult := &model.DiceResult{
		Dice1:     dice1,
		Dice2:     dice2,
		Sum:       sum,
		Challenge: challenge,
		Message:   fmt.Sprintf("🎲 %d + %d = %d，挑战：%s", dice1, dice2, sum, challenge.ActivityTitle),
	}

	// 保存游戏记录
	resultJson, _ := json.Marshal(diceResult)
	insertData := g.Map{
		"player1_name": req.Player1Name,
		"player2_name": req.Player2Name,
		"game_mode_id": 4, // 浪漫骰子游戏模式ID
		"slot_result":  string(resultJson),
		"game_session": gameSession,
		"created_at":   "CURRENT_TIMESTAMP",
	}

	_, err = g.DB().Model("game_record").Data(insertData).Insert()
	if err != nil {
		return nil, err
	}

	// 返回游戏结果
	result := &model.GameResult{
		Player1Name: req.Player1Name,
		Player2Name: req.Player2Name,
		GameMode:    "dice_challenge",
		DiceResult:  diceResult,
		GameSession: gameSession,
	}

	return result, nil
}

func (s *gameService) playLoveQuiz(ctx context.Context, req *model.GameRequest, gameSession string) (*model.GameResult, error) {
	g.Log().Infof(ctx, "💕 playLoveQuiz called with session: %s", gameSession)

	// 开始爱情问答游戏
	g.Log().Infof(ctx, "🎯 Calling LoveQuiz().StartQuizGameWithSession...")
	quizResult, err := LoveQuiz().StartQuizGameWithSession(ctx, gameSession, req.Player1Name, req.Player2Name, 5)
	if err != nil {
		g.Log().Errorf(ctx, "❌ LoveQuiz().StartLoveQuiz failed: %v", err)
		return nil, err
	}
	g.Log().Infof(ctx, "✅ LoveQuiz().StartLoveQuiz succeeded")

	// 保存游戏记录
	resultJson, _ := json.Marshal(quizResult)
	insertData := g.Map{
		"player1_name": req.Player1Name,
		"player2_name": req.Player2Name,
		"game_mode_id": 5, // 爱情问答游戏模式ID
		"slot_result":  string(resultJson),
		"game_session": gameSession,
		"created_at":   "CURRENT_TIMESTAMP",
	}

	_, err = g.DB().Model("game_record").Data(insertData).Insert()
	if err != nil {
		return nil, err
	}

	// 返回游戏结果
	result := &model.GameResult{
		Player1Name:    req.Player1Name,
		Player2Name:    req.Player2Name,
		GameMode:       "love_quiz",
		LoveQuizResult: quizResult,
		GameSession:    gameSession,
	}

	return result, nil
}

func (s *gameService) playPhotoChallenge(ctx context.Context, req *model.GameRequest, gameSession string) (*model.GameResult, error) {
	g.Log().Infof(ctx, "📸 playPhotoChallenge called with session: %s", gameSession)

	// 开始主题摄影游戏
	g.Log().Infof(ctx, "🎯 Calling PhotoChallenge().StartPhotoChallengeWithSession...")
	challengeResult, err := PhotoChallenge().StartPhotoChallengeWithSession(ctx, gameSession, req.Player1Name, req.Player2Name)
	if err != nil {
		g.Log().Errorf(ctx, "❌ PhotoChallenge().StartPhotoChallenge failed: %v", err)
		return nil, err
	}
	g.Log().Infof(ctx, "✅ PhotoChallenge().StartPhotoChallenge succeeded")

	// 保存游戏记录
	resultJson, _ := json.Marshal(challengeResult)
	insertData := g.Map{
		"player1_name": req.Player1Name,
		"player2_name": req.Player2Name,
		"game_mode_id": 6, // 主题摄影游戏模式ID
		"slot_result":  string(resultJson),
		"game_session": gameSession,
		"created_at":   "CURRENT_TIMESTAMP",
	}

	_, err = g.DB().Model("game_record").Data(insertData).Insert()
	if err != nil {
		return nil, err
	}

	// 返回游戏结果
	result := &model.GameResult{
		Player1Name:          req.Player1Name,
		Player2Name:          req.Player2Name,
		GameMode:             "photo_challenge",
		PhotoChallengeResult: challengeResult,
		GameSession:          gameSession,
	}

	return result, nil
}

func (s *gameService) GetGameHistory(ctx context.Context, page, limit int) (list []*model.GameRecord, total int, err error) {
	m := g.DB().Model("game_record gr").
		LeftJoin("punishment_reward pr", "gr.selected_item_id = pr.id")

	// 获取总数
	totalCount, err := m.Count()
	if err != nil {
		return nil, 0, err
	}

	// 获取列表
	offset := (page - 1) * limit
	err = m.Fields("gr.*").Order("gr.created_at DESC").Limit(offset, limit).Scan(&list)

	return list, totalCount, err
}

// GameSettings service interface and implementation
type IGameSettingsService interface {
	GetSetting(ctx context.Context, key string) (*model.GameSetting, error)
	GetAllSettings(ctx context.Context) ([]*model.GameSetting, error)
	UpdateSetting(ctx context.Context, key string, value string) error
	GetIntSetting(ctx context.Context, key string, defaultValue int) int
	GetBoolSetting(ctx context.Context, key string, defaultValue bool) bool
	GetStringSetting(ctx context.Context, key string, defaultValue string) string
}

type gameSettingsService struct{}

func GameSettings() IGameSettingsService {
	return &gameSettingsService{}
}

func (s *gameSettingsService) GetSetting(ctx context.Context, key string) (*model.GameSetting, error) {
	var setting *model.GameSetting
	err := g.DB().Model("game_settings").Where("setting_key", key).Scan(&setting)
	return setting, err
}

func (s *gameSettingsService) GetAllSettings(ctx context.Context) ([]*model.GameSetting, error) {
	var settings []*model.GameSetting
	err := g.DB().Model("game_settings").OrderAsc("setting_key").Scan(&settings)
	return settings, err
}

func (s *gameSettingsService) UpdateSetting(ctx context.Context, key string, value string) error {
	_, err := g.DB().Model("game_settings").Where("setting_key", key).Data(g.Map{
		"setting_value": value,
		"updated_at":    "CURRENT_TIMESTAMP",
	}).Update()
	return err
}

func (s *gameSettingsService) GetIntSetting(ctx context.Context, key string, defaultValue int) int {
	setting, err := s.GetSetting(ctx, key)
	if err != nil || setting == nil {
		return defaultValue
	}

	value := gconv.Int(setting.SettingValue)
	if value <= 0 {
		return defaultValue
	}
	return value
}

func (s *gameSettingsService) GetBoolSetting(ctx context.Context, key string, defaultValue bool) bool {
	setting, err := s.GetSetting(ctx, key)
	if err != nil || setting == nil {
		return defaultValue
	}

	return gconv.Bool(setting.SettingValue)
}

func (s *gameSettingsService) GetStringSetting(ctx context.Context, key string, defaultValue string) string {
	setting, err := s.GetSetting(ctx, key)
	if err != nil || setting == nil {
		return defaultValue
	}

	if setting.SettingValue == "" {
		return defaultValue
	}
	return setting.SettingValue
}

// LoveQuiz service interface and implementation
type ILoveQuizService interface {
	GetRandomQuestion(ctx context.Context, questionType int, difficulty int) (*model.LoveQuiz, error)
	GetQuestionList(ctx context.Context, page, limit int) (list []*model.LoveQuiz, total int, err error)
	GetQuestionById(ctx context.Context, id int) (*model.LoveQuiz, error)
	AddQuestion(ctx context.Context, question *model.LoveQuiz) error
	UpdateQuestion(ctx context.Context, id int, question *model.LoveQuiz) error
	DeleteQuestion(ctx context.Context, id int) error
	StartQuizGame(ctx context.Context, player1Name, player2Name string, totalRounds int) (*model.LoveQuizResult, error)
	StartQuizGameWithSession(ctx context.Context, gameSession, player1Name, player2Name string, totalRounds int) (*model.LoveQuizResult, error)
	ContinueQuizGame(ctx context.Context, gameSession string) (*model.LoveQuizResult, error)
	AnswerQuestion(ctx context.Context, gameSession string, playerId int, answer string) (*model.LoveQuizResult, error)
}

type loveQuizService struct{}

func LoveQuiz() ILoveQuizService {
	return &loveQuizService{}
}

func (s *loveQuizService) GetRandomQuestion(ctx context.Context, questionType int, difficulty int) (*model.LoveQuiz, error) {
	m := g.DB().Model("love_quiz").Where("is_active", 1)

	if questionType > 0 {
		m = m.Where("question_type", questionType)
	}
	if difficulty > 0 {
		m = m.Where("difficulty", difficulty)
	}

	var questions []*model.LoveQuiz
	err := m.Scan(&questions)
	if err != nil {
		return nil, err
	}

	if len(questions) == 0 {
		return nil, fmt.Errorf("没有找到符合条件的问题")
	}

	// 随机选择一个问题
	randomIndex := rand.Intn(len(questions))
	return questions[randomIndex], nil
}

func (s *loveQuizService) GetQuestionList(ctx context.Context, page, limit int) (list []*model.LoveQuiz, total int, err error) {
	m := g.DB().Model("love_quiz")

	// 获取总数
	total, err = m.Count()
	if err != nil {
		return nil, 0, err
	}

	// 获取分页数据
	err = m.Page(page, limit).OrderDesc("created_at").Scan(&list)
	return list, total, err
}

func (s *loveQuizService) GetQuestionById(ctx context.Context, id int) (*model.LoveQuiz, error) {
	var question *model.LoveQuiz
	err := g.DB().Model("love_quiz").Where("id", id).Scan(&question)
	return question, err
}

func (s *loveQuizService) AddQuestion(ctx context.Context, question *model.LoveQuiz) error {
	_, err := g.DB().Model("love_quiz").Data(question).Insert()
	return err
}

func (s *loveQuizService) UpdateQuestion(ctx context.Context, id int, question *model.LoveQuiz) error {
	_, err := g.DB().Model("love_quiz").Where("id", id).Data(question).Update()
	return err
}

func (s *loveQuizService) DeleteQuestion(ctx context.Context, id int) error {
	_, err := g.DB().Model("love_quiz").Where("id", id).Delete()
	return err
}

func (s *loveQuizService) StartQuizGame(ctx context.Context, player1Name, player2Name string, totalRounds int) (*model.LoveQuizResult, error) {
	return s.StartQuizGameWithSession(ctx, "", player1Name, player2Name, totalRounds)
}

func (s *loveQuizService) StartQuizGameWithSession(ctx context.Context, gameSession, player1Name, player2Name string, totalRounds int) (*model.LoveQuizResult, error) {
	// 如果没有提供游戏会话，创建新的
	if gameSession == "" {
		gameSession = fmt.Sprintf("lovequiz_%d", time.Now().Unix())
	}

	// 获取随机问题
	question, err := s.GetRandomQuestion(ctx, 0, 0)
	if err != nil {
		return nil, err
	}

	// 创建游戏记录到数据库
	gameData := g.Map{
		"game_session":        gameSession,
		"player1_name":        player1Name,
		"player2_name":        player2Name,
		"player1_score":       0,
		"player2_score":       0,
		"current_turn":        1,
		"total_rounds":        totalRounds,
		"current_round":       1,
		"is_game_over":        0,
		"winner":              "",
		"current_question_id": question.Id,
		"created_at":          "CURRENT_TIMESTAMP",
	}

	_, err = g.DB().Model("love_quiz_game").Data(gameData).Insert()
	if err != nil {
		return nil, err
	}

	// 创建游戏结果
	result := &model.LoveQuizResult{
		GameSession:  gameSession,
		Question:     question,
		Player1Name:  player1Name,
		Player2Name:  player2Name,
		Player1Score: 0,
		Player2Score: 0,
		CurrentTurn:  1, // 玩家1先开始
		TotalRounds:  totalRounds,
		CurrentRound: 1,
		IsGameOver:   false,
		Winner:       "",
		Message:      fmt.Sprintf("第1轮：%s 先回答问题", player1Name),
	}

	return result, nil
}

func (s *loveQuizService) ContinueQuizGame(ctx context.Context, gameSession string) (*model.LoveQuizResult, error) {
	// 从数据库获取游戏状态
	var gameState struct {
		GameSession       string `json:"game_session"`
		Player1Name       string `json:"player1_name"`
		Player2Name       string `json:"player2_name"`
		Player1Score      int    `json:"player1_score"`
		Player2Score      int    `json:"player2_score"`
		CurrentTurn       int    `json:"current_turn"`
		TotalRounds       int    `json:"total_rounds"`
		CurrentRound      int    `json:"current_round"`
		IsGameOver        int    `json:"is_game_over"`
		Winner            string `json:"winner"`
		CurrentQuestionId int    `json:"current_question_id"`
	}

	err := g.DB().Model("love_quiz_game").Where("game_session", gameSession).Scan(&gameState)
	if err != nil {
		return nil, fmt.Errorf("游戏会话不存在或已过期")
	}

	if gameState.IsGameOver == 1 {
		return nil, fmt.Errorf("游戏已结束")
	}

	// 进入下一轮
	nextRound := gameState.CurrentRound + 1
	if nextRound > gameState.TotalRounds {
		// 游戏结束，计算胜负
		winner := ""
		if gameState.Player1Score > gameState.Player2Score {
			winner = gameState.Player1Name
		} else if gameState.Player2Score > gameState.Player1Score {
			winner = gameState.Player2Name
		} else {
			winner = "平局"
		}

		// 更新游戏状态为结束
		_, err = g.DB().Model("love_quiz_game").Where("game_session", gameSession).Data(g.Map{
			"is_game_over": 1,
			"winner":       winner,
		}).Update()
		if err != nil {
			return nil, err
		}

		// 返回游戏结束结果
		result := &model.LoveQuizResult{
			GameSession:  gameSession,
			Question:     nil,
			Player1Name:  gameState.Player1Name,
			Player2Name:  gameState.Player2Name,
			Player1Score: gameState.Player1Score,
			Player2Score: gameState.Player2Score,
			CurrentTurn:  gameState.CurrentTurn,
			TotalRounds:  gameState.TotalRounds,
			CurrentRound: gameState.CurrentRound,
			IsGameOver:   true,
			Winner:       winner,
			Message: func() string {
				if winner == "平局" {
					return "🎉 游戏结束！平局！"
				}
				return fmt.Sprintf("🎉 游戏结束！%s 获胜！", winner)
			}(),
		}
		return result, nil
	}

	// 获取新问题
	question, err := s.GetRandomQuestion(ctx, 0, 0)
	if err != nil {
		return nil, err
	}

	// 更新游戏状态
	_, err = g.DB().Model("love_quiz_game").Where("game_session", gameSession).Data(g.Map{
		"current_round":       nextRound,
		"current_turn":        1, // 重置为玩家1开始
		"current_question_id": question.Id,
	}).Update()
	if err != nil {
		return nil, err
	}

	// 返回新一轮的游戏状态
	result := &model.LoveQuizResult{
		GameSession:  gameSession,
		Question:     question,
		Player1Name:  gameState.Player1Name,
		Player2Name:  gameState.Player2Name,
		Player1Score: gameState.Player1Score,
		Player2Score: gameState.Player2Score,
		CurrentTurn:  1,
		TotalRounds:  gameState.TotalRounds,
		CurrentRound: nextRound,
		IsGameOver:   false,
		Winner:       "",
		Message:      fmt.Sprintf("第%d轮：%s 先回答问题", nextRound, gameState.Player1Name),
	}

	return result, nil
}

func (s *loveQuizService) AnswerQuestion(ctx context.Context, gameSession string, playerId int, answer string) (*model.LoveQuizResult, error) {
	// 这里应该从缓存或数据库中获取游戏状态
	// 为了简化，这里返回一个示例结果
	question, err := s.GetRandomQuestion(ctx, 0, 0)
	if err != nil {
		return nil, err
	}

	result := &model.LoveQuizResult{
		Question:     question,
		Player1Score: 50,
		Player2Score: 45,
		CurrentTurn:  2,
		TotalRounds:  5,
		CurrentRound: 3,
		IsGameOver:   false,
		Winner:       "",
		Message:      "回答正确！+10分",
	}

	return result, nil
}

// WordChain service interface and implementation
type IWordChainService interface {
	GetWordsByFirstChar(ctx context.Context, firstChar string, category string) ([]*model.WordChain, error)
	GetWordsByLastChar(ctx context.Context, lastChar string, category string) ([]*model.WordChain, error)
	GetRandomStartWord(ctx context.Context, category string) (*model.WordChain, error)
	ValidateWord(ctx context.Context, word string, lastChar string) (*model.WordChain, error)
	StartWordChainGame(ctx context.Context, player1Name, player2Name string, timeLimit int) (*model.WordChainResult, error)
	SubmitWord(ctx context.Context, gameSession string, playerId int, word string) (*model.WordChainResult, error)
	GetWordList(ctx context.Context, page, limit int) (list []*model.WordChain, total int, err error)
	AddWord(ctx context.Context, word *model.WordChain) error
	UpdateWord(ctx context.Context, id int, word *model.WordChain) error
	DeleteWord(ctx context.Context, id int) error
}

type wordChainService struct{}

func WordChain() IWordChainService {
	return &wordChainService{}
}

func (s *wordChainService) GetWordsByFirstChar(ctx context.Context, firstChar string, category string) ([]*model.WordChain, error) {
	m := g.DB().Model("word_chain").Where("first_char", firstChar).Where("is_active", 1)

	if category != "" && category != "all" {
		m = m.Where("category", category)
	}

	var words []*model.WordChain
	err := m.Scan(&words)
	return words, err
}

func (s *wordChainService) GetWordsByLastChar(ctx context.Context, lastChar string, category string) ([]*model.WordChain, error) {
	m := g.DB().Model("word_chain").Where("last_char", lastChar).Where("is_active", 1)

	if category != "" && category != "all" {
		m = m.Where("category", category)
	}

	var words []*model.WordChain
	err := m.Scan(&words)
	return words, err
}

func (s *wordChainService) GetRandomStartWord(ctx context.Context, category string) (*model.WordChain, error) {
	m := g.DB().Model("word_chain").Where("is_active", 1)

	if category != "" && category != "all" {
		m = m.Where("category", category)
	}

	var words []*model.WordChain
	err := m.Scan(&words)
	if err != nil {
		return nil, err
	}

	if len(words) == 0 {
		return nil, fmt.Errorf("没有找到可用的词汇")
	}

	// 随机选择一个词作为开始
	randomIndex := rand.Intn(len(words))
	return words[randomIndex], nil
}

func (s *wordChainService) ValidateWord(ctx context.Context, word string, lastChar string) (*model.WordChain, error) {
	var wordEntity *model.WordChain
	err := g.DB().Model("word_chain").
		Where("word", word).
		Where("first_char", lastChar).
		Where("is_active", 1).
		Scan(&wordEntity)

	if err != nil {
		return nil, err
	}

	if wordEntity == nil {
		return nil, fmt.Errorf("词汇不存在或不符合接龙规则")
	}

	return wordEntity, nil
}

func (s *wordChainService) StartWordChainGame(ctx context.Context, player1Name, player2Name string, timeLimit int) (*model.WordChainResult, error) {
	// 获取随机开始词汇
	startWord, err := s.GetRandomStartWord(ctx, "love")
	if err != nil {
		return nil, err
	}

	// 创建游戏会话
	gameSession := fmt.Sprintf("wordchain_%d", time.Now().Unix())

	// 创建游戏记录
	game := &model.WordChainGame{
		GameSession:      gameSession,
		Player1Name:      player1Name,
		Player2Name:      player2Name,
		CurrentWord:      startWord.Word,
		WordChainHistory: startWord.Word,
		CurrentPlayer:    1,
		Player1Score:     0,
		Player2Score:     0,
		TimeLimit:        timeLimit,
		IsFinished:       0,
	}

	_, err = g.DB().Model("word_chain_game").Data(game).Insert()
	if err != nil {
		return nil, err
	}

	// 获取可接的词汇
	validWords, _ := s.GetWordsByFirstChar(ctx, startWord.LastChar, "love")

	result := &model.WordChainResult{
		Game:        game,
		CurrentWord: startWord.Word,
		ValidWords:  validWords,
		TimeLeft:    timeLimit,
		IsGameOver:  false,
		Message:     fmt.Sprintf("游戏开始！当前词汇：%s，轮到 %s 接龙", startWord.Word, player1Name),
		WordHistory: []string{startWord.Word},
	}

	return result, nil
}

func (s *wordChainService) SubmitWord(ctx context.Context, gameSession string, playerId int, word string) (*model.WordChainResult, error) {
	// 获取游戏状态
	var game *model.WordChainGame
	err := g.DB().Model("word_chain_game").Where("game_session", gameSession).Scan(&game)
	if err != nil {
		return nil, err
	}

	if game.IsFinished == 1 {
		return nil, fmt.Errorf("游戏已结束")
	}

	if game.CurrentPlayer != playerId {
		return nil, fmt.Errorf("不是你的回合")
	}

	// 验证词汇
	currentWordEntity, err := g.DB().Model("word_chain").Where("word", game.CurrentWord).One()
	if err != nil {
		return nil, err
	}

	lastChar := currentWordEntity["last_char"].String()
	validWord, err := s.ValidateWord(ctx, word, lastChar)
	if err != nil {
		return nil, err
	}

	// 更新游戏状态
	wordHistory := strings.Split(game.WordChainHistory, ",")
	wordHistory = append(wordHistory, word)

	// 计算得分
	score := validWord.Difficulty * 10

	updateData := g.Map{
		"current_word":       word,
		"word_chain_history": strings.Join(wordHistory, ","),
		"current_player":     3 - playerId, // 切换玩家 (1->2, 2->1)
	}

	if playerId == 1 {
		updateData["player1_score"] = game.Player1Score + score
	} else {
		updateData["player2_score"] = game.Player2Score + score
	}

	_, err = g.DB().Model("word_chain_game").Where("game_session", gameSession).Data(updateData).Update()
	if err != nil {
		return nil, err
	}

	// 获取更新后的游戏状态
	err = g.DB().Model("word_chain_game").Where("game_session", gameSession).Scan(&game)
	if err != nil {
		return nil, err
	}

	// 获取可接的词汇
	validWords, _ := s.GetWordsByFirstChar(ctx, validWord.LastChar, "love")

	nextPlayerName := game.Player1Name
	if game.CurrentPlayer == 2 {
		nextPlayerName = game.Player2Name
	}

	result := &model.WordChainResult{
		Game:        game,
		CurrentWord: word,
		ValidWords:  validWords,
		TimeLeft:    game.TimeLimit,
		IsGameOver:  false,
		Message:     fmt.Sprintf("接龙成功！+%d分，轮到 %s", score, nextPlayerName),
		WordHistory: wordHistory,
	}

	return result, nil
}

func (s *wordChainService) GetWordList(ctx context.Context, page, limit int) (list []*model.WordChain, total int, err error) {
	m := g.DB().Model("word_chain")

	// 获取总数
	total, err = m.Count()
	if err != nil {
		return nil, 0, err
	}

	// 获取分页数据
	err = m.Page(page, limit).OrderDesc("created_at").Scan(&list)
	return list, total, err
}

func (s *wordChainService) AddWord(ctx context.Context, word *model.WordChain) error {
	_, err := g.DB().Model("word_chain").Data(word).Insert()
	return err
}

func (s *wordChainService) UpdateWord(ctx context.Context, id int, word *model.WordChain) error {
	_, err := g.DB().Model("word_chain").Where("id", id).Data(word).Update()
	return err
}

func (s *wordChainService) DeleteWord(ctx context.Context, id int) error {
	_, err := g.DB().Model("word_chain").Where("id", id).Delete()
	return err
}

// LoveLetter service interface and implementation
type ILoveLetterService interface {
	StartLoveLetterGame(ctx context.Context, player1Name, player2Name string, maxSentences int) (*model.LoveLetterResult, error)
	AddSentence(ctx context.Context, gameSession string, playerId int, sentence string) (*model.LoveLetterResult, error)
	GetLoveLetterBySession(ctx context.Context, gameSession string) (*model.LoveLetterResult, error)
	GetLoveLetterList(ctx context.Context, page, limit int) (list []*model.LoveLetter, total int, err error)
	DeleteLoveLetter(ctx context.Context, id int) error
}

type loveLetterService struct{}

func LoveLetter() ILoveLetterService {
	return &loveLetterService{}
}

func (s *loveLetterService) StartLoveLetterGame(ctx context.Context, player1Name, player2Name string, maxSentences int) (*model.LoveLetterResult, error) {
	// 创建游戏会话
	gameSession := fmt.Sprintf("loveletter_%d", time.Now().Unix())

	// 创建情话接力记录
	letter := &model.LoveLetter{
		GameSession:   gameSession,
		Player1Name:   player1Name,
		Player2Name:   player2Name,
		Title:         "我们的爱情信件",
		Content:       "",
		CurrentPlayer: 1,
		SentenceCount: 0,
		MaxSentences:  maxSentences,
		IsFinished:    0,
	}

	_, err := g.DB().Model("love_letter").Data(letter).Insert()
	if err != nil {
		return nil, err
	}

	result := &model.LoveLetterResult{
		Letter:      letter,
		Sentences:   []*model.LoveLetterSentence{},
		IsGameOver:  false,
		CurrentTurn: player1Name,
		Message:     fmt.Sprintf("开始创作爱情信件！轮到 %s 写第一句话", player1Name),
		FullContent: "",
	}

	return result, nil
}

func (s *loveLetterService) AddSentence(ctx context.Context, gameSession string, playerId int, sentence string) (*model.LoveLetterResult, error) {
	// 获取游戏状态
	var letter *model.LoveLetter
	err := g.DB().Model("love_letter").Where("game_session", gameSession).Scan(&letter)
	if err != nil {
		return nil, err
	}

	if letter.IsFinished == 1 {
		return nil, fmt.Errorf("游戏已结束")
	}

	if letter.CurrentPlayer != playerId {
		return nil, fmt.Errorf("不是你的回合")
	}

	// 添加句子记录
	sentenceRecord := &model.LoveLetterSentence{
		LetterId:      letter.Id,
		PlayerId:      playerId,
		Sentence:      sentence,
		SentenceOrder: letter.SentenceCount + 1,
	}

	_, err = g.DB().Model("love_letter_sentence").Data(sentenceRecord).Insert()
	if err != nil {
		return nil, err
	}

	// 更新信件内容
	newContent := letter.Content
	if newContent != "" {
		newContent += " "
	}
	newContent += sentence

	// 更新游戏状态
	updateData := g.Map{
		"content":        newContent,
		"sentence_count": letter.SentenceCount + 1,
		"current_player": 3 - playerId, // 切换玩家 (1->2, 2->1)
	}

	// 检查是否达到最大句子数
	if letter.SentenceCount+1 >= letter.MaxSentences {
		updateData["is_finished"] = 1
	}

	_, err = g.DB().Model("love_letter").Where("game_session", gameSession).Data(updateData).Update()
	if err != nil {
		return nil, err
	}

	// 获取更新后的状态
	return s.GetLoveLetterBySession(ctx, gameSession)
}

func (s *loveLetterService) GetLoveLetterBySession(ctx context.Context, gameSession string) (*model.LoveLetterResult, error) {
	// 获取信件
	var letter *model.LoveLetter
	err := g.DB().Model("love_letter").Where("game_session", gameSession).Scan(&letter)
	if err != nil {
		return nil, err
	}

	// 获取所有句子
	var sentences []*model.LoveLetterSentence
	err = g.DB().Model("love_letter_sentence").
		Where("letter_id", letter.Id).
		OrderAsc("sentence_order").
		Scan(&sentences)
	if err != nil {
		return nil, err
	}

	// 构建结果
	currentTurn := letter.Player1Name
	if letter.CurrentPlayer == 2 {
		currentTurn = letter.Player2Name
	}

	message := ""
	if letter.IsFinished == 1 {
		message = "🎉 爱情信件创作完成！你们一起写出了一封美丽的情书！"
	} else {
		message = fmt.Sprintf("轮到 %s 继续写下一句话 (%d/%d)", currentTurn, letter.SentenceCount, letter.MaxSentences)
	}

	result := &model.LoveLetterResult{
		Letter:      letter,
		Sentences:   sentences,
		IsGameOver:  letter.IsFinished == 1,
		CurrentTurn: currentTurn,
		Message:     message,
		FullContent: letter.Content,
	}

	return result, nil
}

func (s *loveLetterService) GetLoveLetterList(ctx context.Context, page, limit int) (list []*model.LoveLetter, total int, err error) {
	m := g.DB().Model("love_letter")

	// 获取总数
	total, err = m.Count()
	if err != nil {
		return nil, 0, err
	}

	// 获取分页数据
	err = m.Page(page, limit).OrderDesc("created_at").Scan(&list)
	return list, total, err
}

func (s *loveLetterService) DeleteLoveLetter(ctx context.Context, id int) error {
	// 删除相关句子
	_, err := g.DB().Model("love_letter_sentence").Where("letter_id", id).Delete()
	if err != nil {
		return err
	}

	// 删除信件
	_, err = g.DB().Model("love_letter").Where("id", id).Delete()
	return err
}

// RolePlay service interface and implementation
type IRolePlayService interface {
	GetRandomCharacters(ctx context.Context, characterType string, difficulty int) ([]*model.RolePlayCard, error)
	StartRolePlayGame(ctx context.Context, player1Name, player2Name string, durationMinutes int) (*model.RolePlayResult, error)
	FinishRolePlayGame(ctx context.Context, gameSession string, player1Score, player2Score int) (*model.RolePlayResult, error)
	GetRolePlayCardList(ctx context.Context, page, limit int) (list []*model.RolePlayCard, total int, err error)
	AddRolePlayCard(ctx context.Context, card *model.RolePlayCard) error
	UpdateRolePlayCard(ctx context.Context, id int, card *model.RolePlayCard) error
	DeleteRolePlayCard(ctx context.Context, id int) error
}

type rolePlayService struct{}

func RolePlay() IRolePlayService {
	return &rolePlayService{}
}

func (s *rolePlayService) GetRandomCharacters(ctx context.Context, characterType string, difficulty int) ([]*model.RolePlayCard, error) {
	m := g.DB().Model("role_play_card").Where("is_active", 1)

	if characterType != "" && characterType != "all" {
		m = m.Where("character_type", characterType)
	}
	if difficulty > 0 {
		m = m.Where("difficulty", difficulty)
	}

	var cards []*model.RolePlayCard
	err := m.Scan(&cards)
	if err != nil {
		return nil, err
	}

	if len(cards) < 2 {
		return nil, fmt.Errorf("可用角色卡片不足")
	}

	// 随机选择两个不同的角色
	selectedCards := make([]*model.RolePlayCard, 0, 2)
	usedIndexes := make(map[int]bool)

	for len(selectedCards) < 2 {
		randomIndex := rand.Intn(len(cards))
		if !usedIndexes[randomIndex] {
			selectedCards = append(selectedCards, cards[randomIndex])
			usedIndexes[randomIndex] = true
		}
	}

	return selectedCards, nil
}

func (s *rolePlayService) StartRolePlayGame(ctx context.Context, player1Name, player2Name string, durationMinutes int) (*model.RolePlayResult, error) {
	// 获取随机角色
	characters, err := s.GetRandomCharacters(ctx, "all", 0)
	if err != nil {
		return nil, err
	}

	// 创建游戏会话
	gameSession := fmt.Sprintf("roleplay_%d", time.Now().Unix())

	// 创建游戏记录
	game := &model.RolePlayGame{
		GameSession:        gameSession,
		Player1Name:        player1Name,
		Player2Name:        player2Name,
		Player1CharacterId: characters[0].Id,
		Player2CharacterId: characters[1].Id,
		Player1Score:       0,
		Player2Score:       0,
		DurationMinutes:    durationMinutes,
		IsFinished:         0,
	}

	_, err = g.DB().Model("role_play_game").Data(game).Insert()
	if err != nil {
		return nil, err
	}

	result := &model.RolePlayResult{
		Game:             game,
		Player1Character: characters[0],
		Player2Character: characters[1],
		IsGameOver:       false,
		Message:          fmt.Sprintf("角色扮演开始！%s 扮演 %s，%s 扮演 %s", player1Name, characters[0].CharacterName, player2Name, characters[1].CharacterName),
		TimeLeft:         durationMinutes * 60, // 转换为秒
	}

	return result, nil
}

func (s *rolePlayService) FinishRolePlayGame(ctx context.Context, gameSession string, player1Score, player2Score int) (*model.RolePlayResult, error) {
	// 更新游戏状态
	updateData := g.Map{
		"player1_score": player1Score,
		"player2_score": player2Score,
		"is_finished":   1,
	}

	_, err := g.DB().Model("role_play_game").Where("game_session", gameSession).Data(updateData).Update()
	if err != nil {
		return nil, err
	}

	// 获取更新后的游戏状态
	var game *model.RolePlayGame
	err = g.DB().Model("role_play_game").Where("game_session", gameSession).Scan(&game)
	if err != nil {
		return nil, err
	}

	// 获取角色信息
	var player1Character, player2Character *model.RolePlayCard
	err = g.DB().Model("role_play_card").Where("id", game.Player1CharacterId).Scan(&player1Character)
	if err != nil {
		return nil, err
	}
	err = g.DB().Model("role_play_card").Where("id", game.Player2CharacterId).Scan(&player2Character)
	if err != nil {
		return nil, err
	}

	// 确定获胜者
	winner := ""
	if player1Score > player2Score {
		winner = game.Player1Name
	} else if player2Score > player1Score {
		winner = game.Player2Name
	} else {
		winner = "平局"
	}

	message := fmt.Sprintf("角色扮演结束！%s 获得 %d 分，%s 获得 %d 分。",
		game.Player1Name, player1Score, game.Player2Name, player2Score)
	if winner != "平局" {
		message += fmt.Sprintf(" %s 获胜！", winner)
	} else {
		message += " 平局！你们都表现得很棒！"
	}

	result := &model.RolePlayResult{
		Game:             game,
		Player1Character: player1Character,
		Player2Character: player2Character,
		IsGameOver:       true,
		Message:          message,
		TimeLeft:         0,
	}

	return result, nil
}

func (s *rolePlayService) GetRolePlayCardList(ctx context.Context, page, limit int) (list []*model.RolePlayCard, total int, err error) {
	m := g.DB().Model("role_play_card")

	// 获取总数
	total, err = m.Count()
	if err != nil {
		return nil, 0, err
	}

	// 获取分页数据
	err = m.Page(page, limit).OrderDesc("created_at").Scan(&list)
	return list, total, err
}

func (s *rolePlayService) AddRolePlayCard(ctx context.Context, card *model.RolePlayCard) error {
	_, err := g.DB().Model("role_play_card").Data(card).Insert()
	return err
}

func (s *rolePlayService) UpdateRolePlayCard(ctx context.Context, id int, card *model.RolePlayCard) error {
	_, err := g.DB().Model("role_play_card").Where("id", id).Data(card).Update()
	return err
}

func (s *rolePlayService) DeleteRolePlayCard(ctx context.Context, id int) error {
	_, err := g.DB().Model("role_play_card").Where("id", id).Delete()
	return err
}

// FuturePrediction service interface and implementation
type IFuturePredictionService interface {
	CreatePrediction(ctx context.Context, gameSession, predictorName, targetName, predictionText string) (*model.FuturePrediction, error)
	VerifyPrediction(ctx context.Context, id int, isCorrect bool, actualResult string) (*model.FuturePrediction, error)
	GetPredictionsBySession(ctx context.Context, gameSession string) ([]*model.FuturePrediction, error)
	GetPendingPredictions(ctx context.Context, targetName string) ([]*model.FuturePrediction, error)
}

type futurePredictionService struct{}

func FuturePrediction() IFuturePredictionService {
	return &futurePredictionService{}
}

func (s *futurePredictionService) CreatePrediction(ctx context.Context, gameSession, predictorName, targetName, predictionText string) (*model.FuturePrediction, error) {
	prediction := &model.FuturePrediction{
		GameSession:    gameSession,
		PredictorName:  predictorName,
		TargetName:     targetName,
		PredictionText: predictionText,
		PredictionDate: gtime.Now(),
		Points:         0,
	}

	_, err := g.DB().Model("future_prediction").Data(prediction).Insert()
	if err != nil {
		return nil, err
	}

	return prediction, nil
}

func (s *futurePredictionService) VerifyPrediction(ctx context.Context, id int, isCorrect bool, actualResult string) (*model.FuturePrediction, error) {
	correctValue := 0
	if isCorrect {
		correctValue = 1
	}

	points := 0
	if isCorrect {
		points = 10 // 预测正确得10分
	}

	updateData := g.Map{
		"verification_date": gtime.Now(),
		"is_correct":        correctValue,
		"actual_result":     actualResult,
		"points":            points,
	}

	_, err := g.DB().Model("future_prediction").Where("id", id).Data(updateData).Update()
	if err != nil {
		return nil, err
	}

	// 获取更新后的预测
	var prediction *model.FuturePrediction
	err = g.DB().Model("future_prediction").Where("id", id).Scan(&prediction)
	return prediction, err
}

func (s *futurePredictionService) GetPredictionsBySession(ctx context.Context, gameSession string) ([]*model.FuturePrediction, error) {
	var predictions []*model.FuturePrediction
	err := g.DB().Model("future_prediction").
		Where("game_session", gameSession).
		OrderDesc("created_at").
		Scan(&predictions)
	return predictions, err
}

func (s *futurePredictionService) GetPendingPredictions(ctx context.Context, targetName string) ([]*model.FuturePrediction, error) {
	var predictions []*model.FuturePrediction
	err := g.DB().Model("future_prediction").
		Where("target_name", targetName).
		Where("verification_date IS NULL").
		OrderDesc("created_at").
		Scan(&predictions)
	return predictions, err
}

// GamePreset service interface and implementation
type IGamePresetService interface {
	GetPresetList(ctx context.Context, category string) ([]*model.GamePreset, error)
	GetPresetById(ctx context.Context, id int) (*model.GamePreset, error)
	CreatePreset(ctx context.Context, preset *model.GamePreset) error
	UpdatePreset(ctx context.Context, id int, preset *model.GamePreset) error
	DeletePreset(ctx context.Context, id int) error
	ApplyPreset(ctx context.Context, presetId int) (map[string]interface{}, error)
	SaveCurrentAsPreset(ctx context.Context, name, description string, settings map[string]interface{}, createdBy string) error
}

type gamePresetService struct{}

func GamePreset() IGamePresetService {
	return &gamePresetService{}
}

func (s *gamePresetService) GetPresetList(ctx context.Context, category string) ([]*model.GamePreset, error) {
	m := g.DB().Model("game_presets").Where("is_active", 1).Order("category, intimacy_level, id")

	if category != "" && category != "all" {
		m = m.Where("category", category)
	}

	var presets []*model.GamePreset
	err := m.Scan(&presets)
	return presets, err
}

func (s *gamePresetService) GetPresetById(ctx context.Context, id int) (*model.GamePreset, error) {
	var preset *model.GamePreset
	err := g.DB().Model("game_presets").Where("id", id).Where("is_active", 1).Scan(&preset)
	return preset, err
}

func (s *gamePresetService) CreatePreset(ctx context.Context, preset *model.GamePreset) error {
	_, err := g.DB().Model("game_presets").Data(preset).Insert()
	return err
}

func (s *gamePresetService) UpdatePreset(ctx context.Context, id int, preset *model.GamePreset) error {
	_, err := g.DB().Model("game_presets").Where("id", id).Data(preset).Update()
	return err
}

func (s *gamePresetService) DeletePreset(ctx context.Context, id int) error {
	_, err := g.DB().Model("game_presets").Where("id", id).Data(g.Map{"is_active": 0}).Update()
	return err
}

func (s *gamePresetService) ApplyPreset(ctx context.Context, presetId int) (map[string]interface{}, error) {
	preset, err := s.GetPresetById(ctx, presetId)
	if err != nil {
		return nil, err
	}

	// 解析设置JSON
	var settings map[string]interface{}
	err = json.Unmarshal([]byte(preset.Settings), &settings)
	if err != nil {
		return nil, err
	}

	return settings, nil
}

func (s *gamePresetService) SaveCurrentAsPreset(ctx context.Context, name, description string, settings map[string]interface{}, createdBy string) error {
	// 将设置转换为JSON
	settingsJson, err := json.Marshal(settings)
	if err != nil {
		return err
	}

	preset := &model.GamePreset{
		Name:             name,
		Description:      description,
		Category:         "custom",
		RelationshipType: "custom",
		IntimacyLevel:    3,
		Settings:         string(settingsJson),
		IsSystem:         0,
		IsActive:         1,
		CreatedBy:        createdBy,
	}

	return s.CreatePreset(ctx, preset)
}

// PhotoChallenge service interface and implementation
type IPhotoChallengeService interface {
	GetRandomTheme(ctx context.Context, category string, difficulty int) (*model.PhotoTheme, error)
	StartPhotoChallenge(ctx context.Context, player1Name, player2Name string) (*model.PhotoChallengeResult, error)
	StartPhotoChallengeWithSession(ctx context.Context, gameSession, player1Name, player2Name string) (*model.PhotoChallengeResult, error)
	SubmitPhoto(ctx context.Context, gameSession string, playerName string, photoUrl string) (*model.PhotoChallengeResult, error)
	VoteForPhoto(ctx context.Context, gameSession string, voterName string, winnerName string) (*model.PhotoChallengeResult, error)
	GetPhotoChallengeBySession(ctx context.Context, gameSession string) (*model.PhotoChallengeResult, error)
	GetPhotoThemeList(ctx context.Context, page, limit int) (list []*model.PhotoTheme, total int, err error)
	AddPhotoTheme(ctx context.Context, theme *model.PhotoTheme) error
	UpdatePhotoTheme(ctx context.Context, id int, theme *model.PhotoTheme) error
	DeletePhotoTheme(ctx context.Context, id int) error
}

type photoChallengeService struct{}

func PhotoChallenge() IPhotoChallengeService {
	return &photoChallengeService{}
}

func (s *photoChallengeService) GetRandomTheme(ctx context.Context, category string, difficulty int) (*model.PhotoTheme, error) {
	m := g.DB().Model("photo_theme").Where("is_active", 1)

	if category != "" && category != "all" {
		m = m.Where("category", category)
	}
	if difficulty > 0 {
		m = m.Where("difficulty", difficulty)
	}

	var themes []*model.PhotoTheme
	err := m.Scan(&themes)
	if err != nil {
		return nil, err
	}

	if len(themes) == 0 {
		return nil, fmt.Errorf("没有找到符合条件的摄影主题")
	}

	// 随机选择一个主题
	randomIndex := rand.Intn(len(themes))
	return themes[randomIndex], nil
}

func (s *photoChallengeService) StartPhotoChallenge(ctx context.Context, player1Name, player2Name string) (*model.PhotoChallengeResult, error) {
	return s.StartPhotoChallengeWithSession(ctx, "", player1Name, player2Name)
}

func (s *photoChallengeService) StartPhotoChallengeWithSession(ctx context.Context, gameSession, player1Name, player2Name string) (*model.PhotoChallengeResult, error) {
	// 获取随机主题
	theme, err := s.GetRandomTheme(ctx, "all", 0)
	if err != nil {
		return nil, err
	}

	// 如果没有提供游戏会话，创建新的
	if gameSession == "" {
		gameSession = fmt.Sprintf("photochallenge_%d", time.Now().Unix())
	}

	// 创建摄影挑战记录 - 使用 Map 避免 Id 字段冲突
	challengeData := g.Map{
		"game_session":   gameSession,
		"player1_name":   player1Name,
		"player2_name":   player2Name,
		"theme":          theme.ThemeName,
		"description":    theme.Description,
		"player1_photo":  "",
		"player2_photo":  "",
		"player1_score":  0,
		"player2_score":  0,
		"voting_enabled": 1,
		"is_finished":    0,
		"winner":         "",
		"created_at":     "CURRENT_TIMESTAMP",
		"updated_at":     "CURRENT_TIMESTAMP",
	}

	insertResult, err := g.DB().Model("photo_challenge").Data(challengeData).Insert()
	if err != nil {
		return nil, err
	}

	// 获取插入的记录ID
	insertId, _ := insertResult.LastInsertId()

	// 创建 PhotoChallenge 对象用于返回结果
	challenge := &model.PhotoChallenge{
		Id:            int(insertId),
		GameSession:   gameSession,
		Player1Name:   player1Name,
		Player2Name:   player2Name,
		Theme:         theme.ThemeName,
		Description:   theme.Description,
		Player1Photo:  "",
		Player2Photo:  "",
		Player1Score:  0,
		Player2Score:  0,
		VotingEnabled: 1,
		IsFinished:    0,
		Winner:        "",
	}

	result := &model.PhotoChallengeResult{
		Challenge:   challenge,
		Theme:       theme,
		IsCompleted: false,
		CanVote:     false,
		Message:     fmt.Sprintf("📸 摄影主题：%s - %s", theme.ThemeName, theme.Description),
	}

	return result, nil
}

func (s *photoChallengeService) SubmitPhoto(ctx context.Context, gameSession string, playerName string, photoUrl string) (*model.PhotoChallengeResult, error) {
	// 获取挑战状态
	var challenge *model.PhotoChallenge
	err := g.DB().Model("photo_challenge").Where("game_session", gameSession).Scan(&challenge)
	if err != nil {
		return nil, err
	}

	if challenge.IsFinished == 1 {
		return nil, fmt.Errorf("游戏已结束")
	}

	// 更新照片
	updateData := g.Map{}
	if playerName == challenge.Player1Name {
		updateData["player1_photo"] = photoUrl
	} else if playerName == challenge.Player2Name {
		updateData["player2_photo"] = photoUrl
	} else {
		return nil, fmt.Errorf("玩家名称不匹配")
	}

	_, err = g.DB().Model("photo_challenge").Where("game_session", gameSession).Data(updateData).Update()
	if err != nil {
		return nil, err
	}

	return s.GetPhotoChallengeBySession(ctx, gameSession)
}

func (s *photoChallengeService) VoteForPhoto(ctx context.Context, gameSession string, voterName string, winnerName string) (*model.PhotoChallengeResult, error) {
	// 获取挑战状态
	var challenge *model.PhotoChallenge
	err := g.DB().Model("photo_challenge").Where("game_session", gameSession).Scan(&challenge)
	if err != nil {
		return nil, err
	}

	if challenge.IsFinished == 1 {
		return nil, fmt.Errorf("游戏已结束")
	}

	// 检查是否两人都已提交照片
	if challenge.Player1Photo == "" || challenge.Player2Photo == "" {
		return nil, fmt.Errorf("还有玩家未提交照片")
	}

	// 更新分数和结束游戏
	updateData := g.Map{
		"is_finished": 1,
		"winner":      winnerName,
	}

	if winnerName == challenge.Player1Name {
		updateData["player1_score"] = 10
		updateData["player2_score"] = 5
	} else if winnerName == challenge.Player2Name {
		updateData["player1_score"] = 5
		updateData["player2_score"] = 10
	} else {
		// 平局
		updateData["player1_score"] = 8
		updateData["player2_score"] = 8
		updateData["winner"] = "平局"
	}

	_, err = g.DB().Model("photo_challenge").Where("game_session", gameSession).Data(updateData).Update()
	if err != nil {
		return nil, err
	}

	return s.GetPhotoChallengeBySession(ctx, gameSession)
}

func (s *photoChallengeService) GetPhotoChallengeBySession(ctx context.Context, gameSession string) (*model.PhotoChallengeResult, error) {
	// 获取挑战
	var challenge *model.PhotoChallenge
	err := g.DB().Model("photo_challenge").Where("game_session", gameSession).Scan(&challenge)
	if err != nil {
		return nil, err
	}

	// 获取主题信息
	var theme *model.PhotoTheme
	err = g.DB().Model("photo_theme").Where("theme_name", challenge.Theme).Scan(&theme)
	if err != nil {
		return nil, err
	}

	// 构建结果
	isCompleted := challenge.IsFinished == 1
	canVote := challenge.Player1Photo != "" && challenge.Player2Photo != "" && !isCompleted

	message := ""
	if isCompleted {
		if challenge.Winner == "平局" {
			message = "🎉 游戏结束！平局！你们都拍得很棒！"
		} else {
			message = fmt.Sprintf("🎉 游戏结束！%s 获胜！", challenge.Winner)
		}
	} else if canVote {
		message = "📸 两人都已提交照片，现在可以投票选出最佳照片！"
	} else {
		submitted := 0
		if challenge.Player1Photo != "" {
			submitted++
		}
		if challenge.Player2Photo != "" {
			submitted++
		}
		message = fmt.Sprintf("📸 已提交照片：%d/2，等待其他玩家提交", submitted)
	}

	result := &model.PhotoChallengeResult{
		Challenge:   challenge,
		Theme:       theme,
		IsCompleted: isCompleted,
		CanVote:     canVote,
		Message:     message,
	}

	return result, nil
}

func (s *photoChallengeService) GetPhotoThemeList(ctx context.Context, page, limit int) (list []*model.PhotoTheme, total int, err error) {
	m := g.DB().Model("photo_theme")

	// 获取总数
	total, err = m.Count()
	if err != nil {
		return nil, 0, err
	}

	// 获取分页数据
	err = m.Page(page, limit).OrderDesc("created_at").Scan(&list)
	return list, total, err
}

func (s *photoChallengeService) AddPhotoTheme(ctx context.Context, theme *model.PhotoTheme) error {
	_, err := g.DB().Model("photo_theme").Data(theme).Insert()
	return err
}

func (s *photoChallengeService) UpdatePhotoTheme(ctx context.Context, id int, theme *model.PhotoTheme) error {
	_, err := g.DB().Model("photo_theme").Where("id", id).Data(theme).Update()
	return err
}

func (s *photoChallengeService) DeletePhotoTheme(ctx context.Context, id int) error {
	_, err := g.DB().Model("photo_theme").Where("id", id).Delete()
	return err
}

// MusicChain service interface and implementation
type IMusicChainService interface {
	StartMusicChain(ctx context.Context, player1Name, player2Name string, maxRounds int) (*model.MusicChainResult, error)
	SubmitSong(ctx context.Context, gameSession string, singerName string, audioUrl string, songTitle string, artist string) (*model.MusicChainResult, error)
	SubmitGuess(ctx context.Context, gameSession string, guesserName string, guessedTitle string) (*model.MusicChainResult, error)
	GetMusicChainBySession(ctx context.Context, gameSession string) (*model.MusicChainResult, error)
}

type musicChainService struct{}

func MusicChain() IMusicChainService {
	return &musicChainService{}
}

func (s *musicChainService) StartMusicChain(ctx context.Context, player1Name, player2Name string, maxRounds int) (*model.MusicChainResult, error) {
	// 创建游戏会话
	gameSession := fmt.Sprintf("musicchain_%d", time.Now().Unix())

	// 创建音乐接龙记录
	game := &model.MusicChain{
		GameSession:   gameSession,
		Player1Name:   player1Name,
		Player2Name:   player2Name,
		CurrentPlayer: 1,
		RoundNumber:   1,
		MaxRounds:     maxRounds,
		Player1Score:  0,
		Player2Score:  0,
		IsFinished:    0,
	}

	_, err := g.DB().Model("music_chain").Data(game).Insert()
	if err != nil {
		return nil, err
	}

	result := &model.MusicChainResult{
		Game:        game,
		Rounds:      []*model.MusicRound{},
		IsCompleted: false,
		CurrentTurn: player1Name,
		Message:     fmt.Sprintf("🎵 音乐接龙开始！轮到 %s 哼唱歌曲", player1Name),
		Action:      "sing",
	}

	return result, nil
}

func (s *musicChainService) SubmitSong(ctx context.Context, gameSession string, singerName string, audioUrl string, songTitle string, artist string) (*model.MusicChainResult, error) {
	// 获取游戏状态
	var game *model.MusicChain
	err := g.DB().Model("music_chain").Where("game_session", gameSession).Scan(&game)
	if err != nil {
		return nil, err
	}

	if game.IsFinished == 1 {
		return nil, fmt.Errorf("游戏已结束")
	}

	// 确定猜歌者
	guesserName := game.Player2Name
	if singerName == game.Player2Name {
		guesserName = game.Player1Name
	}

	// 创建回合记录
	round := &model.MusicRound{
		GameId:       game.Id,
		RoundNumber:  game.RoundNumber,
		SingerName:   singerName,
		GuesserName:  guesserName,
		AudioUrl:     audioUrl,
		SongTitle:    songTitle,
		Artist:       artist,
		PointsEarned: 0,
	}

	_, err = g.DB().Model("music_round").Data(round).Insert()
	if err != nil {
		return nil, err
	}

	return s.GetMusicChainBySession(ctx, gameSession)
}

func (s *musicChainService) SubmitGuess(ctx context.Context, gameSession string, guesserName string, guessedTitle string) (*model.MusicChainResult, error) {
	// 获取游戏状态
	var game *model.MusicChain
	err := g.DB().Model("music_chain").Where("game_session", gameSession).Scan(&game)
	if err != nil {
		return nil, err
	}

	if game.IsFinished == 1 {
		return nil, fmt.Errorf("游戏已结束")
	}

	// 获取当前回合
	var round *model.MusicRound
	err = g.DB().Model("music_round").
		Where("game_id", game.Id).
		Where("round_number", game.RoundNumber).
		Where("guesser_name", guesserName).
		Scan(&round)
	if err != nil {
		return nil, err
	}

	// 判断答案是否正确
	isCorrect := 0
	points := 0
	if strings.Contains(strings.ToLower(round.SongTitle), strings.ToLower(guessedTitle)) ||
		strings.Contains(strings.ToLower(guessedTitle), strings.ToLower(round.SongTitle)) {
		isCorrect = 1
		points = 10
	}

	// 更新回合记录
	updateRoundData := g.Map{
		"guessed_title": guessedTitle,
		"is_correct":    isCorrect,
		"points_earned": points,
	}

	_, err = g.DB().Model("music_round").Where("id", round.Id).Data(updateRoundData).Update()
	if err != nil {
		return nil, err
	}

	// 更新游戏分数和状态
	updateGameData := g.Map{}
	if guesserName == game.Player1Name {
		updateGameData["player1_score"] = game.Player1Score + points
	} else {
		updateGameData["player2_score"] = game.Player2Score + points
	}

	// 检查是否游戏结束
	if game.RoundNumber >= game.MaxRounds {
		updateGameData["is_finished"] = 1
		// 确定获胜者
		finalPlayer1Score := game.Player1Score
		finalPlayer2Score := game.Player2Score
		if guesserName == game.Player1Name {
			finalPlayer1Score += points
		} else {
			finalPlayer2Score += points
		}

		if finalPlayer1Score > finalPlayer2Score {
			updateGameData["winner"] = game.Player1Name
		} else if finalPlayer2Score > finalPlayer1Score {
			updateGameData["winner"] = game.Player2Name
		} else {
			updateGameData["winner"] = "平局"
		}
	} else {
		// 下一回合，切换玩家
		updateGameData["round_number"] = game.RoundNumber + 1
		updateGameData["current_player"] = 3 - game.CurrentPlayer // 1->2, 2->1
	}

	_, err = g.DB().Model("music_chain").Where("game_session", gameSession).Data(updateGameData).Update()
	if err != nil {
		return nil, err
	}

	return s.GetMusicChainBySession(ctx, gameSession)
}

func (s *musicChainService) GetMusicChainBySession(ctx context.Context, gameSession string) (*model.MusicChainResult, error) {
	// 获取游戏
	var game *model.MusicChain
	err := g.DB().Model("music_chain").Where("game_session", gameSession).Scan(&game)
	if err != nil {
		return nil, err
	}

	// 获取所有回合
	var rounds []*model.MusicRound
	err = g.DB().Model("music_round").
		Where("game_id", game.Id).
		OrderAsc("round_number").
		Scan(&rounds)
	if err != nil {
		return nil, err
	}

	// 获取当前回合
	var currentRound *model.MusicRound
	if len(rounds) > 0 {
		for _, round := range rounds {
			if round.RoundNumber == game.RoundNumber {
				currentRound = round
				break
			}
		}
	}

	// 构建结果
	currentTurn := game.Player1Name
	if game.CurrentPlayer == 2 {
		currentTurn = game.Player2Name
	}

	action := "sing"
	message := ""

	if game.IsFinished == 1 {
		action = "finished"
		if game.Winner == "平局" {
			message = "🎉 音乐接龙结束！平局！你们都很棒！"
		} else {
			message = fmt.Sprintf("🎉 音乐接龙结束！%s 获胜！", game.Winner)
		}
	} else if currentRound != nil && currentRound.IsCorrect == nil {
		action = "guess"
		message = fmt.Sprintf("🎵 轮到 %s 猜歌名", currentRound.GuesserName)
	} else {
		action = "sing"
		message = fmt.Sprintf("🎵 第%d回合，轮到 %s 哼唱歌曲", game.RoundNumber, currentTurn)
	}

	result := &model.MusicChainResult{
		Game:         game,
		CurrentRound: currentRound,
		Rounds:       rounds,
		IsCompleted:  game.IsFinished == 1,
		CurrentTurn:  currentTurn,
		Message:      message,
		Action:       action,
	}

	return result, nil
}
