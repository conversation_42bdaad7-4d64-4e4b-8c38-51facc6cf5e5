package service

import (
	"context"
	"fmt"
	"lovergame/app/model"
	"math/rand"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/util/guid"
)

type IPunishmentRewardService interface {
	Create(ctx context.Context, data *model.PunishmentReward) error
	Update(ctx context.Context, id int, data *model.PunishmentReward) error
	Delete(ctx context.Context, id int) error
	GetList(ctx context.Context, page, limit int, searchType int) (list []*model.PunishmentReward, total int, err error)
	GetById(ctx context.Context, id int) (*model.PunishmentReward, error)
	GetRandomByCondition(ctx context.Context, prType, level int) (*model.PunishmentReward, error)
}

type punishmentRewardService struct{}

func PunishmentReward() IPunishmentRewardService {
	return &punishmentRewardService{}
}

func (s *punishmentRewardService) Create(ctx context.Context, data *model.PunishmentReward) error {
	_, err := g.DB().Model("punishment_reward").Data(data).Insert()
	return err
}

func (s *punishmentRewardService) Update(ctx context.Context, id int, data *model.PunishmentReward) error {
	// 构建更新数据，排除不应该更新的字段
	updateData := g.Map{
		"title":      data.Title,
		"content":    data.Content,
		"type":       data.Type,
		"media_type": data.MediaType,
		"media_url":  data.MediaUrl,
		"level":      data.Level,
		"is_active":  data.IsActive,
		"updated_at": "CURRENT_TIMESTAMP",
	}

	_, err := g.DB().Model("punishment_reward").Where("id", id).Data(updateData).Update()
	return err
}

func (s *punishmentRewardService) Delete(ctx context.Context, id int) error {
	_, err := g.DB().Model("punishment_reward").Where("id", id).Delete()
	return err
}

func (s *punishmentRewardService) GetList(ctx context.Context, page, limit int, searchType int) (list []*model.PunishmentReward, total int, err error) {
	m := g.DB().Model("punishment_reward")

	if searchType > 0 {
		m = m.Where("type", searchType)
	}

	// 获取总数
	totalCount, err := m.Count()
	if err != nil {
		return nil, 0, err
	}

	// 获取列表
	offset := (page - 1) * limit
	err = m.Order("created_at DESC").Limit(offset, limit).Scan(&list)

	return list, totalCount, err
}

func (s *punishmentRewardService) GetById(ctx context.Context, id int) (*model.PunishmentReward, error) {
	var item *model.PunishmentReward
	err := g.DB().Model("punishment_reward").Where("id", id).Scan(&item)
	return item, err
}

func (s *punishmentRewardService) GetRandomByCondition(ctx context.Context, prType, level int) (*model.PunishmentReward, error) {
	var items []*model.PunishmentReward
	m := g.DB().Model("punishment_reward").Where("is_active", 1)

	if prType > 0 {
		m = m.Where("type", prType)
	}
	if level > 0 {
		m = m.Where("level", level)
	}

	err := m.Scan(&items)
	if err != nil {
		return nil, err
	}

	if len(items) == 0 {
		return nil, fmt.Errorf("没有找到符合条件的奖惩内容")
	}

	// 随机选择一个
	randomIndex := rand.Intn(len(items))
	return items[randomIndex], nil
}

type IGameService interface {
	PlayGame(ctx context.Context, req *model.GameRequest) (*model.GameResult, error)
	GetGameHistory(ctx context.Context, page, limit int) (list []*model.GameRecord, total int, err error)
}

type gameService struct{}

func Game() IGameService {
	return &gameService{}
}

func (s *gameService) PlayGame(ctx context.Context, req *model.GameRequest) (*model.GameResult, error) {
	// 随机选择一个玩家
	selectedPlayer := rand.Intn(2) + 1 // 1 or 2

	// 随机选择奖惩内容
	item, err := PunishmentReward().GetRandomByCondition(ctx, req.Type, req.Level)
	if err != nil {
		return nil, err
	}

	// 生成游戏会话ID
	gameSession := guid.S()

	// 保存游戏记录 - 使用明确的字段映射避免ID冲突
	insertData := g.Map{
		"player1_name":     req.Player1Name,
		"player2_name":     req.Player2Name,
		"selected_item_id": item.Id,
		"selected_player":  selectedPlayer,
		"game_session":     gameSession,
		"created_at":       "CURRENT_TIMESTAMP",
	}

	_, err = g.DB().Model("game_record").Data(insertData).Insert()
	if err != nil {
		return nil, err
	}

	// 构建结果
	result := &model.GameResult{
		Player1Name:    req.Player1Name,
		Player2Name:    req.Player2Name,
		SelectedPlayer: selectedPlayer,
		SelectedItem:   item,
		GameSession:    gameSession,
	}

	return result, nil
}

func (s *gameService) GetGameHistory(ctx context.Context, page, limit int) (list []*model.GameRecord, total int, err error) {
	m := g.DB().Model("game_record gr").
		LeftJoin("punishment_reward pr", "gr.selected_item_id = pr.id")

	// 获取总数
	totalCount, err := m.Count()
	if err != nil {
		return nil, 0, err
	}

	// 获取列表
	offset := (page - 1) * limit
	err = m.Fields("gr.*").Order("gr.created_at DESC").Limit(offset, limit).Scan(&list)

	return list, totalCount, err
}
