@echo off
chcp 65001 >nul
echo.
echo ==========================================
echo    🎮 双人互动奖惩游戏 - 数据库备份工具
echo ==========================================
echo.

set "TIMESTAMP=%date:~0,4%%date:~5,2%%date:~8,2%_%time:~0,2%%time:~3,2%%time:~6,2%"
set "TIMESTAMP=%TIMESTAMP: =0%"

set "SOURCE_DIR=lovergame"
set "BACKUP_DIR=backups"
set "DB_FILE=lovergame.db"

echo 📅 备份时间: %date% %time%
echo 📁 源目录: %SOURCE_DIR%
echo 💾 数据库文件: %DB_FILE%
echo.

REM 创建备份目录
if not exist "%BACKUP_DIR%" (
    mkdir "%BACKUP_DIR%"
    echo ✅ 创建备份目录: %BACKUP_DIR%
)

REM 检查源目录是否存在
if not exist "%SOURCE_DIR%" (
    echo ❌ 错误: 源目录 %SOURCE_DIR% 不存在！
    pause
    exit /b 1
)

REM 检查数据库文件是否存在
if not exist "%SOURCE_DIR%\%DB_FILE%" (
    echo ❌ 错误: 数据库文件 %SOURCE_DIR%\%DB_FILE% 不存在！
    pause
    exit /b 1
)

echo 🔄 开始备份...
echo.

REM 备份整个项目
set "PROJECT_BACKUP=%BACKUP_DIR%\lovergame_full_%TIMESTAMP%.zip"
powershell -Command "Compress-Archive -Path '%SOURCE_DIR%' -DestinationPath '%PROJECT_BACKUP%' -Force"
if %errorlevel% equ 0 (
    echo ✅ 项目完整备份成功: %PROJECT_BACKUP%
) else (
    echo ❌ 项目备份失败！
)

REM 单独备份数据库文件
set "DB_BACKUP=%BACKUP_DIR%\database_%TIMESTAMP%.db"
copy "%SOURCE_DIR%\%DB_FILE%" "%DB_BACKUP%" >nul
if %errorlevel% equ 0 (
    echo ✅ 数据库备份成功: %DB_BACKUP%
) else (
    echo ❌ 数据库备份失败！
)

REM 备份配置文件
set "CONFIG_BACKUP=%BACKUP_DIR%\config_%TIMESTAMP%.zip"
if exist "%SOURCE_DIR%\config" (
    powershell -Command "Compress-Archive -Path '%SOURCE_DIR%\config' -DestinationPath '%CONFIG_BACKUP%' -Force"
    if %errorlevel% equ 0 (
        echo ✅ 配置文件备份成功: %CONFIG_BACKUP%
    )
)

REM 备份上传文件
set "UPLOAD_BACKUP=%BACKUP_DIR%\uploads_%TIMESTAMP%.zip"
if exist "%SOURCE_DIR%\upload" (
    powershell -Command "Compress-Archive -Path '%SOURCE_DIR%\upload' -DestinationPath '%UPLOAD_BACKUP%' -Force"
    if %errorlevel% equ 0 (
        echo ✅ 上传文件备份成功: %UPLOAD_BACKUP%
    )
)

echo.
echo ==========================================
echo    🎉 备份完成！
echo ==========================================
echo.
echo 📦 备份文件列表:
dir /b "%BACKUP_DIR%\*%TIMESTAMP%*"
echo.
echo 💡 提示:
echo    - 完整项目备份包含所有源码和资源
echo    - 数据库备份可单独恢复游戏数据
echo    - 建议定期备份以防数据丢失
echo.
echo 🔗 恢复说明:
echo    1. 解压项目备份文件
echo    2. 替换数据库文件 (如需要)
echo    3. 运行 go mod tidy 安装依赖
echo    4. 启动服务: go run main.go
echo.
pause
