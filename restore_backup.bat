@echo off
chcp 65001 >nul
echo.
echo ==========================================
echo    🎮 双人互动奖惩游戏 - 备份恢复工具
echo ==========================================
echo.

echo 📋 可用的备份文件:
echo.

REM 显示可用的备份文件
if exist "backups\" (
    echo 📦 完整项目备份:
    dir /b "backups\lovergame_full_*.zip" 2>nul
    echo.
    echo 💾 数据库备份:
    dir /b "backups\database_*.db" 2>nul
    echo.
) else (
    echo ❌ 没有找到备份目录！
)

if exist "lovergame_backup_*.zip" (
    echo 📦 当前目录的备份文件:
    dir /b "lovergame_backup_*.zip"
    echo.
)

echo ==========================================
echo    选择恢复选项:
echo ==========================================
echo.
echo 1. 恢复完整项目 (从 lovergame_backup_20250101.zip)
echo 2. 恢复完整项目 (从 backups 目录选择)
echo 3. 仅恢复数据库文件
echo 4. 查看备份文件详情
echo 5. 退出
echo.
set /p choice="请输入选项 (1-5): "

if "%choice%"=="1" goto restore_main_backup
if "%choice%"=="2" goto restore_from_backups
if "%choice%"=="3" goto restore_database_only
if "%choice%"=="4" goto show_backup_info
if "%choice%"=="5" goto exit
goto invalid_choice

:restore_main_backup
echo.
echo 🔄 恢复完整项目 (lovergame_backup_20250101.zip)...
echo.

if not exist "lovergame_backup_20250101.zip" (
    echo ❌ 错误: 找不到备份文件 lovergame_backup_20250101.zip
    goto end
)

REM 备份现有项目 (如果存在)
if exist "lovergame" (
    echo 📋 检测到现有项目，创建备份...
    set "TIMESTAMP=%date:~0,4%%date:~5,2%%date:~8,2%_%time:~0,2%%time:~3,2%%time:~6,2%"
    set "TIMESTAMP=%TIMESTAMP: =0%"
    ren "lovergame" "lovergame_old_%TIMESTAMP%"
    echo ✅ 现有项目已备份为: lovergame_old_%TIMESTAMP%
)

REM 解压备份文件
echo 📦 解压备份文件...
powershell -Command "Expand-Archive -Path 'lovergame_backup_20250101.zip' -DestinationPath '.' -Force"
if %errorlevel% equ 0 (
    echo ✅ 项目恢复成功！
    echo.
    echo 🚀 下一步操作:
    echo    1. cd lovergame
    echo    2. go mod tidy
    echo    3. go run main.go
    echo.
) else (
    echo ❌ 恢复失败！
)
goto end

:restore_from_backups
echo.
echo 📦 backups 目录中的完整备份:
dir /b "backups\lovergame_full_*.zip" 2>nul
echo.
set /p backup_file="请输入要恢复的备份文件名: "

if not exist "backups\%backup_file%" (
    echo ❌ 错误: 找不到备份文件 backups\%backup_file%
    goto end
)

echo 🔄 恢复项目从 %backup_file%...
powershell -Command "Expand-Archive -Path 'backups\%backup_file%' -DestinationPath '.' -Force"
if %errorlevel% equ 0 (
    echo ✅ 项目恢复成功！
) else (
    echo ❌ 恢复失败！
)
goto end

:restore_database_only
echo.
echo 💾 可用的数据库备份:
dir /b "backups\database_*.db" 2>nul
echo.
set /p db_file="请输入要恢复的数据库文件名: "

if not exist "backups\%db_file%" (
    echo ❌ 错误: 找不到数据库文件 backups\%db_file%
    goto end
)

if not exist "lovergame" (
    echo ❌ 错误: 找不到 lovergame 项目目录！
    goto end
)

echo 🔄 恢复数据库文件...
copy "backups\%db_file%" "lovergame\lovergame.db" >nul
if %errorlevel% equ 0 (
    echo ✅ 数据库恢复成功！
) else (
    echo ❌ 数据库恢复失败！
)
goto end

:show_backup_info
echo.
echo ==========================================
echo    📊 备份文件详细信息
echo ==========================================
echo.

if exist "lovergame_backup_20250101.zip" (
    echo 📦 主备份文件: lovergame_backup_20250101.zip
    for %%i in ("lovergame_backup_20250101.zip") do echo    大小: %%~zi 字节
    for %%i in ("lovergame_backup_20250101.zip") do echo    修改时间: %%~ti
    echo.
)

if exist "backups\" (
    echo 📁 backups 目录内容:
    for %%f in ("backups\*.*") do (
        echo    %%~nxf - %%~zf 字节 - %%~tf
    )
)
goto end

:invalid_choice
echo ❌ 无效选项，请重新选择！
goto end

:end
echo.
pause
goto :eof

:exit
echo 👋 退出恢复工具
exit /b 0
