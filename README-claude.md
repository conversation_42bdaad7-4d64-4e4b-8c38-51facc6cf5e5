# Claude Code 配置说明

## 配置方法

### 方法1: 使用配置文件 (推荐)
```bash
# 使用项目配置文件启动
claude --settings .claude-config.json

# 或者直接交互模式
claude --settings .claude-config.json
```

### 方法2: 使用脚本
```bash
# Linux/Mac/Git Bash
./claude-setup.sh

# Windows CMD
claude-setup.bat

# 带参数运行
./claude-setup.sh --print "你的问题"
```

### 方法3: 手动设置环境变量
```bash
# 设置环境变量
export ANTHROPIC_AUTH_TOKEN="sk-FURU174RzrjlJL1tKHAXJuM9qzTu5uPZyusBT0JfWQHRepeL"
export ANTHROPIC_BASE_URL="https://anyrouter.top"

# 启动 claude
claude
```

## 文件说明

- `.claude-config.json` - Claude 配置文件，包含API密钥和基础URL
- `claude-setup.sh` - Linux/Mac/Git Bash 启动脚本
- `claude-setup.bat` - Windows 批处理启动脚本

## 使用建议

1. **日常使用**: 推荐使用 `claude --settings .claude-config.json`
2. **快速启动**: 使用对应平台的脚本文件
3. **临时使用**: 手动设置环境变量

## 安全提示

- 不要将包含API密钥的配置文件提交到版本控制系统
- 建议将 `.claude-config.json` 添加到 `.gitignore` 文件中
