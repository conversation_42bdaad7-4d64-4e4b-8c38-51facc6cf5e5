@echo off
REM Claude Code 配置脚本 (Windows版本)
REM 用于设置环境变量和启动 claude

REM 设置环境变量
set ANTHROPIC_AUTH_TOKEN=sk-FURU174RzrjlJL1tKHAXJuM9qzTu5uPZyusBT0JfWQHRepeL
set ANTHROPIC_BASE_URL=https://anyrouter.top

echo 已设置 Claude 环境变量:
echo ANTHROPIC_AUTH_TOKEN: %ANTHROPIC_AUTH_TOKEN:~0,10%...
echo ANTHROPIC_BASE_URL: %ANTHROPIC_BASE_URL%
echo.

REM 如果提供了参数，则传递给 claude 命令
if "%~1"=="" (
    echo 启动 Claude 交互模式...
    claude
) else (
    echo 运行 Claude 命令: %*
    claude %*
)
